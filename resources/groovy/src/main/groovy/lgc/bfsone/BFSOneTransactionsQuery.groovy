package lgc.bfsone

import net.datatp.module.data.db.query.OptionFilter
import net.datatp.module.data.db.query.RangeFilter
import net.datatp.module.data.db.query.SearchFilter
import net.datatp.module.data.db.query.SqlQueryParams
import net.datatp.util.text.DateUtil
import net.datatp.util.text.StringUtil
import net.datatp.util.ds.MapObject

String BUILD_RANGER_FILTERS(List<RangeFilter> rangerFilters) {
  StringBuilder b = new StringBuilder();
  for(RangeFilter sel : rangerFilters) {
    String buildRangeFilter = BUILD_RANGER_FILTER(sel);
    if(buildRangeFilter != null) b.append(" AND " + buildRangeFilter);
  }
  return b.toString();
}

String BUILD_RANGER_FILTER(RangeFilter rangerFilter) {
  StringBuilder b = new StringBuilder();
  b.append("(");
  if(!StringUtil.isEmpty(rangerFilter.getFromValue())) {
    Date date = DateUtil.parseCompactDateTime(rangerFilter.getFromValue());
    String fromDate = DateUtil.as_yyyy_mm_dd(date);
    b.append( rangerFilter.getName() + " >= " + "'" + fromDate + "'");
  }
  if(!StringUtil.isEmpty(rangerFilter.getToValue())) {
    if(!StringUtil.isEmpty(rangerFilter.getFromValue())) b.append(" AND ");
    Date date = DateUtil.parseCompactDateTime(rangerFilter.getToValue());
    String toDate = DateUtil.as_yyyy_mm_dd(date);
    b.append( rangerFilter.getName() + " <= " + "'" + toDate + "'");
  }
  b.append(")");

  if(StringUtil.isEmpty(rangerFilter.getFromValue()) && StringUtil.isEmpty(rangerFilter.getToValue())) return null;
  return b.toString();
}

def BUILD_COLLECT_FEE_TABLE() {
  return """
      WITH CollectFee AS (
            SELECT
                ps.HAWBNO                                      AS hawb_no,
                ps.Notes                                       AS label,
                (
                  CASE
                    WHEN ps.SortDes LIKE 'B%' THEN ps.Quantity * ps.UnitPrice * ps.ExtVND
                  END
                )                                               AS buying_rate,
                (
                  CASE
                    WHEN ps.SortDes LIKE 'S%' THEN ps.Quantity * ps.UnitPrice * ps.ExtVND
                  END
                )                                               AS selling_rate,
                (
                  CASE
                    WHEN ps.SortDes LIKE 'B%' THEN 'buying_rate'
                    ELSE 'selling_rate'
                  END
                )                                               AS type
            FROM ProfitShares ps 
            WHERE ps.OBH != 1
    
            UNION ALL SELECT
                sr.HAWBNO                                       AS hawb_no,
                sr.Description                                  AS label,
                0                                               AS buying_rate,
                (sr.Quantity * sr.UnitPrice * sr.ExtVND)        AS selling_rate,
                'selling_rate'                                  AS type
            FROM SellingRate sr
    
            UNION ALL SELECT
                brwh.HAWBNO                                     AS hawb_no,
                brwh.Description                                AS label,
                (brwh.Quantity * brwh.UnitPrice * brwh.ExtVND)  AS buying_rate,
                0                                               AS selling_rate,
                'buying_rate'                                   AS type
            FROM BuyingRateWithHBL brwh
         )
    """;
}

String buildQuery() {
  SqlQueryParams params = sqlparams;
  SearchFilter search = params.getFilter("search");
  if(search == null) search = new SearchFilter();
  
  MapObject config = companyConfig;
  OptionFilter optionFilter = params.getOptionFilter("searchBy");
  String companyFilter = "";
  if(optionFilter == null || "company".equals(optionFilter.getSelectOption())) companyFilter = "AND t.TransID LIKE '%${config.getString("company-code")}%'";
  String buildRangeFilters = BUILD_RANGER_FILTERS(params.getRangeFilters());
  if(StringUtil.isNotEmpty(search.getFilterValue())) buildRangeFilters = "";
  String filterHblNo = "";
  if(params.hasParam("hblNo")) filterHblNo = "AND td.HWBNO = '${params.getString("hblNo")}'";
  String query = """
    ${BUILD_COLLECT_FEE_TABLE()}
    SELECT 
      TOP ${params.getMaxReturn()}
      t.TransID                         AS trans_id,
      t.MAWB                            AS mawb,
      t.TransDate                       AS trans_date,
      t.TpyeofService                   AS type_of_service,
      t.PortofLading                    AS port_of_lading,
      t.PortofUnlading                  AS port_of_unlading,
      t.LoadingDate                     AS loading_date,
      t.ArrivalDate                     AS arrival_date,
      t.ExpressNotes                    AS note,
      td.ETA                            AS eta,
      td.ETD                            AS etd,
      td.ShipperID                      AS shipper_id,
      sp.PartnerName                    AS shipper_short_name,
      sp.PartnerName3                   AS shipper_name,
      sp.Taxcode                        AS shipper_tax_code,
      sp.Address                        AS shipper_address,
      td.ContactID                      AS contact_id,
      td.BookingID                      AS booking_id,
      td.HWBNO                          AS hwb_no,
      td.ShipmentType                   AS shipment_type,
      h.DepartureAirportCode            AS departure_airport_code,
      h.DestinationCode                 AS destination_code,
      h.Consignee                       AS consignee,
      h.ConsigneeID                     AS consignee_id,
      h.ATTN                            AS attn,
      h.HBShipperID                     AS hb_shipper_id,
      (
        CASE
          WHEN td.Quantity IS NOT NULL THEN td.Quantity
          ELSE t.Noofpieces
        END
      )                                 AS quantity,
      (
        CASE
          WHEN td.Quantity IS NOT NULL THEN td.UnitDetail
          ELSE t.UnitPieaces
        END
      )                                 AS quantity_unit,
      (
        CASE
          WHEN td.GrosWeight IS NOT NULL THEN td.GrosWeight
          ELSE t.GrossWeight
        END
      )                                 AS gross_weight,
      (
        CASE
          WHEN td.WeightChargeable IS NOT NULL THEN td.WeightChargeable
          ELSE t.ChargeableWeight
        END
      )                                 AS chargeable_weight,
      td.CBMSea                         AS cbm_sea,
      (
        SELECT 
            SUM(
              CASE
                WHEN cf.type = 'buying_rate' THEN -cf.buying_rate
                ELSE +cf.selling_rate
              END
            ) AS profit
        FROM CollectFee cf 
        WHERE cf.hawb_no = td.HWBNO
      )                                 AS profit,
      si.IDKey                          AS container_id,
      si.Qty                            AS container_qty,
      si.Container                      AS container_type,
      si.ContainerNo                    AS container_no,
      si.SealNo                         AS seal_no,
      si.TotalPackages                  AS container_packages,
      si.UnitPack                       AS container_unit_pack,
      si.GrossWeight                    AS container_gross_weight,
      si.CBM                            AS container_cbm
    FROM 
      Transactions t
    INNER JOIN 
      TransactionDetails td ON td.TransID = t.TransID
    LEFT JOIN 
      HAWB h ON h.HWBNO = td.HWBNO
    LEFT JOIN 
      Partners sp ON td.ShipperID  = sp.PartnerID
    LEFT JOIN 
      ShippingInstruction si ON td.TransID  = si.TransID
    WHERE 1 = 1
      ${StringUtil.isNotEmpty(search.getFilterValue()) ? "" : "--"}AND t.TransID LIKE '%${search.getFilterValue()}%' ${companyFilter}
      ${filterHblNo}
      ${buildRangeFilters}
    ORDER BY 
      trans_date DESC, trans_id DESC
  """;
  println query;
  return query;
}

buildQuery()