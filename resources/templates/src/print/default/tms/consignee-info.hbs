<div style="line-height: 22px;">
    {{#if bill.tmsBillForwarderTransport.isImport}}
        {{#each this.addressMap}}
            <div style="margin-top: 15px;">
                <span class="text-bold">TÊN CÔNG TY/ COMPANY NAME: </span>{{this.0.customer.customerFullName}}
            </div>
            <div>
                 <div style="display: inline-block">-</div>
                 <div style="display: inline-block;margin-left:5px;">Địa chỉ/ Address: <span style="white-space: pre-line;">{{this.0.receiver.receiverAddress}}</span></div>
            </div>
            <div style="display:flex;align-items: center;white-space: nowrap;">
                <div>-</div>
                <div style="margin-left:5px;"><PERSON><PERSON><PERSON> hệ/ Contact: {{this.0.receiver.receiverFullName}} {{this.0.receiver.receiverMobile}}</div>
            </div>
            {{#each this as | bill|}}
                {{#each bill.stopLocations as | stopLocation|}}
                   <span style="white-space: pre-wrap;">Điểm trung gian {{math 'inc' @index}}: {{stopLocation.address}}</span>
                {{/each}}
            {{/each}}
            {{> companydb:print:tms/commodity-info.hbs bills=this bill=this.0}}
        {{/each}}
    {{else}}
        {{#if bill.tmsBillForwarderTransport.isExport}}
            {{#each this.addressMap}}
                <div style="margin-top: 15px;">
                    <span class="text-bold">TÊN CÔNG TY/ COMPANY NAME: </span>{{this.0.customer.customerFullName}}
                </div>
                <div style="display:flex;">
                    <div>-</div>
                    <div style="margin-left:5px;">Địa chỉ/ Address: <span style="white-space: pre-line;">{{this.0.sender.senderAddress}}</span></div>
                </div>
                <div style="display:flex;white-space: nowrap;">
                    <div>-</div>
                    <div style="margin-left:5px;">Liên hệ/ Contact: {{this.0.sender.senderFullName}} {{this.0.sender.senderMobile}} </div>
                </div>
                {{#each this as | bill|}}
                    {{#each bill.stopLocations as | stopLocation|}}
                        <span style="white-space: pre-wrap;">Điểm trung gian {{math 'inc' @index}}: {{stopLocation.address}}</span>
                    {{/each}}
                {{/each}}
                {{> companydb:print:tms/commodity-info.hbs bills=this bill=this.0}}
            {{/each}}
        {{else}}
            <div>
                <div style="margin-top: 15px;">
                    <span class="text-bold">TÊN CÔNG TY/ COMPANY NAME: </span>{{bill.customer.customerFullName}}
                </div>
                <div class="text-bold">Điểm Lấy Hàng/ Pickup Location</div>
                <div style="display:flex;">
                    <div>-</div>
                    <div style="margin-left:5px;">Địa chỉ/ Address: <span style="white-space: pre-line;">{{bill.sender.senderAddress}}</span></div>
                </div>
                <div style="display:flex;white-space: nowrap;">
                    <div>-</div>
                    <div style="margin-left:5px;">Liên hệ/ Contact: {{bill.sender.senderFullName}} {{bill.sender.senderMobile}} </div>
                </div>
            </div>
            <div>
                <span class="text-bold">Điểm Giao Hàng/ Delivery Location</span>
                <div style="display:flex;">
                    <div>-</div>
                    <div style="margin-left:5px;">Địa chỉ/ Address: <span style="white-space: pre-line;">{{bill.receiver.receiverAddress}}</span></div>
                </div>
                <div style="display:flex;white-space: nowrap;">
                    <div>-</div>
                    <div style="margin-left:5px;">Liên hệ/ Contact: {{bill.receiver.receiverFullName}} {{bill.receiver.receiverMobile}}</div>
                </div>
            </div>
            {{#each bill.stopLocations as | stopLocation|}}
                <span style="white-space: pre-wrap;">Điểm trung gian {{math 'inc' @index}}: {{stopLocation.address}}</span>
            {{/each}}
            {{> companydb:print:tms/commodity-info.hbs bills=bills bill=bills.0}}
        {{/if}}
    {{/if}}
</div>