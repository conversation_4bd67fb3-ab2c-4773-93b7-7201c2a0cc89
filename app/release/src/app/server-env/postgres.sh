#!/usr/bin/env bash

source ./common.sh
source ./env.sh

function dump_db() {
  echo "args = $@"
  dbUser=$(get_opt --db-user '' $@)
  dbName=$(get_opt --db-name '' $@)
  format=$(get_opt --format 'c' $@)
  backup_file=$(get_opt --file '' $@)
  if [ "$backup_file" = "" ]; then
    mkdir -p dbbackup
    backup_file="dbbackup/$dbName-latest.dump"
  fi
  PGPASSWORD=$dbUser pg_dump -v --blobs -h $DB_HOST -U $dbUser -F$format -f $backup_file $dbName
}

function dump_db_tables() {
  echo "args = $@"
  format=$(get_opt --format 'c' $@)
  backup_file=$(get_opt --file '' $@)

  table_args=()
  for arg in "$@"; do
    if [[ "$arg" == --table=* ]]; then
      table_name="${arg#--table=}"
      table_args+=("-t" "$table_name")
    elif [[ "$arg" == -t=* ]]; then
      table_name="${arg#-t=}"
      table_args+=("-t" "$table_name")
    fi
  done

  if [ "${#table_args[@]}" -eq 0 ]; then
    echo "Require table. Use --table=table_name hoặc -t=table_name"
    return 1
  fi

  if [ "$backup_file" = "" ]; then
    backup_file="dbbackup/${DB_NAME}-partial.dump"
  fi

  mkdir -p dbbackup
  echo "Table dump: ${table_args[*]}"
  echo "File: $backup_file"

  PGPASSWORD=$DB_USER pg_dump -v --blobs -h $DB_HOST -U $DB_USER -F$format -f $backup_file "${table_args[@]}" $DB_NAME
}

function restore_db() {
  dbUser=$(get_opt --db-user '' $@)
  dbName=$(get_opt --db-name '' $@)
  jobs=$(get_opt --jobs '' $@)

  JOB_OPTIONS="-j $jobs"
  if [ "$jobs" = "" ]; then
    JOB_OPTIONS=""
  fi

  backup_file=$(get_opt --file '' $@)
  if [ "$backup_file" = "" ]; then
    backup_file="dbbackup/$dbName.dump"
  fi
  #echo "Drop And Recreate DB $DB_NAME"
  echo "Start Restoring DB $dbName"
  PGPASSWORD=$dbUser pg_restore \
    --verbose --exit-on-error --no-owner $JOB_OPTIONS \
    --host=$DB_HOST --username=$dbUser --dbname=$dbName "$backup_file"
  echo "Restored DB $DB_NAME!!!"
}

PG_CMD=$1
shift

if [ "$PG_CMD" = "psql" ] ; then
  echo "PGPASSWORD=$DB_USER psql -h $DB_HOST -p $DB_PORT -U $DB_USER $DB_NAME"
  PGPASSWORD=$DB_USER psql -h $DB_HOST -p $DB_PORT -U $DB_USER $DB_NAME
elif [ "$PG_CMD" = "dump" ] ; then
  time dump_db --format=c --db-user=$DB_USER --db-name=datatpdb
  time dump_db --format=c --db-user=document_ie --db-name=document_ie_db
elif [ "$PG_CMD" = "dump:datatpdb" ] ; then
  time dump_db --format=c --db-user=$DB_USER --db-name=datatpdb $@
elif [ "$PG_CMD" = "dump:document_ie_db" ] ; then
  time dump_db --format=c --db-user=document_ie --db-name=document_ie_db $@
elif [ "$PG_CMD" = "restore:datatpdb" ] ; then
  time restore_db --db-user=$DB_USER --db-name=$DB_NAME $@
elif [ "$PG_CMD" = "restore:document_ie_db" ] ; then
  time restore_db --db-user=document_ie --db-name=document_ie_db $@
elif [ "$PG_CMD" = "dump-tables" ]; then
  time dump_db_tables $@
else
  echo 'Usage: '
  echo "  ./postgres.sh psql        Run psql client with $PG_ADMIN_USER and $DB_NAME"
  echo ""
  echo "  ./postgres.sh dump        Dump data from db $DB_NAME to  dbbackup/$DB_NAME.tar"
  echo "              [--file=path/name]     Optional file name instead default dbbackup/$DB_NAME.tar"
  echo ""
  echo "  ./postgres.sh restore     Restore data from dbbackup/$DB_NAME.tar to db $DB_NAME"
  echo "              [--file=path/name]     Optional file name instead default dbbackup/$DB_NAME.tar"
  echo ""
fi