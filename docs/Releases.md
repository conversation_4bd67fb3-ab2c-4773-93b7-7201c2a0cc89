# Changelog

All notable changes to this project will be documented in this file.

### [R20250702]

1. Cập nhật nhánh tms:
  - Fix bugs TMSBillList
  - X<PERSON> lý save màn TMSHouseBillEditor
  - <PERSON><PERSON> lý Clone TMSHouseBill
  - Kéo HouseBill từ BFSOne về hệ thống TMSHouseBill

2. Cập nhật nhánh crm:
  - [Dan] - clean code datatp-logistics: module price/ sales/ management (module cũ đã tách thành datatp-crm)
  - [Dan] - Chỉnh báo cáo Key Account Performance cho teame BD - Mrs Hải y/c
  - [Dan] - Sửa lỗi Copy Quotation.
  - [Dan] - Báo cáo theo dõi sales, highlight các sales trực quan các sales không có activity.
  - [Dan] - Sync khách hàng cho tất cả employee bee corp/ beehcm/ bee dad.
  - [Dan] - <PERSON><PERSON><PERSON> nhậ<PERSON>, kiểm tra các script db, backup-tools.
      Document tại link https://gitlab.datatp.net/tuan/datatp-docs/-/blob/master/docs/shared/developer/SETUP.md

  - [Nhat] - CRM: Tracking số lần Saleman Export Quotation, hiển thị lên CRM Dashboard: Salesman Activity Tracker.
  - [Nhat] - Spreadsheet: Cập nhật màn hình Report team IST (BD HCM). Bổ sung thêm các biểu đồ báo cáo volume cho từng cá nhân:
    - TEU (1x20DC = 1 TEU, 1x40HC/1x45HC = 2 TEUs)
    - CBM (hàng LCL)
    - KG (hàng Air)
    - Shipment (Breakbulk).

  - [An] - Export dữ liệu Sales Daily Tasks theo excel Mrs Minh y/c.
  - [An] - Hiển thị chi tiết dữ liệu cho Salesman Activity Tracker
  - [An] - Fix bugs, enhance UI Bulk Cargo Inquiry Request - Mrs Cường BD yêu cầu.
  - [An] - Chỉnh form tạo mới Agent Approach.
  - [An] - Trên màn hình Dashboard, thêm các chức năng để cho phép view dữ liệu, mở rộng detail các chỉ số trên dashboard.

Có thể download db mới tại đường dẫn:
- https://beelogistics.cloud/download/datatpdb-latest.dump
- https://beelogistics.cloud/download/document_ie_db-latest.dump

hoặc chạy run update và các migrate script:

### [R20250702]

1. Cập nhật nhánh tms:

    [Chiến]
    - Làm tiếp phần TMSHouseBill, đồng bộ dữ liệu 2 TMSHouseBill và TMSBill khi tạo mới và update.
    - Update template export data thầu phụ và đối soát cước thầu phụ.

    [Quân]
    - Phân quyền TMSVendorBill cho thầu phụ và test.
    - Clean code TMS.

2. Cập nhật nhánh crm:
  - [Nhat] Thêm 2 option cho button Feedback ở màn hình Sea FCL/LCL Price
      + Popup Form Request Price
      + Popup Form Feedback
  - [Nhat] Migrate dữ liệu forwarder_customer_leads: cập nhật company_id, saleman_label
        + script: migrate:run --script crm/AlterTables.groovy
  - [An] Export dữ liệu Sales Daily Tasks theo excel Mrs Minh y/c
  - [An] Cập nhật theo feedback chỉnh sửa cho inquiry hàng rời
  - [An] Sync lại toàn bộ khách hàng cho sales HPH/ HAN
  - [An] Hiển thị chi tiết dữ liệu cho Salesman Activity Tracker
  - [Dan] Enhance lọc, báo cáo volume theo tuyến cho team pricing.
  - [Dan] Enhance UI Sale Dashboard, UI Pricing Dashboard.

Có thể download db mới tại đường dẫn:
- https://beelogistics.cloud/download/datatpdb-latest.dump
- https://beelogistics.cloud/download/document_ie_db-latest.dump

hoặc chạy run update và các migrate script:
- migrate:run --script crm/AlterTables.groovy
- server:migrate:run --script crm/SyncBFSOneBySaleman.groovy --company beehph
- server:migrate:run --script crm/SyncBFSOneBySaleman.groovy --company beehan

### [R20250701]

1. Cập nhật nhánh tms:

    Task [Chiến] :
    - Cập nhật lại template BFSOne theo form mới
    - Check bản kê thầu phụ khi import cost vào TMSBill, không  khớp số cont, biển số xe note lại lỗi để cus kiểm

2. Cập nhật nhánh crm:
- Fix báo cáo Key Account Report.
- Enhance lại Pricing Dashboard, thêm báo cáo volume theo từng tuyến.
- Thêm báo cáo theo dõi khách hàng, chỉ số chuyển đổi khách hàng cho Sale Dashboard.
- Fix, sửa lỗi tạo Partner, api với BFSOne.

Có thể download db mới tại đường dẫn:
- https://beelogistics.cloud/download/datatpdb-latest.dump
- https://beelogistics.cloud/download/document_ie_db-latest.dump

hoặc chạy run update và các migrate script:
- migrate:run --script crm/AlterTables.groovy
- server:migrate:run --script crm/UpdateDataKeyAccountReport.groovy --company bee
- server:migrate:run --script crm/UpdateDataKeyAccountReport.groovy --company beehph
- server:migrate:run --script crm/UpdateDataKeyAccountReport.groovy --company beehcm

### [R20250626]

Cập nhật nhánh tms:

Task:
[Quân]: clean code TMSPartnerVendor và các code không sử dụng đến trên TMS

[Chiến] :
- Fix bugs và điều chỉnh tiếp template export BFSOne và template TMSBill  gửi cho thầu phụ
- Cập nhật giao diện TMSHouseBill

Cập nhật nhánh crm:
- [Nhat] - Fix bugs create partner, enhance màn hình CRM Sale Dashboard.
- [An] - Enhance báo cáo Saleman Activities Tracker - Mrs. Minh Sales HPH
         - Cố định row header và cột đầu tiên.
         - Kiểm tra lại chỉ số Khách hàng mới/ lead mới.

Cập nhật nhánh document-ie:
- Em bổ sung trích rút biên lai ạ

Có thể download db mới tại đường dẫn https://beelogistics.cloud/download/datatpdb-latest.dump
hoặc chạy run update và các migrate script
- server:migrate:run --script document/MigrationDocument.groovy
- server:migrate:run --script crm/SyncBFSOneAuthorizePartner.groovy


### [R20250621]

Cập nhật nhánh tms:
[Quân] :
 - Cập nhật dữ liệu kho cảng từ file excell (Dương điều chỉnh)
- Update kho cảng tms bill theo địa chỉ mới

[Chiến]:
Update template excell Cho BFSOne:

-Xác minh thông tin bill đủ điều kiện thanh toán:[TMSBillFee] Thêm trường verifyPaymentInfo(true/fasle), verifyPaymentNote(note lại các thông tin thiếu)
-Xác minh thông tin xe:[TMSBill] Thêm verifyVehicleInfo, verifyVehicleNote
-TMSBillList show các cột verify trên
-Fix hàm searchInvoiceSummaryReconciles sau khi tách DB Documnet-set

Cập nhật nhánh hr:
Task Cập nhật UI + Query Sync dữ liệu OKR tự động
- Bổ sung service type: Crossborder
- Bổ sung đếm số lượng tờ khai hải quan

Cập nhật nhánh crm:

1. [An] - Company Pricing Dashboard:
- chức năng lọc theo Type Of Service cho toàn bộ dữ liệu, cho Top Route Performance.
- export excel từng section/ bảng.

2. [An] - Cập nhật thông tin các trường industry_sector (từ excel), date_created, date_modified (từ BFSOne) cho partner.


Có thể download db mới tại đường dẫn https://beelogistics.cloud/download/datatpdb-latest.dump
hoặc chạy run update và các migrate script
- server:migrate:run --script tms/MigrateAddressPortWarehouse.groovy
- server:migrate:run --script crm/UpdateIndustryBFSOnePartner.groovy
- server:migrate:run --script crm/UpdateDataKeyAccountReport.groovy --company bee


### [R20250621]

Cập nhật nhánh develop

Cập nhật dự án datatp-crm:
- Checkout code (nhánh develop) và setup:  git@gitlab:datatp.net:tuan/datatp-crm.git
- Remove node_modules pnpm-lock.yaml dist và pnpm install && pnpm run build ở các dự án lib, erp, document-ie, logistics, crm, phoenix.
- Build java code bằng command: ./datatp.sh lgc:build -clean -build
- Run code như bình thường.

Có thể download db mới tại đường dẫn https://beelogistics.cloud/download/datatpdb-latest.dump

### [R20250621]

Cập nhật nhánh document-ie:
- Em clear bảng nằm trên database cũ và fix bug bên doument set ạ

Cập nhật nhánh tms:
[Quân] :
1)Cho phép cus tự lên thông tin xe thay điều vận:
- Giao diện TMSBillList hiển thị popup các tab VehicleTrip Form cho phép nhập thông tin lô hàng
2)Tự động ghép chuyến cho các lô hàng theo request của cus:
- Giao diện: Thêm 1 checkbox Combine Trip tại popup Processing Goods Request
- Backend: Tạo VehicleTrip chung cho các VehicleTripGoodsTracking được tạo từ các TMSBill request.
3)Export template xuất thông tin xe cho BFSOne theo feedback của người dùng.
Thêm note cảnh báo các lô chưa đủ điều kiện thanh toán(Sai hbl, chưa nhập giá, sai đơn vị)

Chiến:
+ Dựng Entity TMSHouseBill: Số Hbl, Loại Hình(Nhập/Xuất), Customer, Kho bãi lấy/trả hàng, Carrier, COT/ETA,...
+ Thêm các hàm logic sử lý CRUD. Tối ưu hàm search khi join bảng tms-bill
+ Migration TMS HouseBill data, tạo liên kết đến TMSBill tương ứng
+ Dựng màn hình cho TMSHouseBillList và TMSHouseBillEditor

Có thể download db mới tại đường dẫn https://beelogistics.cloud/download/datatpdb-latest.dump
hoặc chạy run update và các migrate script
- migrate:run --script document/CleanDocument.groovy
- server:migrate:run --script tms/MigrationTMSHouseBill.groovy

### [R20250620]

Cập nhật nhánh document-ie:
Em fix bug search document set ạ

Có thể download db mới tại đường dẫn https://beelogistics.cloud/download/datatpdb-latest.dump

### [*********]

Cập nhật nhánh crm:
[Dan] - Implement UI Dashboard Salesman Activity Tracker.

Cập nhật dự án document-ie:
- Checkout code (nhánh develop):  git@gitlab:datatp.cloud:tuan/datatp-document-ie.git
- Tạo db, user và restore theo link db tương ứng ở dưới. (Sửa thông tin ở file env.sh, chạy lại các lệnh ở file postgres-admin.sh)
db_name: document_ie_db
username: document_ie
password: document_ie

Xem lại cấu hình các file config application.properties, bổ sung thêm datasource cho db mới.
document-ie:
type: com.zaxxer.hikari.HikariDataSource
connectionTimeout: 30000
idleTimeout: 600000
maxLifetime: 600000
minimumIdle: 5
maximumPoolSize: 15
auto-commit: false
driverClassName: org.postgresql.DriverjdbcUrl: ***********************************************:{spring.datasource.server.port}/document_ie_db
username: document_ie
password: document_ie

Có thể download db mới tại đường dẫn https://beelogistics.cloud/download/datatpdb-latest.dump
Có thể download db document_ie_db mới tại đường dẫn https://beelogistics.cloud/download/document_ie_db-latest.dump

### [*********]

Cập nhật nhánh crm:
- [Dan] - Implement UI Dashboard cho Pricing Company.

[Nhat]
- Cập nhật phân quyền màn hình UI Customer Lead, Customer Lead Detail
- Check Customer theo Tax Code: Bổ sung Button Check, Check thêm Customer Lead
- Thêm button tạo Shipping Instruction từ Partner Obligation, search Partner Obligation theo Cus/ Docs

Có thể download db mới tại đường dẫn https://beelogistics.cloud/download/datatpdb-latest.dump

### [*********]

Cập nhật nhánh quan-tms:
- Thêm nút clear trạng thái thanh toán với quyền moderator tại màn hình TMSBillList
- Điều chỉnh hiển thị các điểm dừng tại màn TMSBillList, VehicleTripGoodTrackingList theo yêu cầu

Có thể download db mới tại đường dẫn https://beelogistics.cloud/download/datatpdb-latest.dump

### [R20250616]

Cập nhật nhánh crm:
- [An] - Tiếp tục nhận feedback chỉnh sửa, bugs cho inquiry hàng rời (Req from Team BD)
- [An] -Refactor lại thông tin BFSOne Partner, Viết cron sync tự động theo ngày
- [An] - "Cập nhật bảng giá cont (Trucking): Chia lại mức giá cho cont 20' theo y/c của HPH.

- [Nhat] - Clean code Uncompleted Sale Daily Task, thay thế logic sendMessage cũ bằng CRMMessageSystem
- [Nhat] - Cập nhật phân quyền màn hình UI Customer Lead, Customer Lead Detail

- [Dan] - Implement UI Dashboard cho CRM Company.
- [Dan] - Viết api gen token, api cập nhật BFSOne Partner Code cho a Quý.
   Quy trình lúc tạo mới partner -> KT approve -> BFSOne gen code partner -> Gọi api để sync lại với DataTP
   Đã gửi lại api cho a Quý lúc  13/06
   https://docs.google.com/document/d/1hI71aD9YjN2zbHxVUAVEc_Sp0lgYsOJwgHvZv1x1zsA/edit?usp=sharing"
- [Dan] - Tạo bảng dữ liệu, lưu thông tin khách hàng
   Tạo các api service cho phép cập nhật, chỉnh sửa và xoá record.
- [Dan] - "Cập nhật api liên quan đến authorization, cho phép các hệ thống bên ngoài gọi vào các service để cập nhật dữ liệu.
   Hard code token, gửi cho client sau đó kiểm tra request để xác thực."
- [Dan] - Tạo bảng dữ liệu, lưu thông tin unit
   Tạo các api service cho phép sync, cập nhật, chỉnh sửa và xoá record.

Cập nhật nhánh quan-tms:
- Giao diện TMSBillList, VehicleTripGoodTrackingList cho phép nhập thêm các điểm dừng
- Hàm search TMSBillList, VehicleTripGoodTrackingList load thêm các điểm dừng

Có thể download db mới tại đường dẫn https://beelogistics.cloud/download/datatpdb-latest.dump
hoặc chạy run update và các migrate script

### [R20250612]

Cập nhật nhánh crm:
- Refactor lại thông tin BFSOne Partner
- Tiếp tục nhận feedback chỉnh sửa, bugs cho inquiry hàng rời (Req from Team BD)

Cập nhật nhánh tms:
- [Quân] Drop bảng document_document_set_category, drop column document_category_id tại bảng document_document_set,  drop các index đặt tên sai tại bảng document_document
- [Chiến] Fix bugs TMSBillFee save lỗi, Clear thông tin xe và giá cost khi copy lô hàng

Có thể download db mới tại đường dẫn https://beelogistics.cloud/download/datatpdb-latest.dump
hoặc chạy run update và các migrate script

- migrate:run --script document/CleanDocument.groovy

### [R20250612]

Cập nhật nhánh crm:
- Refactor lại thông tin BFSOne Partner
- Tiếp tục nhận feedback chỉnh sửa, bugs cho inquiry hàng rời (Req from Team BD)

Cập nhật nhánh tms:
- Địa chỉ config cho khách hàng thêm StreetName để lưu số nhà, đường,...(Kế toán yêu cầu tách)
 - Thêm senderStreetName, receiverStreetName trên TMSBill và đồng bộ với địa chỉ khách hàng đã config
- Fix BFSOne template export, chuẩn hóa địa chỉ  xã/phường, quận/huyên theo yêu cầu
- Fix lỗi query liên quan đến migration TMSVendor sang Vehicle Fleet (Quân chưa thay hết)

Có thể download db mới tại đường dẫn https://beelogistics.cloud/download/datatpdb-latest.dump

### [R20250612]

Cập nhật nhánh crm:
- Clean code groovy sql không sử dụng từ datatp-build/app/cli
- Drop các colume thừa, tạo nhầm, không còn sử dụng ở các bảng bfsone_partner, lgc_price_bulk_cargo_inquiry_request, lgc_price_truck_container_charge

Cập nhật nhánh nhat:
- Chỉnh sửa giao diện Asset Calendar+ fix bug màn hình tạo Task
- Bổ sung màn hình Admin KPI

Cập nhật nhánh tms:
Task:
Chiến:
- Push cước từ Vehicle Goods Tracking về phần Chi phí TMSBill
- Cho phép cus đồng bộ cước khi Vehicle Goods Tracking đã được nhập cước
- Thêm trạng thái thông báo thanh toán chi phí TMSBill và các lỗi dẫn đến chưa đc thanh toán
- UITMSBillList lọc các bill chưa thanh toán
- Verify HouseBill tmsBill với BFSOne, cảnh báo các lô hàng HouseBill chưa Verify

Quân:
- [Vehicle Fleet] Thêm field emails và các field webhook config
- Viết groovy script merge tms vendor vào vehicle fleet và migration dữ liệu các entity dùng TMSVendor sang Vehicle Fleets
- Thay thế trên các giao diện màn hình dùng BBRefTMSVendor sang BBRefVehicleFleet.
- Chuyển và kiểm tra các chức năng call webhook được cấu hình từ TMS Partner sang Vehicle Fleet

Có thể download db mới tại đường dẫn https://beelogistics.cloud/download/datatpdb-latest.dump
hoặc chạy run update và các migrate script
- migrate:run --script crm/AlterTables.groovy
- migrate:run --script tms/MigrationTmsBillFee.groovy
- server:migrate:run --script tms/MigrationVehicleFleet.groovy

### [R20250611]

Cập nhật nhánh crm:
- Sửa logo báo cáo

Cập nhật nhánh asset:
Task:
- [Asset] Thêm giao diện Calendar riêng cho book xe + phòng họp (trong module Asset). Default xem ở dạng week
- [Spreadsheet] Tạo bảng config màn hình Report team IST (BD HCM)

Cập nhật nhánh ocr:
Em fix bug trích rút bên kế toán ạ

Có thể download db mới tại đường dẫn https://beelogistics.cloud/download/datatpdb-latest.dump
hoặc chạy run update và các migrate script

### [R20250610]

Cập nhật nhánh crm:

- Cập nhật form, feedback chỉnh sửa, bugs cho inquiry hàng rời (Team BD)
- Enhance các báo cáo ở màn hình CRM. (Lọc, filter, xuất excel, ..)
   Báo cáo hoạt động khách hàng/ lead theo dõi gần đây
- Fix bugs lỗi spam mail nhắc cập nhật request
- Cập nhật response MSA: String => MapObject

Cập nhật nhánh tms:

- Fix bugs liên quan đến GPS
- Fix bugs TMS liên quan đến TMSPartner và TMSBillFee

Có thể download db mới tại đường dẫn https://beelogistics.cloud/download/datatpdb-latest.dump
hoặc chạy run update và các migrate script

# Cập nhật server , code nhánh develop và download db

Cập nhật nhánh ocr:
- Bổ sung trích rút tên người bán người mua bên python và thêm vào bên giao diện
- cập nhật trích rút bên dat-ocr, code datatp-python

Cập nhật nhánh maintenance
- Remove react-beautiful-dnd thư viện
- Cập nhật kanban sử dụng dndkit lib

Có thể download db mới tại đường dẫn https://beelogistics.cloud/download/datatpdb-latest.dump
hoặc chạy run update và các migrate script

Nếu không cập nhật db, chạy script:
- migrate:run --script tms/MigrationTmsPartnerAddess.groovy
