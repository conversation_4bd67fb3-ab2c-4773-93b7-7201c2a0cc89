# Changelog

All notable changes to this project will be documented in this file.

### [Unreleased]
1. Tasks:
  - [Dan] - Task 1.
  - [An] - Task 2.
  - [Nhat] - Task 3.

2. Migrate script:
  - [Dan] - Script 1.
  - [An] - Script 2.
  - [Nhat] - Script 3.

### [R20250702]

1. Tasks:
  - [Dan] - clean code datatp-logistics: module price/ sales/ management (module cũ đã tách thành datatp-crm)
  - [Dan] - Chỉnh báo cáo Key Account Performance cho teame BD - Mrs Hải y/c
  - [Dan] - Sửa lỗi Copy Quotation.
  - [Dan] - Báo cáo theo dõi sales, highlight các sales trực quan các sales không có activity.
  - [Dan] - Sync khách hàng cho tất cả employee bee corp/ beehcm/ bee dad.
  - [Dan] - Cập nhật, kiểm tra các script db, backup-tools.
      Document tại link https://gitlab.datatp.net/tuan/datatp-docs/-/blob/master/docs/shared/developer/SETUP.md

  - [Nhat] - CRM: Tracking số lần <PERSON>an Export Quotation, hiển thị lên CRM Dashboard: Salesman Activity Tracker.
  - [Nhat] - Spreadsheet: Cập nhật màn hình Report team IST (BD HCM). Bổ sung thêm các biểu đồ báo cáo volume cho từng cá nhân:
    - TEU (1x20DC = 1 TEU, 1x40HC/1x45HC = 2 TEUs)
    - CBM (hàng LCL)
    - KG (hàng Air)
    - Shipment (Breakbulk).

  - [An] - Export dữ liệu Sales Daily Tasks theo excel Mrs Minh y/c.
  - [An] - Hiển thị chi tiết dữ liệu cho Salesman Activity Tracker
  - [An] - Fix bugs, enhance UI Bulk Cargo Inquiry Request - Mrs Cường BD yêu cầu.
  - [An] - Chỉnh form tạo mới Agent Approach.
  - [An] - Trên màn hình Dashboard, thêm các chức năng để cho phép view dữ liệu, mở rộng detail các chỉ số trên dashboard.

2. Migrate script:

### [R20250702]

1. Tasks:
  - [Nhat] Thêm 2 option cho button Feedback ở màn hình Sea FCL/LCL Price
      + Popup Form Request Price
      + Popup Form Feedback
  - [Nhat] Migrate dữ liệu forwarder_customer_leads: cập nhật company_id, saleman_label
        + script: migrate:run --script crm/AlterTables.groovy
  - [An] Export dữ liệu Sales Daily Tasks theo excel Mrs Minh y/c
  - [An] Cập nhật theo feedback chỉnh sửa cho inquiry hàng rời
  - [An] Sync lại toàn bộ khách hàng cho sales HPH/ HAN
  - [An] Hiển thị chi tiết dữ liệu cho Salesman Activity Tracker
  - [Dan] Enhance lọc, báo cáo volume theo tuyến cho team pricing.
  - [Dan] Enhance UI Sale Dashboard, UI Pricing Dashboard.

2. Migrate script:
- migrate:run --script crm/AlterTables.groovy
- server:migrate:run --script crm/SyncBFSOneBySaleman.groovy --company beehph
- server:migrate:run --script crm/SyncBFSOneBySaleman.groovy --company beehan

### [R20250701]
- Fix báo cáo Key Account Report.
- Enhance lại Pricing Dashboard, thêm báo cáo volume theo từng tuyến.
- Thêm báo cáo theo dõi khách hàng, chỉ số chuyển đổi khách hàng cho Sale Dashboard.
- Fix, sửa lỗi tạo Partner, api với BFSOne.

### [R20250626]
- [Nhat] - Fix bugs create partner, enhance màn hình CRM Sale Dashboard.
- [An] - Enhance báo cáo Saleman Activities Tracker - Mrs. Minh Sales HPH
         - Cố định row header và cột đầu tiên.
         - Kiểm tra lại chỉ số Khách hàng mới/ lead mới.

### [R20250621]

Cập nhật nhánh hr:
Task Cập nhật UI + Query Sync dữ liệu OKR tự động
- Bổ sung service type: Crossborder
- Bổ sung đếm số lượng tờ khai hải quan

1. [An] - Company Pricing Dashboard:
- chức năng lọc theo Type Of Service cho toàn bộ dữ liệu, cho Top Route Performance.
- export excel từng section/ bảng.

2. [An] - Cập nhật thông tin các trường industry_sector (từ excel), date_created, date_modified (từ BFSOne) cho partner.

### [R20250618]

[Dan] - Implement UI Dashboard Salesman Activity Tracker.

