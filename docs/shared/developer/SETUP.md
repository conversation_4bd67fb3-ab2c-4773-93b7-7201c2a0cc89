---
sidebar_position: 1
hide_table_of_contents: true
displayed_sidebar: developerSidebar
---

# Setup DataTP Project

## 📋 Y<PERSON><PERSON> cầu công cụ và cấu hình

### 🛠️ Công cụ chính
| <PERSON><PERSON><PERSON> c<PERSON> | <PERSON><PERSON><PERSON> bản | <PERSON><PERSON> tả |
|---------|-----------|-------|
| Node.js | > 23 | JavaScript runtime |
| pnpm | <PERSON><PERSON><PERSON> nhất | Package manager |
| Git | <PERSON>ới nhất | Version control |
| Java | 21 | Java runtime |
| Gradle | > 8.7 | Build tool |
| VS Code/Eclipse | Mới nhất | IDE |
| Postgres | > 16 | Database server |
| DBeaver | M<PERSON><PERSON> nhất | Database explorer |

### 🧰 Công cụ bổ sung
- Python (phiên bản 3.10 trở lên)
- Docker
- K3s

## ⚙️ Cấu hình

### 1. Git
```bash
# C<PERSON><PERSON> hình thông tin người dùng
git config --global user.email "<EMAIL>"
git config --global user.name "Your Name"

# C<PERSON><PERSON> hình file mode
git config --global core.filemode false

# Cấu hình line ending với unix style
git config --global core.autocrlf false
```

### 2. SSH Key
```bash
# Tạo SSH key mới
ssh-keygen -t ed25519 -b 4096 -C "<EMAIL>"

# Khởi động SSH agent
eval "$(ssh-agent -s)"

# Thêm SSH key vào SSH agent
ssh-add ~/.ssh/id_ed25519
```

### 3. Thêm SSH key vào tài khoản GitLab
1. Sao chép nội dung SSH key
   ```bash
   cat ~/.ssh/id_ed25519.pub | pbcopy
   ```
2. Đăng nhập vào GitLab
3. Vào **Settings > SSH Keys**
4. Dán SSH key và đặt tên cho key

## 🚀 Cài đặt dự án

### 1. Tạo thư mục root và clone dự án
```bash
# Tạo thư mục root
mkdir datatp
cd datatp

# Clone các dự án
git clone git@gitlab:datatp.net:tuan/datatp-core.git
git clone git@gitlab:datatp.net:tuan/datatp-erp.git
git clone git@gitlab:datatp.net:tuan/datatp-document-ie.git
git clone git@gitlab:datatp.net:tuan/datatp-logistics.git
git clone git@gitlab:datatp.net:tuan/datatp-crm.git
git clone git@gitlab:datatp.net:tuan/datatp-build.git
```

#### 📂 Cấu trúc dự án

Sau khi clone, cấu trúc thư mục của dự án sẽ như sau:

```
datatp/
├── datatp-core/               # Core services và utilities
│   ├── app/                   # Core application
│   ├── service/               # Core services
│   └── webui/                 # Core UI components
│
├── datatp-erp/                # Enterprise Resource Planning
│   ├── app/                   # ERP application
│   ├── module/                # ERP modules
│   └── webui/                 # ERP UI components
│       ├── lib/               # Shared UI library
│       └── erp/               # ERP specific UI
│
├── datatp-document-ie/        # Document Information Extractor
│   ├── app/                   # Document IE application
│   └── webui/                 # Document IE UI
│
├── datatp-logistics/          # Logistics management
│   ├── app/                   # Logistics application
│   ├── module/                # Logistics modules
│   └── webui/                 # Logistics UI
│
├── datatp-crm/                # Customer Relationship Management
│   ├── app/                   # CRM application
│   └── webui/                 # CRM UI
│
└── datatp-build/              # Build tools and configurations
    ├── scripts/               # Build scripts
    └── config/                # Build configurations
```

#### 🔄 Quan hệ giữa các dự án

```
                                ┌─────────────────┐
                                │   datatp-build  │
                                └─────────┬───────┘
                                          ▼
          ┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
          │document-ie      │     │  datatp-crm     │     │datatp-logistics │
          └─────────┬───────┘     └─────────┬───────┘     └─────────┬───────┘
                    │                       │                       │
                    │                       │                       │
                    ▼                       ▼                       ▼
                                  ┌─────────────────┐
                                  │   datatp-erp    │
                                  └─────────┬───────┘
                                            │
                                            │
                                            ▼
                                  ┌─────────────────┐
                                  │   datatp-core   │
                                  └─────────────────┘
```

### 2. Backend
Cài đặt dependencies và build cho từng projects theo thứ tự:

> ⚠️ **Quan trọng**: Tuân thủ đúng thứ tự build

1. datatp-core
2. datatp-erp
3. datatp-document-ie
4. datatp-logistics
5. datatp-crm
6. datatp-build

```bash
cd datatp-core
gradle clean build -x test
gradle publishToMaven
```

### 3. Frontend
Install và build webui cho các dự án:

```bash
cd datatp-erp/webui/lib
pnpm install
pnpm run build
```

### 4. Database và môi trường

> 📁 Thực hiện trong thư mục: `working/release-dev/server-env/`

#### 4.1. DataTP Core
1. Download database tại: [https://beelogistics.cloud/download/datatpdb-latest.dump](https://beelogistics.cloud/download/datatpdb-latest.dump)
2. Tạo user, khởi tạo DB, và restore database mẫu:
   ```bash
   ./postgres-admin.sh create-db-user
   ./postgres-admin.sh new-db
   ./postgres.sh restore:datatpdb --file=datatpdb-latest.dump
   ```

#### 4.2. DataTP Document IE
1. Download database tại: [https://beelogistics.cloud/download/document_ie_db-latest.dump](https://beelogistics.cloud/download/document_ie_db-latest.dump)
2. Tạo user, khởi tạo DB, và restore database mẫu:
   ```bash
   ./postgres-admin.sh create-document-ie-user
   ./postgres-admin.sh new-document-ie-db
   ./postgres.sh restore:document_ie_db --file=document_ie_db-latest.dump
   ```

### 5. Khởi động server phát triển
Mở [http://localhost:3000](http://localhost:3000) để xem trang web.

## 🔍 Kiểm tra cài đặt
- [ ] Tất cả các dự án đã được clone thành công
- [ ] Backend đã được build theo đúng thứ tự
- [ ] Frontend đã được cài đặt và build
- [ ] Database đã được khởi tạo và restore
- [ ] Server phát triển đã khởi động thành công

