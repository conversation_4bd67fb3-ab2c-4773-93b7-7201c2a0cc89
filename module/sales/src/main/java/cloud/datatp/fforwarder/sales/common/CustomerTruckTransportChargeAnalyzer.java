package cloud.datatp.fforwarder.sales.common;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import cloud.datatp.fforwarder.sales.booking.entity.BookingTruckTransportCharge;
import cloud.datatp.fforwarder.sales.inquiry.entity.DeliveryServiceInquiry;
import cloud.datatp.fforwarder.sales.inquiry.entity.SpecificServiceInquiry;

import lombok.Getter;
import net.datatp.util.ds.Collections;
import net.datatp.util.ds.Objects;

@Getter
public class CustomerTruckTransportChargeAnalyzer {
  private List<BookingTruckTransportCharge> atOriginList;
  private List<BookingTruckTransportCharge> atDestinationList;
  private List<BookingTruckTransportCharge> unknownList;

  public CustomerTruckTransportChargeAnalyzer(List<BookingTruckTransportCharge> holder, SpecificServiceInquiry inquiry) {
    List<BookingTruckTransportCharge> unknownList;
    if(Collections.isEmpty(holder)) unknownList = new ArrayList<>();
    else unknownList = new ArrayList<>(holder);

    String fromLocationCode = inquiry.getFromLocationCode();
    String toLocationCode   = inquiry.getToLocationCode();

    /*
    DeliveryServiceInquiry deliveryInquiry = inquiry.getDeliveryServiceInquiry();

    if(Objects.nonNull(deliveryInquiry)) {
      String pickupLocCode   = deliveryInquiry.getPickupLocationCode();
      String deliveryLocCode = deliveryInquiry.getDeliveryLocationCode();
      this.atOriginList      = match(unknownList, pickupLocCode, fromLocationCode);
      this.atDestinationList = match(unknownList, toLocationCode, deliveryLocCode);
    }
     */
    this.unknownList       = unknownList;
  }

  List<BookingTruckTransportCharge> match(
    List<BookingTruckTransportCharge> holder, String pickupLocCode, String deliveryLoc) {
    List<BookingTruckTransportCharge> matchList = new ArrayList<>();
    if(Objects.isNull(pickupLocCode) || Objects.isNull(deliveryLoc)) return matchList;
    Iterator<BookingTruckTransportCharge> i = holder.iterator();
    while(i.hasNext()) {
      BookingTruckTransportCharge sel = i.next();
      if(pickupLocCode.equals(sel.getPickupLocationCode()) && deliveryLoc.equals(sel.getDeliveryLocationCode())) {
        i.remove();
        matchList.add(sel);
      }
    }
    return matchList;
  }
}