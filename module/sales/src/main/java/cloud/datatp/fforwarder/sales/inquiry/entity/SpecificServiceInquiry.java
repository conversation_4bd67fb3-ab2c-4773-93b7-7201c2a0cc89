package cloud.datatp.fforwarder.sales.inquiry.entity;

import cloud.datatp.fforwarder.price.entity.InquiryRequest;
import cloud.datatp.fforwarder.price.entity.InquiryRequest.InquiryStatus;
import cloud.datatp.fforwarder.settings.Purpose;
import cloud.datatp.fforwarder.settings.TransportationMode;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Index;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import java.io.Serial;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.company.entity.Company;
import net.datatp.module.company.entity.CompanyEntity;
import net.datatp.module.data.db.util.DeleteGraph;
import net.datatp.module.data.db.util.DeleteGraphJoinType;
import net.datatp.module.data.db.util.DeleteGraphs;
import net.datatp.util.dataformat.DataSerializer;
import net.datatp.util.ds.Arrays;
import net.datatp.util.text.DateUtil;

@Entity
@Table(
  name = SpecificServiceInquiry.TABLE_NAME,
  indexes = {
    @Index(name = SpecificServiceInquiry.TABLE_NAME + "_code_idx", columnList = "code"),
    @Index(name = SpecificServiceInquiry.TABLE_NAME + "_company_id_idx", columnList = "company_id"),
    @Index(name = SpecificServiceInquiry.TABLE_NAME + "_storage_state_idx", columnList = "storage_state"),
    @Index(name = SpecificServiceInquiry.TABLE_NAME + "_client_idx", columnList = "client_partner_id, client_label"),
    @Index(name = SpecificServiceInquiry.TABLE_NAME + "_saleman_idx", columnList = "saleman_account_id, saleman_label"),
    @Index(name = SpecificServiceInquiry.TABLE_NAME + "_reference_idx", columnList = "reference_code"),
    @Index(name = SpecificServiceInquiry.TABLE_NAME + "_request_date_idx", columnList = "request_date"),
    @Index(name = SpecificServiceInquiry.TABLE_NAME + "_location_idx", columnList = "from_location_code, to_location_code"),
    @Index(name = SpecificServiceInquiry.TABLE_NAME + "_mode_idx", columnList = "mode")
  }
)
@DeleteGraphs({
  @DeleteGraph(target = DeliveryServiceInquiry.class, joinField = "delivery_service_inquiry_id"),
  @DeleteGraph(target = Container.class, joinField = "specific_service_inquiry_id", joinType = DeleteGraphJoinType.OneToMany),
})
@JsonInclude(Include.NON_NULL)
@NoArgsConstructor
@Getter @Setter
public class SpecificServiceInquiry extends CompanyEntity {

  public static final String TABLE_NAME = "lgc_sales_specific_service_inquiry";
  public static final String SEQUENCE = "logistics:specific-inquiry";

  @Serial
  private static final long serialVersionUID = 1L;

  public enum InquiryFlow { REQUEST, QUOTATION, BOOKING }

  //@Column(name = "code", nullable = false, length = 50)
  @Column(name = "code", length = 50)
  private String code;

  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "request_date")
  private Date requestDate;

  @Enumerated(EnumType.STRING)
  @Column(name = "status", nullable = false)
  private InquiryStatus status = InquiryStatus.SUCCESS;

  //TODO: An - new field
  //@Enumerated(EnumType.STRING)
  //@Column(name = "status", nullable = false)
  //private InquiryFlow inquiryFlow = InquiryFlow.QUOTATION;

  @Enumerated(EnumType.STRING)
  @Column(name = "mode")
  private TransportationMode mode;

  @Enumerated(EnumType.STRING)
  private Purpose purpose = Purpose.IMPORT;

  /* -------------- Customer/ Partner ------------------- */
  @Column(name = "client_partner_type")
  private String clientPartnerType;

  @Column(name = "client_partner_id")
  private Long clientPartnerId;

  @Column(name = "client_label")
  private String clientLabel;

  @Column(name = "attention")
  private String attention;

  /* -------------- Saleman------------------- */
  @Column(name = "saleman_account_id")
  private Long salemanAccountId;

  @Column(name = "saleman_label")
  private String salemanLabel;

  /* -------------- Port/ Airport/ Location------------------- */
  @Column(name = "from_location_code")
  private String fromLocationCode;

  @Column(name = "from_location_label")
  private String fromLocationLabel;

  @Column(name = "to_location_code")
  private String toLocationCode;

  @Column(name = "to_location_label")
  private String toLocationLabel;

  @Column(name = "final_destination")
  private String finalDestination;

  @Column(name = "pickup_address", length = 2 * 1024)
  private String pickupAddress;

  @Column(name = "delivery_address", length = 2 * 1024)
  private String deliveryAddress;

  /* -------------- Shipment Info------------------- */
  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "estimated_time_departure")
  private Date estimatedTimeDeparture;

  @Enumerated(EnumType.STRING)
  @Column(name = "term_of_service")
  private TermOfService termOfService;

  @Column(name = "incoterms")
  private String incoterms;

  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "cargo_ready_date")
  private Date cargoReadyDate;

  @Column(name = "target_rate_and_charges")
  private String targetRateAndCharges;

  @Column(name = "container_types")
  private String containerTypes;

  @Column(name = "packaging_type")
  private String packagingType = "PK";

  @Column(name = "package_quantity")
  private int packageQty;

  @Column(name = "report_volume")
  private Double reportVolume;

  @Column(name = "report_volume_unit")
  private String reportVolumeUnit;

  @Column(name = "desc_of_goods", length = 1024 * 32)
  private String descOfGoods = "";

  @Column(name = "commodity", length = 1024 * 32)
  private String commodity;

  @Column(name = "reference_code")
  private String referenceCode;

  @Column(name = "gross_weight_kg")
  private double grossWeightKg;

  @Column(name = "volume_cbm")
  private double volumeCbm;

  @Column(name = "chargeable_weight")
  private double chargeableWeight;

  @Column(name = "chargeable_volume")
  private double chargeableVolume;

  @Column(name = "terms_and_conditions", length = 1024 * 32)
  private String termsAndConditions;

  @Column(length = 1024 * 32)
  private String note;

  @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
  @JoinColumn(name = "specific_service_inquiry_id")
  private List<Container> containers = new ArrayList<>();

  public SpecificServiceInquiry withContainer(Container... container) {
    containers = Arrays.addToList(containers, container);
    return this;
  }

  public void set(ClientInfo client, Company company) {
    super.set(client, company);
    set(client, company, containers);
  }

  @SuppressWarnings("unchecked")
  public SpecificServiceInquiry clearIds() {
    clearId(this);
    clearIds(containers);
    return this;
  }

  public SpecificServiceInquiry clone() {
    SpecificServiceInquiry clone = DataSerializer.JSON.clone(this);
    return clone;
  }

  public SpecificServiceInquiry convertToSpecificInquiry(InquiryRequest request) {
    this.mode = request.getMode();
    this.purpose = request.getPurpose();
    this.fromLocationCode = request.getFromLocationCode();
    this.fromLocationLabel = request.getFromLocationLabel();
    this.toLocationCode = request.getToLocationCode();
    this.toLocationLabel = request.getToLocationLabel();
    this.cargoReadyDate = request.getCargoReadyDate();
    this.estimatedTimeDeparture = request.getCargoReadyDate();
    this.targetRateAndCharges = request.getTargetRate();
    this.note = request.getNote();
    this.clientPartnerId = request.getClientPartnerId();
    this.clientLabel = request.getClientLabel();
    this.incoterms = request.getTermOfService();
    this.requestDate = request.getRequestDate();
    this.containerTypes = request.getShipmentDetail().getVolumeInfo();
    this.grossWeightKg = request.getShipmentDetail().getGrossWeightKg();
    this.volumeCbm = request.getShipmentDetail().getVolumeCbm();
    this.reportVolume = request.getShipmentDetail().getReportVolume();
    this.reportVolumeUnit = request.getShipmentDetail().getReportVolumeUnit();
    this.descOfGoods = request.getShipmentDetail().getDescOfGoods();
    this.commodity = request.getShipmentDetail().getCommodity();
    this.packageQty = Optional.ofNullable(request.getShipmentDetail().getPackageQty()).orElse(0);
    this.pickupAddress = request.getPickupAddress();
    this.deliveryAddress = request.getDeliveryAddress();
    return this;
  }

}