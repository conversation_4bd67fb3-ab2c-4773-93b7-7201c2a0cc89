package cloud.datatp.fforwarder.sales.integration;

import cloud.datatp.bfsone.crm.BFSOneApi;
import cloud.datatp.bfsone.partner.BFSOnePartnerGroup;
import cloud.datatp.bfsone.partner.BFSOnePartnerLogic;
import cloud.datatp.bfsone.partner.entity.BFSOnePartner;
import cloud.datatp.fforwarder.sales.booking.BookingLogic;
import cloud.datatp.fforwarder.sales.booking.SendInternalBookingMessagePlugin;
import cloud.datatp.fforwarder.sales.booking.dto.BookingModel;
import cloud.datatp.fforwarder.sales.booking.entity.Booking;
import cloud.datatp.fforwarder.sales.common.BookingShipmentInfo;
import cloud.datatp.fforwarder.settings.message.CRMMessageLogic;
import cloud.datatp.fforwarder.settings.message.entity.CRMMessageSystem;
import cloud.datatp.fforwarder.settings.message.entity.MessageType;

import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import javax.sql.DataSource;

import lombok.extern.slf4j.Slf4j;
import net.datatp.module.account.AccountLogic;
import net.datatp.module.account.entity.Account;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.company.CompanyConfigLogic;
import net.datatp.module.company.entity.Company;
import net.datatp.module.company.entity.CompanyConfig;
import net.datatp.module.data.db.ExternalDataSourceManager;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.SqlQueryUnitManager;
import net.datatp.module.data.db.SqlSelectView;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.hr.EmployeeReadLogic;
import net.datatp.module.hr.entity.Employee;
import net.datatp.util.dataformat.DataSerializer;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;
import net.datatp.util.error.RuntimeError;
import net.datatp.util.text.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class BFSOneCRMLogic {
  private final String accessCode = "jWQ953gZwSg6FLfnfCtJmdAxZJcYEABSQX8HOjAguSgmLHtFxThGi6ZDuBmoo1hNNI3W3r6FGPDYuGacRxYOIxvQUfoTOt4LL6rzcrIBwyTBa77Nvigxwotj8Ay97iqyiQOZ51zFhvBpWmXTG6/l/gNW+LmQ8WmX6RRzLOzbRmD5RBRdl5HwE8O5EHwjaeHMSPHG+BO+L3zHO69XWN/b3TqO+M2xdsPF32/FlwYDNKxNjDA4uJIPSySnwXw2iph0Zf7t+Ws6oT5mRYvF30r1bRYuOvKOMAQBgPiJ9PO5JkPUwsOKF2BeNBG0Y84fH6DL3fxqhcVl6VmHeVmab+wE+g==";

  @Autowired
  private CompanyConfigLogic companyConfigLogic;

  @Autowired
  private EmployeeReadLogic employeeLogic;

  @Autowired
  private AccountLogic accountLogic;

  @Autowired
  private BFSOneApi bfsOneApi;

  @Autowired
  private CRMMessageLogic crmMessageLogic;

  @Autowired
  private BookingLogic bookingLogic;

  @Autowired
  private BFSOnePartnerLogic bfsOnePartnerLogic;

  public BookingModel quickSendBFSOneIBooking(ClientInfo client, Company company, BookingModel bookingModel) {
    try {
      BookingModel updatedBookingModel = bookingLogic.saveBookingModel(client, company, bookingModel);
      updatedBookingModel = createInternalBooking(client, company, updatedBookingModel);
      Account cus = accountLogic.getAccountById(client, updatedBookingModel.getReceiverAccountId());
      Objects.assertNotNull(cus, "Receiver Account is not found, login id = " + updatedBookingModel.getReceiverEmployeeLabel());
      CRMMessageSystem message = new CRMMessageSystem();
      message.setContent(SendInternalBookingMessagePlugin.buildZaloMessage(updatedBookingModel));
      message.setScheduledAt(new Date());
      message.setMessageType(MessageType.ZALO);
      message.setReferenceId(updatedBookingModel.getId());
      message.setReferenceType(Booking.TABLE_NAME);
      message.setPluginName(SendInternalBookingMessagePlugin.PLUGIN_TYPE);
      message.setRecipients(new HashSet<>(Collections.singletonList(cus.getMobile())));
      crmMessageLogic.scheduleMessage(client, company, message);
      return updatedBookingModel;
    } catch (Exception e) {
      throw RuntimeError.UnknownError(e.getMessage());
    }
  }

  public BookingModel createInternalBooking(ClientInfo client, Company company, BookingModel bookingModel) {
    Employee employee = employeeLogic.getByAccount(client, company, bookingModel.getSenderAccountId());
    Objects.assertNotNull(employee, "Employee is not found, login id = " + bookingModel.getSenderLabel());
    Objects.assertNotNull(employee.getBfsoneCode(), "Sender Contact Code is not found, name = " + bookingModel.getSenderLabel());
    Objects.assertNotNull(employee.getBfsoneUsername(), "Sender Username is not found, name = " + bookingModel.getSenderLabel());

    String receiverEmpl = bookingModel.getReceiverEmployeeLabel();
    Employee receiver = employeeLogic.getByAccount(client, company, bookingModel.getReceiverAccountId());
    Objects.assertNotNull(receiver.getBfsoneCode(), "Receiver Contact Code is not found, name = " + receiverEmpl);


    boolean isValidEmp = StringUtil.isNotEmpty(employee.getBfsoneCode()) && StringUtil.isNotEmpty(employee.getBfsoneUsername());
    Objects.assertTrue(isValidEmp, "Sender Contact is not valid, name = " + bookingModel.getSenderLabel());
    String bfsoneEmployeeCode = employee.getBfsoneCode();
    String bfsoneUsername = employee.getBfsoneUsername();
    String authenticate = bfsOneApi.authenticate(accessCode, bfsoneEmployeeCode, bfsoneUsername);
    log.info("------------------------TOKEN---------------------------\n");
    DataSerializer.JSON.dump(authenticate);
    log.info("--------------------------------------------------------\n");


    Long clientPartnerId = bookingModel.getClientPartnerId();
    BFSOnePartner customerPartner = bfsOnePartnerLogic.getById(client, company, clientPartnerId);
    Objects.assertNotNull(customerPartner, "Customer is not found, id = " + clientPartnerId);
    MapObject bfsOneIBooking = bookingModel.toBFSOneIBooking(customerPartner);

    bfsOneIBooking.put("SendUserID", bfsoneEmployeeCode);
    String receiverContactCode = receiver.getBfsoneCode();
    Objects.assertNotNull(receiverContactCode, "Receiver Contact Code is not found, name = " + receiverEmpl);
    bfsOneIBooking.put("ReceiveUserID", receiverContactCode);

    BookingShipmentInfo shipmentInfo = bookingModel.getShipmentInfo();
    BFSOnePartner agent = bfsOnePartnerLogic.getById(client, company, shipmentInfo.getHandlingAgentPartnerId());
    if (agent != null) bfsOneIBooking.put("AgentID", agent.getBfsonePartnerCode());
    BFSOnePartner coloader = bfsOnePartnerLogic.getById(client, company, shipmentInfo.getCarrierPartnerId());
    if (coloader != null) bfsOneIBooking.put("ColoaderID", coloader.getBfsonePartnerCode());
    MapObject savedIB = null;
    DataSerializer.JSON.dump(bfsOneIBooking);

    try {
      String bkgID = bfsOneIBooking.getString("BkgID");
      if (StringUtil.isNotBlank(bkgID)) {
        MapObject bfsOneBKExist = bfsOneApi.loadIBooking(authenticate, bkgID);
        DataSerializer.JSON.dump(bfsOneBKExist);
        String bkgIDExist = bfsOneBKExist.getString("BkgID", null);
        if (StringUtil.isEmpty(bkgIDExist)) bfsOneIBooking.put("BkgID", null);
      }
      savedIB = bfsOneApi.createIBooking(authenticate, bfsOneIBooking);
    } catch (Exception ex) {
      log.info(ex.getMessage());
      log.error("Error when create internal booking: \n");
      DataSerializer.JSON.dump(bfsOneIBooking);
      throw RuntimeError.UnknownError(ex.getMessage());
    }

    Booking booking = bookingLogic.getBooking(client, company, bookingModel.getId());
    Objects.assertNotNull(booking, "Booking is not found, id = " + bookingModel.getId());

    String bkgID = savedIB.getString("BkgID");
    Objects.assertNotNull(bkgID, "BFSOne Reference is not found, id = " + bookingModel.getId());
    booking.setExternalCaseReference(bkgID);
    booking = bookingLogic.saveBooking(client, company, booking);
    bookingModel.setReferenceNo(bkgID);



    return bookingModel;
  }

  public Boolean deleteInternalBooking(ClientInfo client, Company company, String ibCode) {
    Employee employee = employeeLogic.getEmployee(client, company, client.getRemoteUser());
    Objects.assertNotNull(employee, "Employee is not found, login id = " + client.getRemoteUser());
    String bfsoneEmployeeCode = employee.getBfsoneCode();
    String bfsoneUsername = employee.getBfsoneUsername();
    String authenticate = bfsOneApi.authenticate(accessCode, bfsoneEmployeeCode, bfsoneUsername);
    bfsOneApi.deleteInternalBooking(authenticate, ibCode);
    return true;
  }

  public MapObject loadInternalBooking(ClientInfo client, Company company, String ibCode) {
    Employee employee = employeeLogic.getEmployee(client, company, client.getRemoteUser());
    Objects.assertNotNull(employee, "Employee is not found, login id = " + client.getRemoteUser());
    String bfsoneEmployeeCode = employee.getBfsoneCode();
    String bfsoneUsername = employee.getBfsoneUsername();
    String authenticate = bfsOneApi.authenticate(accessCode, bfsoneEmployeeCode, bfsoneUsername);
    return bfsOneApi.loadIBooking(authenticate, ibCode);
  }

  public BFSOnePartner createBFSPartner(ClientInfo client, Company company, Long fwdPartnerId) {
    BFSOnePartner fwdPartner = bfsOnePartnerLogic.getById(client, company, fwdPartnerId);
    Objects.assertNotNull(fwdPartner, "Partner is not found, id = " + fwdPartnerId);
    MapObject bfsOnePartner = fwdPartner.toBFSOnePartner();

    Employee employee = employeeLogic.getEmployee(client, company, client.getRemoteUser());
    Objects.assertNotNull(employee, "Employee is not found, login id = " + client.getRemoteUser());
    String bfsoneEmployeeCode = employee.getBfsoneCode();
    String bfsoneUsername = employee.getBfsoneUsername();

    bfsOnePartner.put("RequestUser", bfsoneUsername);

    log.info("------------------------Partner---------------------------\n");
    DataSerializer.JSON.dump(bfsOnePartner);
    log.info("--------------------------------------------------------\n");

    BFSOnePartnerGroup group = fwdPartner.getGroup();
    String authenticate = bfsOneApi.authenticate(accessCode, bfsoneEmployeeCode, bfsoneUsername);
    MapObject partner = bfsOneApi.createPartner(group, authenticate, bfsOnePartner);
    String partnerCode = partner.getString("PartnerCodeTemp", "");
    fwdPartner.setBfsonePartnerCode(partnerCode);
    fwdPartner.setPartnerCodeTemp(partnerCode);
    return bfsOnePartnerLogic.saveBFSOnePartner(client, fwdPartner);
  }

  public List<SqlMapRecord> findBookingLocalByHawb(ClientInfo client, Company company, String hawb) {
    SqlQueryParams params = new SqlQueryParams();
    params.addParam("hawb", hawb.trim());
    String scriptDir = bfsOnePartnerLogic.getAppEnv().addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/bfsone/groovy/FindBookingLocalSql.groovy";
    String scriptName = "FindBookingLocalByHawb";
    CompanyConfig companyConfig = bfsOnePartnerLogic.getCompanyConfigLogic().getCompanyConfigByCompanyId(client, company.getId());
    ExternalDataSourceManager.DataSourceParams dsPrams = companyConfig.getSubConfigAs("bfsone.ds", ExternalDataSourceManager.DataSourceParams.class);
    DataSource ds = bfsOnePartnerLogic.getDataSourceManager().getDataSource(company, dsPrams);
    SqlQueryUnitManager.QueryContext queryContext = bfsOnePartnerLogic.getSqlQueryUnitManager().create(scriptDir, scriptFile, scriptName);
    SqlSelectView view = queryContext.createSqlSelectView(ds, params);
    List<SqlMapRecord> records = view.getSqlMapRecords();
    log.info("Retrieved {} records", records.size());
    return records;
  }

}