package cloud.datatp.fforwarder.sales.booking.entity;

import cloud.datatp.fforwarder.price.common.ChargeTarget;
import cloud.datatp.fforwarder.price.entity.CommissionDistribution;
import cloud.datatp.fforwarder.sales.common.entity.CustomerCommissionDistribution;
import cloud.datatp.fforwarder.sales.common.entity.CustomerTruckTransportAdditionalCharge;
import cloud.datatp.fforwarder.sales.common.entity.CustomerTruckTransportCharge;
import cloud.datatp.fforwarder.sales.common.entity.CustomerTruckTransportCommissionDistribution;
import cloud.datatp.fforwarder.sales.inquiry.entity.SpecificServiceInquiry;
import cloud.datatp.fforwarder.settings.TransportationMode;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.ForeignKey;
import jakarta.persistence.Index;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import java.io.Serial;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.company.entity.Company;
import net.datatp.module.data.db.entity.EditMode;
import net.datatp.module.data.db.util.DeleteGraph;
import net.datatp.module.data.db.util.DeleteGraphJoinType;
import net.datatp.module.data.db.util.DeleteGraphs;
import net.datatp.util.ds.Arrays;
import net.datatp.util.ds.Collections;
import net.datatp.util.ds.Objects;
import net.datatp.util.text.StringUtil;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

@Entity
@Table(
    name = BookingTruckTransportCharge.TABLE_NAME,
    indexes = {
        @Index(columnList = "code")
    }
)
@DeleteGraphs({
    @DeleteGraph(
        target = CustomerTruckTransportAdditionalCharge.class,
        joinField = "booking_truck_charge_id", joinType = DeleteGraphJoinType.OneToMany),
    @DeleteGraph(
        target = CustomerTruckTransportCommissionDistribution.class,
        joinField = "booking_truck_charge_id", joinType = DeleteGraphJoinType.OneToMany),
})
@JsonInclude(Include.NON_NULL)
@NoArgsConstructor
@Getter
@Setter
public class BookingTruckTransportCharge extends CustomerTruckTransportCharge {
  @Serial
  private static final long serialVersionUID = 1L;

  public static final String TABLE_NAME = "lgc_sales_booking_truck_charge";

  @Column(name = "plan_container_number")
  private String planContainerNumber;

  @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
  @OnDelete(action = OnDeleteAction.CASCADE)
  @JoinColumn(name = "booking_truck_charge_id", referencedColumnName = "id")
  private List<CustomerTruckTransportAdditionalCharge> additionalCharges = new ArrayList<>();

  @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
  @OnDelete(action = OnDeleteAction.CASCADE)
  @JoinColumn(name = "booking_truck_charge_id", referencedColumnName = "id",
      foreignKey = @ForeignKey(name = "fk__lgc_sales_customer_truck_commission__lgc_sales_booking_truck_charge"))
  private List<CustomerTruckTransportCommissionDistribution> commissionDistributions = new ArrayList<>();

  public BookingTruckTransportCharge(ChargeTarget target, Booking template) {
    editMode = EditMode.VALIDATED;
    validFrom = new Date();
    validTo = new Date();
    currency = "VND";
    SpecificServiceInquiry inquiry = template.getInquiry();
    TransportationMode mode = inquiry.getMode();
    this.truckType = TruckType.CONTAINER;
    if (TransportationMode.isAirTransport(mode)) truckType = TruckType.REGULAR;
    if (TransportationMode.isTruckRegular(mode)) truckType = TruckType.REGULAR;

    if (target.equals(ChargeTarget.ORIGIN)) {
      pickupAddress  = inquiry.getPickupAddress();
      deliveryLocationCode = inquiry.getFromLocationCode();
      deliveryLocationLabel = inquiry.getFromLocationLabel();
    } else {
      pickupLocationCode = inquiry.getToLocationCode();
      pickupLocationLabel = inquiry.getToLocationLabel();
      deliveryLocationLabel = inquiry.getDeliveryAddress();
    }

    route = pickupLocationCode + "-" + deliveryLocationCode;
    if (StringUtil.isEmpty(carrierLabel)) carrierRoute = route + "-N/A";
    else carrierRoute = route + "-" + carrierLabel;
    assigneeLabel = inquiry.getSalemanLabel();
  }

  public void copyCommission(List<CommissionDistribution> commissions) {
    Collections.apply(
        commissions, commission -> withCommission(new CustomerTruckTransportCommissionDistribution(commission)));
  }

  public BookingTruckTransportCharge withAdditionalCharge(CustomerTruckTransportAdditionalCharge... addCharge) {
    additionalCharges = Arrays.addToList(additionalCharges, addCharge);
    return this;
  }

  public BookingTruckTransportCharge withCommission(CustomerTruckTransportCommissionDistribution... commission) {
    commissionDistributions = Arrays.addToList(commissionDistributions, commission);
    return this;
  }

  @Override
  public List<CustomerTruckTransportAdditionalCharge> getAdditionalCharges() {
    if (Objects.isNull(additionalCharges)) additionalCharges = new ArrayList<>();
    return additionalCharges;
  }

  public void setAdditionalCharges(List<CustomerTruckTransportAdditionalCharge> addCharges) {
    if (Objects.isNull(additionalCharges)) additionalCharges = new ArrayList<>();
    this.additionalCharges.clear();
    if (addCharges != null) this.additionalCharges.addAll(addCharges);
  }

  @Override
  public List<? extends CustomerCommissionDistribution> getCommissionDistributions() {
    if (Objects.isNull(commissionDistributions)) commissionDistributions = new ArrayList<>();
    return commissionDistributions;
  }

  @Override
  public BookingTransportCharge toTransportCharge() {
    return new BookingTransportCharge(this);
  }

  public void setCommissionDistributions(List<CustomerTruckTransportCommissionDistribution> commission) {
    if (Objects.isNull(commissionDistributions)) commissionDistributions = new ArrayList<>();
    this.commissionDistributions.clear();
    if (commission != null) this.commissionDistributions = commission;
  }

  @Override
  public void set(ClientInfo client, Company company) {
    super.set(client, company);
    set(client, company, additionalCharges);
    set(client, company, commissionDistributions);
  }
}