package cloud.datatp.fforwarder.sales.groovy

import net.datatp.lib.executable.ExecutableContext
import net.datatp.lib.executable.Executor
import net.datatp.module.data.db.ExecutableSqlBuilder
import net.datatp.util.ds.MapObject
import org.springframework.context.ApplicationContext

public class SaleReportSql extends Executor {

    //TODO: An - rename to SalemanActivityTrackerReport
    public class SalemanSystemPerformanceReport extends ExecutableSqlBuilder {
        @Override
        public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
            MapObject sqlParams = ctx.getParam("sqlParams");
            String query = """
                    WITH sorted_employees AS (
                        SELECT DISTINCT ON (e.account_id)
                               e."label" AS employee_label,
                               e.account_id AS employee_account_id,
                               e.company_branch AS company_branch
                        FROM company_hr_employee e
                        JOIN company_hr_department_employee_rel dr ON dr.employee_id = e.id
                        JOIN company_hr_department d ON d.id = dr.department_id
                        WHERE d."name" LIKE 'SALES%'
                        ${AND_FILTER_BY_PARAM("d.company_id", "companyId", sqlParams)}
                    ),
                    booking_counts AS (
                        SELECT i.saleman_account_id,
                               COUNT(b.id) AS booking_count
                        FROM lgc_sales_booking b
                        JOIN lgc_sales_specific_service_inquiry i ON i.id = b.inquiry_id
                        WHERE ${FILTER_BY_PARAM("b.company_id", "companyId", sqlParams)}
                        ${AND_FILTER_BY_RANGE('b.booking_date', 'reportDate', sqlParams)}
                        GROUP BY i.saleman_account_id
                    ),
                    quotation_counts AS (
                        SELECT a.id        AS saleman_account_id,
                               SUM(s.frequency) AS quotation_count
                        FROM core_statistic s
                        LEFT JOIN account_account a ON a.login_id = s.login_id
                        LEFT JOIN company_company c ON c.code = s.company_code
                        WHERE ${FILTER_BY_PARAM("c.id", "companyId", sqlParams)}
                              ${AND_FILTER_BY_RANGE('s.created_time', 'reportDate', sqlParams)}
                              AND s.group = 'fforwarder:quotation:export'
                        GROUP BY a.id
                    ),
                    customer_lead_counts AS (
                        SELECT leads.saleman_account_id,
                          COUNT(leads.id) AS new_lead_count,
                          COUNT(CASE WHEN leads.status = 'CONVERTED' THEN leads.id END) AS new_customer_count
                        FROM forwarder_customer_leads leads
                        WHERE ${FILTER_BY_PARAM("leads.company_id", "companyId", sqlParams)}
                        ${AND_FILTER_BY_RANGE('leads.date', 'reportDate', sqlParams)}
                        GROUP BY leads.saleman_account_id
                    ),
                    overdue_request_counts AS (
                        SELECT rq.saleman_account_id,
                               COUNT(mgs.id) AS overdue_request_count
                        FROM lgc_forwarder_crm_message_system mgs
                        JOIN lgc_price_inquiry_request rq ON rq.id = mgs.reference_id
                        WHERE mgs.plugin_name = 'inquiry-overdue-reminder'
                          AND mgs.message_type = 'MAIL'
                          AND mgs.status = 'SENT'
                          ${AND_FILTER_BY_RANGE('mgs.sent_at', 'reportDate', sqlParams)}
                          ${AND_FILTER_BY_PARAM("mgs.company_id", "companyId", sqlParams)}
                        GROUP BY rq.saleman_account_id
                    ),
                    task_counts AS (
                        SELECT task.saleman_account_id,
                               COUNT(CASE WHEN task.status = 'IN_PROGRESS' THEN task.id END) AS in_progress_tasks,
                               COUNT(CASE WHEN task.task_type = 'MEET_CUSTOMER' THEN task.id END) AS meet_customer_tasks,
                               COUNT(task.id) AS total_tasks
                        FROM forwarder_sales_daily_task task
                        WHERE ${FILTER_BY_PARAM("task.company_id", "companyId", sqlParams)}
                        ${AND_FILTER_BY_RANGE('task.due_date', 'reportDate', sqlParams)}
                        GROUP BY task.saleman_account_id
                    )
                    SELECT
                        se.employee_label,
                        se.employee_account_id,
                        se.company_branch,
                        COALESCE(task.total_tasks, 0) AS total_tasks,
                        COALESCE(task.in_progress_tasks, 0) AS in_progress_tasks,
                        COALESCE(task.meet_customer_tasks, 0) AS meet_customer_tasks,
                        COUNT(request.id) AS total_requests_pricing,
                        COUNT(CASE WHEN request.status IN ('NO_RESPONSE', 'DONE') THEN request.id END) AS no_response_requests,
                        COALESCE(bc.booking_count, 0)           AS booking_count,
                        COALESCE(qc.quotation_count, 0)         AS quotation_count,
                        COALESCE(lead.new_customer_count, 0)    AS new_customer_count,
                        COALESCE(lead.new_lead_count, 0)        AS new_lead_count,
                        COALESCE(orc.overdue_request_count, 0)  AS overdue_request_count
                    FROM sorted_employees se
                    LEFT JOIN lgc_price_inquiry_request request
                        ON se.employee_account_id = request.saleman_account_id
                        ${AND_FILTER_BY_PARAM("request.company_id", "companyId", sqlParams)}
                        ${AND_FILTER_BY_RANGE('request.request_date', 'reportDate', sqlParams)}
                    LEFT JOIN task_counts task
                        ON se.employee_account_id = task.saleman_account_id
                    LEFT JOIN booking_counts bc
                        ON se.employee_account_id = bc.saleman_account_id
                    LEFT JOIN quotation_counts qc
                        ON se.employee_account_id = qc.saleman_account_id
                    LEFT JOIN customer_lead_counts lead
                        ON se.employee_account_id = lead.saleman_account_id
                    LEFT JOIN overdue_request_counts orc
                        ON se.employee_account_id = orc.saleman_account_id
                    WHERE 1 = 1
                    GROUP BY
                        se.employee_label, se.employee_account_id, se.company_branch,
                        task.total_tasks, task.in_progress_tasks, task.meet_customer_tasks, qc.quotation_count,
                        bc.booking_count, lead.new_customer_count, lead.new_lead_count, orc.overdue_request_count
                    ORDER BY se.company_branch;
            """
            return query;
        }
    }



    public class SaleAccountReport extends ExecutableSqlBuilder {
        @Override
        public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
            MapObject sqlParams = ctx.getParam("sqlParams");

            String query = """
                WITH search_summary AS (
                  SELECT
                    aa.id AS sale_account_id,
                    aa.full_name AS sale_man_label,
                    COUNT(sta.id) AS total_search_count
                  FROM core_statistic sta
                    JOIN account_account aa ON aa.login_id = sta.login_id
                  WHERE sta."group" = 'fforwarder:pricing'
                    ${AND_FILTER_BY_RANGE('sta.created_time', 'reportDate', sqlParams)}
                  GROUP BY aa.id, aa.full_name
                ),
                search_details AS (
                  SELECT DISTINCT ON (sta.login_id)
                    aa.id AS account_id,
                    aa.full_name,
                    from_loc."label" as "source",
                    to_loc."label" as destination,
                    COUNT(sta.id) OVER (PARTITION BY sta.login_id) AS search_count
                  FROM core_statistic sta
                    JOIN account_account aa ON aa.login_id = sta.login_id
                    JOIN company_hr_employee che ON che.account_id = aa.id
                    left join settings_location as from_loc on from_loc.code = sta."source"
                    left join settings_location as to_loc on to_loc.code = sta.destination
                  WHERE sta."group" = 'fforwarder:pricing'
                    ${AND_FILTER_BY_RANGE('sta.created_time', 'reportDate', sqlParams)}
                    AND sta."source" IS NOT NULL
                    AND sta.destination IS NOT NULL
                  ORDER BY sta.login_id, search_count DESC
                ),
                booking_summary AS (
                  SELECT
                    inquiry.saleman_account_id AS account_id,
                    COUNT(b.id) AS booking_count
                  FROM lgc_sales_booking b
                    JOIN lgc_sales_specific_service_inquiry inquiry ON inquiry.id = b.inquiry_id
                        AND inquiry.storage_state = 'ACTIVE'
                  WHERE b.storage_state = 'ACTIVE'
                    ${AND_FILTER_BY_OPTION("inquiry.purpose", "purpose", sqlParams)}
                    ${AND_FILTER_BY_RANGE('inquiry.modified_time', 'reportDate', sqlParams)}
                  GROUP BY inquiry.saleman_account_id
                ),
                quotation_summary AS (
                  SELECT
                    inquiry.saleman_account_id AS account_id,
                    COUNT(q.id) AS quotation_count
                  FROM lgc_sales_specific_quotation q
                    JOIN lgc_sales_specific_service_inquiry inquiry ON inquiry.id = q.inquiry_id
                        AND inquiry.storage_state = 'ACTIVE'
                  WHERE q.storage_state = 'ACTIVE'
                    ${AND_FILTER_BY_RANGE('inquiry.modified_time', 'reportDate', sqlParams)}
                    ${AND_FILTER_BY_OPTION("inquiry.purpose", "purpose", sqlParams)}
                  GROUP BY inquiry.saleman_account_id
                ),
                inquiry_summary AS (
                  SELECT
                    saleman_account_id AS account_id,
                    COUNT(c.id) AS inquiry_count
                  FROM lgc_price_inquiry_request c
                  WHERE ${FILTER_BY_RANGE('c.request_date', 'reportDate', sqlParams)}
                        ${AND_FILTER_BY_OPTION("c.purpose", "purpose", sqlParams)}
                  GROUP BY saleman_account_id
                ),
                company_details AS (
                  SELECT DISTINCT ON (che.account_id)
                    che.account_id,
                    che."label" as sale_man_label,
                    com.code as code
                  FROM company_hr_employee che
                    JOIN company_company com ON com.id = che.company_id
                  WHERE che.storage_state = 'ACTIVE'
                    ${AND_FILTER_BY_OPTION('com.code', 'companyCode', sqlParams, ['beehph', 'beehan', 'beedad', 'bee', 'beehcm'])}
                  ORDER BY che.account_id
                )
                SELECT
                  cd.code as company_code,
                  cd.account_id as sale_account_id,
                  cd.sale_man_label,
                  ss.total_search_count,
                  sd.search_count as top_route_search_count,
                  sd."source" as search_source,
                  sd.destination as search_destination,
                  COALESCE(booking.booking_count, 0) AS booking_count,
                  COALESCE(qs.quotation_count, 0) AS quotation_count,
                  COALESCE(isum.inquiry_count, 0) AS inquiry_count
                FROM company_details cd
                    LEFT JOIN search_summary ss ON ss.sale_account_id = cd.account_id
                    LEFT JOIN search_details sd ON sd.account_id = cd.account_id
                    LEFT JOIN booking_summary booking ON booking.account_id = cd.account_id
                    LEFT JOIN quotation_summary qs ON qs.account_id = cd.account_id
                    LEFT JOIN inquiry_summary isum ON isum.account_id = cd.account_id
                WHERE cd.account_id IS NOT NULL AND ss.total_search_count > 0
                ORDER BY ss.total_search_count DESC;
            """;
            return query;
        }
    }

    public class SaleConversationRateReport extends ExecutableSqlBuilder {
      @Override
      public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
          MapObject sqlParams = ctx.getParam("sqlParams");

          String query = """
              SELECT
                cl.id AS customer_lead_id,
                cl.name AS customer_lead_name,
                cl.saleman_account_id,
                cl.saleman_label,
                cl.date AS created_date,
                cl.status AS customer_lead_status,
                COUNT(dt.id) AS meeting_count
              FROM forwarder_customer_leads cl
              JOIN company_hr_employee em ON em.account_id = cl.saleman_account_id AND ${FILTER_BY_PARAM("em.company_id", "companyId", sqlParams)}
              LEFT JOIN forwarder_sales_daily_task dt on dt.customer_lead_partner_id = cl.id AND dt.task_type = 'MEET_CUSTOMER'
              WHERE ${FILTER_BY_PARAM("cl.company_id", "companyId", sqlParams)}
              GROUP BY em.id, cl.id
              ORDER BY cl.saleman_label ASC, cl.date DESC;
            """;
          return query;
      }
    }

    public SaleReportSql() {
        register(new SalemanSystemPerformanceReport());
        register(new SaleAccountReport());
        register(new SaleConversationRateReport());
    }
}