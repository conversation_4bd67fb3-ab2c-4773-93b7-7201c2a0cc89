package cloud.datatp.fforwarder.sales.quotation.entity;

import cloud.datatp.fforwarder.price.common.ChargeTarget;
import cloud.datatp.fforwarder.price.entity.TransportFrequency;
import cloud.datatp.fforwarder.price.entity.TruckContainerTransportCharge;
import cloud.datatp.fforwarder.price.entity.TruckRegularTransportCharge;
import cloud.datatp.fforwarder.sales.booking.entity.BookingTransportCharge;
import cloud.datatp.fforwarder.sales.common.entity.CustomerTruckTransportAdditionalCharge;
import cloud.datatp.fforwarder.sales.common.entity.CustomerTruckTransportCharge;
import cloud.datatp.fforwarder.sales.common.entity.CustomerTruckTransportCommissionDistribution;
import cloud.datatp.fforwarder.sales.common.quote.CustomerContainerPriceGroup;
import cloud.datatp.fforwarder.sales.common.quote.CustomerTruckPriceGroup;
import cloud.datatp.fforwarder.sales.inquiry.entity.DeliveryServiceInquiry;
import cloud.datatp.fforwarder.sales.inquiry.entity.SpecificServiceInquiry;
import cloud.datatp.fforwarder.settings.TransportationMode;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Embedded;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Index;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import java.io.Serial;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.company.entity.Company;
import net.datatp.module.data.db.entity.EditMode;
import net.datatp.util.ds.Arrays;
import net.datatp.util.ds.Collections;
import net.datatp.util.text.StringUtil;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

@Entity
@Table(
    name = QuotationTruckTransportCharge.TABLE_NAME,
    indexes = {
        @Index(columnList = "code")
    }
)
@JsonInclude(Include.NON_NULL)
@Getter
@Setter
@NoArgsConstructor
public class QuotationTruckTransportCharge extends CustomerTruckTransportCharge {
  @Serial
  private static final long serialVersionUID = 1L;

  public static final String TABLE_NAME = "lgc_sales_quotation_truck_charge";

  @Embedded
  private CustomerContainerPriceGroup containerPriceGroup = new CustomerContainerPriceGroup();

  @Embedded
  private CustomerTruckPriceGroup truckPriceGroup = new CustomerTruckPriceGroup();

  @Column(name = "truck_transport_charge_code")
  private String truckTransportChargeCode;

  @Setter
  @Getter
  @Column(name = "specific_quotation_id")
  private Long specificQuotationId;

  @Column(name = "generic_quotation_id")
  private Long genericQuotationId;

  @Enumerated(EnumType.STRING)
  private ChargeTarget target = ChargeTarget.NONE;

  @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
  @OnDelete(action = OnDeleteAction.CASCADE)
  @JoinColumn(name = "quotation_truck_charge_id", referencedColumnName = "id")
  private List<TransportFrequency> transportFrequencies = new ArrayList<>();

  @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
  @OnDelete(action = OnDeleteAction.CASCADE)
  @JoinColumn(name = "quotation_truck_charge_id", referencedColumnName = "id")
  private List<CustomerTruckTransportAdditionalCharge> additionalCharges = new ArrayList<>();

  @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
  @OnDelete(action = OnDeleteAction.CASCADE)
  @JoinColumn(name = "quotation_truck_charge_id", referencedColumnName = "id")
  private List<CustomerTruckTransportCommissionDistribution> commissionDistributions = new ArrayList<>();

  public QuotationTruckTransportCharge(ChargeTarget target, SpecificQuotation quotation) {
    editMode = EditMode.VALIDATED;
    validFrom = new Date();
    validTo = new Date();
    currency = "VND";
    final SpecificServiceInquiry inquiry = quotation.getInquiry();
    final TransportationMode mode = inquiry.getMode();
    this.truckType = TruckType.CONTAINER;
    this.target = target;
    if (TransportationMode.isAirTransport(mode)) truckType = TruckType.REGULAR;
    if (TransportationMode.isTruckRegular(mode)) truckType = TruckType.REGULAR;

    if (target.equals(ChargeTarget.ORIGIN)) {
      pickupAddress  = inquiry.getPickupAddress();
      deliveryLocationCode = inquiry.getFromLocationCode();
      deliveryLocationLabel = inquiry.getFromLocationLabel();
    } else {
      pickupLocationCode = inquiry.getToLocationCode();
      pickupLocationLabel = inquiry.getToLocationLabel();
      deliveryLocationLabel = inquiry.getDeliveryAddress();
    }

    route = pickupLocationCode + "-" + deliveryLocationCode;
    if (StringUtil.isEmpty(carrierLabel)) carrierRoute = route + "-N/A";
    else carrierRoute = route + "-" + carrierLabel;

    specificQuotationId = quotation.getId();
    assigneeLabel = inquiry.getSalemanLabel();
    //assigneeEmployeeId = inquiry.getSalemanEmployeeId();
  }

  public QuotationTruckTransportCharge(TruckRegularTransportCharge truck) {
    super(truck);
    truckTransportChargeCode = truck.getCode();
    truckPriceGroup = new CustomerTruckPriceGroup(truck.getPriceGroup());

    Collections.apply(truck.getAdditionalCharges(),
        addCharge -> withAdditionalCharge(new CustomerTruckTransportAdditionalCharge(addCharge)));

    Collections.apply(truck.getCommissionDistributions(),
        commission -> withCommission(new CustomerTruckTransportCommissionDistribution(commission)));

    Collections.apply(truck.getTransportFrequencies(), this::withTransportFrequency);
  }

  public QuotationTruckTransportCharge(TruckContainerTransportCharge container) {
    super(container);
    truckTransportChargeCode = container.getCode();
    containerPriceGroup = new CustomerContainerPriceGroup(container.getPriceGroup());

    Collections.apply(container.getAdditionalCharges(),
        addCharge -> withAdditionalCharge(new CustomerTruckTransportAdditionalCharge(addCharge)));

    Collections.apply(container.getCommissionDistributions(),
        commission -> withCommission(new CustomerTruckTransportCommissionDistribution(commission)));
    Collections.apply(container.getTransportFrequencies(), this::withTransportFrequency);
  }

  public QuotationTruckTransportCharge withAdditionalCharge(CustomerTruckTransportAdditionalCharge... addCharge) {
    additionalCharges = Arrays.addToList(additionalCharges, addCharge);
    return this;
  }

  public QuotationTruckTransportCharge withCommission(CustomerTruckTransportCommissionDistribution... commission) {
    commissionDistributions = Arrays.addToList(commissionDistributions, commission);
    return this;
  }

  public QuotationTruckTransportCharge withTransportFrequency(TransportFrequency... frequencies) {
    transportFrequencies = Arrays.addToList(transportFrequencies, frequencies);
    return this;
  }

  public List<TransportFrequency> getTransportFrequencies() {
    if (Objects.isNull(transportFrequencies)) transportFrequencies = new ArrayList<>();
    return transportFrequencies;
  }

  public void setTransportFrequencies(List<TransportFrequency> frequencies) {
    if (Objects.isNull(transportFrequencies)) transportFrequencies = new ArrayList<>();
    this.transportFrequencies.clear();
    if (frequencies != null) this.transportFrequencies.addAll(frequencies);
  }


  public List<CustomerTruckTransportAdditionalCharge> getAdditionalCharges() {
    if (Objects.isNull(additionalCharges)) additionalCharges = new ArrayList<>();
    return additionalCharges;
  }

  public void setAdditionalCharges(List<CustomerTruckTransportAdditionalCharge> addCharges) {
    if (Objects.isNull(additionalCharges)) additionalCharges = new ArrayList<>();
    this.additionalCharges.clear();
    if (addCharges != null) this.additionalCharges.addAll(addCharges);
  }

  public List<CustomerTruckTransportCommissionDistribution> getCommissionDistributions() {
    if (Objects.isNull(commissionDistributions)) commissionDistributions = new ArrayList<>();
    return commissionDistributions;
  }

  public void setCommissionDistributions(List<CustomerTruckTransportCommissionDistribution> commissions) {
    if (Objects.isNull(commissionDistributions)) commissionDistributions = new ArrayList<>();
    this.commissionDistributions.clear();
    if (commissions != null) this.commissionDistributions.addAll(commissions);
  }

  @Override
  public BookingTransportCharge toTransportCharge() {
    return new BookingTransportCharge(this);
  }

  @Override
  public void set(ClientInfo client, Company company) {
    super.set(client, company);
    set(client, company, transportFrequencies);
    set(client, company, additionalCharges);
    set(client, company, commissionDistributions);
  }

  public QuotationTruckTransportCharge withType(TruckType truckType) {
    this.truckType = truckType;
    return this;
  }
}