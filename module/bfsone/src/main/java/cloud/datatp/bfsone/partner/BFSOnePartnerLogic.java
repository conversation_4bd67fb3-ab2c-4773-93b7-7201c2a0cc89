package cloud.datatp.bfsone.partner;

import cloud.datatp.bfsone.partner.entity.BFSOnePartner;
import cloud.datatp.bfsone.partner.entity.SalemanPartnerObligation;
import cloud.datatp.bfsone.partner.repository.BFSOnePartnerRepository;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import net.datatp.lib.executable.ExecutableContext;
import net.datatp.module.account.AccountLogic;
import net.datatp.module.account.NewAccountModel;
import net.datatp.module.account.ProfileLogic;
import net.datatp.module.account.entity.Account;
import net.datatp.module.account.entity.AccountType;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.company.CompanyConfigLogic;
import net.datatp.module.company.CompanyLogic;
import net.datatp.module.company.entity.Company;
import net.datatp.module.core.security.SecurityLogic;
import net.datatp.module.core.security.entity.AppPermission;
import net.datatp.module.data.db.DAOService;
import net.datatp.module.data.db.ExternalDataSourceManager;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.data.db.util.DBConnectionUtil;
import net.datatp.module.data.db.util.DeleteGraphBuilder;
import net.datatp.module.hr.EmployeeLogic;
import net.datatp.module.hr.EmployeeReadLogic;
import net.datatp.module.hr.entity.Employee;
import net.datatp.module.partner.PartnerGroupLogic;
import net.datatp.module.partner.PartnerLogic;
import net.datatp.module.resource.entity.OwnerType;
import net.datatp.module.resource.location.CountryLogic;
import net.datatp.module.resource.location.entity.Country;
import net.datatp.module.resource.misc.ContactLogic;
import net.datatp.module.resource.misc.entity.AccountContact;
import net.datatp.module.service.ExecutableUnitManager;
import net.datatp.util.ds.Arrays;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;
import net.datatp.util.text.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Getter
@Component
public class BFSOnePartnerLogic extends DAOService {

  @Autowired
  private BFSOnePartnerRepository fwdPartnerRepo;

  @Autowired
  private SalemanPartnerObligationLogic obligationLogic;

  @Autowired
  private PartnerLogic partnerLogic;

  @Autowired
  private EmployeeLogic employeeLogic;

  @Autowired
  private SecurityLogic securityLogic;

  @Autowired
  private ProfileLogic profileLogic;

  @Autowired
  @Getter
  private ContactLogic contactLogic;

  @Autowired
  @Getter
  private CompanyLogic companyLogic;

  @Autowired
  private CountryLogic countryLogic;

  @Autowired
  private AccountLogic accountLogic;

  @Autowired
  private EmployeeReadLogic hrService;

  @Autowired
  private PartnerGroupLogic partnerGroupLogic;

  @Autowired
  ExecutableUnitManager executableUnitManager;

  @Autowired
  CompanyConfigLogic companyConfigLogic;

  @Autowired
  ExternalDataSourceManager dataSourceManager;

  public BFSOnePartner getById(ClientInfo client, Company company, Long id) {
    return fwdPartnerRepo.getById(id);
  }

  public BFSOnePartner getByAccountId(ClientInfo client, Company company, Long accountId) {
    return fwdPartnerRepo.getByAccountId(accountId);
  }

  public BFSOnePartner saveBFSOnePartner(ClientInfo client, BFSOnePartner forwarderPartner) {
    forwarderPartner.computePartnerGroup();
    if (StringUtil.isEmpty(forwarderPartner.getBfsonePartnerCode())) {
      BFSOnePartnerGroup group = forwarderPartner.getGroup();

      String code = "";
      if (BFSOnePartnerGroup.CUSTOMERS == group) {
        code = "CS";
      } else if (BFSOnePartnerGroup.AGENTS == group) {
        code = "AG";
      } else {
        code = "CL";
      }
      code += forwarderPartner.getTaxCode();
      forwarderPartner.setBfsonePartnerCode(code);
    }

    if (forwarderPartner.isNew()) {
      Account account = getOrCreateAccount(client, forwarderPartner);
      forwarderPartner.withAccount(account);
    }
    forwarderPartner.withBillReference();
    forwarderPartner.set(client);
    return fwdPartnerRepo.save(forwarderPartner);
  }

  public BFSOnePartner createBFSOnePartner(ClientInfo client, Company company, BFSOnePartner forwarderPartner) {
    BFSOnePartner partnerInDb = fwdPartnerRepo.getByBFSOneCode(forwarderPartner.getBfsonePartnerCode());
    BFSOnePartner partnerBfsOne = fetchBFSOnePartnersByCode(client, company, forwarderPartner.getBfsonePartnerCode());
    List<Employee> employees = employeeLogic.findEmployeeByBFSOneCode(client, partnerBfsOne.getSaleOwnerContactCode());
    if (Objects.isNull(partnerInDb)) {
      if (net.datatp.util.ds.Collections.isNotEmpty(employees)) {
        Employee employee = employees.get(0);
        forwarderPartner.withSalemanOwner(employee);
        forwarderPartner.withInputEmployee(employee);
      }
      partnerInDb = saveBFSOnePartner(client, forwarderPartner);
    }
    if (net.datatp.util.ds.Collections.isNotEmpty(employees)) {
      Employee employee = employees.get(0);
      obligationLogic.createIfNotExists(client, new SalemanPartnerObligation(employee, partnerInDb));
    }
    return partnerInDb;
  }

  public List<BFSOnePartner> createBFSOnePartner(ClientInfo client, List<BFSOnePartner> partners) {
    List<BFSOnePartner> holder = new ArrayList<>();
    for (BFSOnePartner partner : partners) {
      holder.add(saveBFSOnePartner(client, partner));
    }
    return holder;
  }

  public List<BFSOnePartner> createPartners(ClientInfo client, Company company, Employee saleman, List<BFSOnePartner> partners) {
    List<BFSOnePartner> holder = new ArrayList<>();
    try {
      for (BFSOnePartner forwarderPartner : partners) {
        BFSOnePartner partnerInDb = getByCode(client, forwarderPartner.getBfsonePartnerCode());
        if (partnerInDb == null) {
          Account account = accountLogic.getAccountById(client, client.getAccountId());

          forwarderPartner.withSalemanOwner(saleman);
          forwarderPartner.withInputEmployee(saleman);

          partnerInDb = saveBFSOnePartner(client, forwarderPartner);
        }
        obligationLogic.createIfNotExists(client, new SalemanPartnerObligation(saleman, partnerInDb));
        holder.add(partnerInDb);
      }
    } catch (Exception e) {
      log.error("-------------------- Create Forwarder Partners Error, Saleman: {}-------------------------", saleman.getLabel());
      throw new RuntimeException(e);
    }
    return holder;
  }

  public List<BFSOnePartner> createPartners(ClientInfo client, Company company, List<MapObject> records) {
    List<BFSOnePartner> holder = new ArrayList<>();
    try {
      log.info("Fetch saleman authorized data -------------------------\n\n\n\n");
      Map<String, List<BFSOnePartner>> salePartnerGroup = BFSOnePartner.groupBySaleObligation(records);
      for (String bfsoneCode : salePartnerGroup.keySet()) {
        List<BFSOnePartner> partners = salePartnerGroup.get(bfsoneCode);
        List<Employee> employees = employeeLogic.findEmployeeByBFSOneCode(client, bfsoneCode);
        if (net.datatp.util.ds.Collections.isNotEmpty(employees) && employees.size() == 1) {
          Employee employee = employees.get(0);
          Company companyEmployee = companyLogic.getCompany(client, employee.getCompanyId());
          List<BFSOnePartner> savedPartners = createPartners(client, companyEmployee, employee, partners);
          holder.addAll(savedPartners);
        } else {
          log.warn("Owner Saleman not found, code = {}", bfsoneCode);
        }
      }

      log.info("Fetch owner saleman partner data -------------------------\n\n\n\n");
      Map<String, List<BFSOnePartner>> firstSalePartnerGroup = BFSOnePartner.groupBySaleOwnerObligation(records);
      for (String bfsoneCode : firstSalePartnerGroup.keySet()) {
        List<BFSOnePartner> partners = firstSalePartnerGroup.get(bfsoneCode);
        List<Employee> employees = employeeLogic.findEmployeeByBFSOneCode(client, bfsoneCode);
        if (net.datatp.util.ds.Collections.isNotEmpty(employees) && employees.size() == 1) {
          Employee employee = employees.get(0);
          Company companyEmployee = companyLogic.getCompany(client, employee.getCompanyId());
          List<BFSOnePartner> savedPartners = createPartners(client, companyEmployee, employee, partners);
          holder.addAll(savedPartners);
        } else {
          log.warn("Owner Saleman not found, code = {}", bfsoneCode);
        }
      }
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
    return holder;
  }

  public List<SqlMapRecord> searchBFSOnePartners(ClientInfo client, Company company, SqlQueryParams sqlParams) {
    try {
      final AppPermission permission = securityLogic.getAppPermission(client, company.getId(), "logistics", "user-logistics-sales");
      if (permission == null) return Collections.emptyList();
      Employee employee = employeeLogic.getEmployee(client, company, client.getRemoteUser());
      sqlParams.addParam("accessEmployeeId", employee.getId());
      sqlParams.addParam("companyId", company.getId());
      String scriptDir = appEnv.addonPath("logistics", "groovy");
      String scriptFile = "cloud/datatp/bfsone/groovy/SearchBFSOnePartnerSql.groovy";
      return searchDbRecords(client, scriptDir, scriptFile, "SearchBFSOnePartner", sqlParams);
    } catch (Exception e) {
      log.error("-------------------- Search BFSOne Partners Error -------------------------");
      log.error(e.getMessage());
      return Collections.emptyList();
    }
  }

  public List<SqlMapRecord> searchBFSOnePartnerCustomers(ClientInfo client, Company company, SqlQueryParams sqlParams) {
    try {
      final AppPermission permission = securityLogic.getAppPermission(client, company.getId(), "logistics", "user-logistics-sales");
      if (permission == null) return Collections.emptyList();
      sqlParams.addParam("companyId", company.getId());
      sqlParams.addParam("accessAccountId", client.getAccountId());
      String scriptDir = appEnv.addonPath("logistics", "groovy");
      String scriptFile = "cloud/datatp/bfsone/groovy/SearchBFSOnePartnerCustomerSql.groovy";
      return searchDbRecords(client, scriptDir, scriptFile, "SearchBFSOnePartner", sqlParams);
    } catch (Exception e) {
      log.error("-------------------- Search BFSOne Partners Error -------------------------");
      log.error(e.getMessage());
      return Collections.emptyList();
    }
  }

  public int deleteBFSOnePartner(ClientInfo client, Company company, List<Long> targetIds) {
    DBConnectionUtil connectionUtil = new DBConnectionUtil(primaryDataSource);
    DeleteGraphBuilder deleteGraphBuilder = new DeleteGraphBuilder(connectionUtil, company.getId(), BFSOnePartner.class, targetIds);
    final int count = deleteGraphBuilder.runDelete();
    connectionUtil.commit();
    connectionUtil.close();
    return count;
  }

  public List<BFSOnePartner> findByTaxCode(ClientInfo client, Company company, String taxCode) {
    return fwdPartnerRepo.findByTaxCode(taxCode);
  }

  public BFSOnePartner getByCode(ClientInfo client, String code) {
    return fwdPartnerRepo.getByBFSOneCode(code);
  }

  public List<BFSOnePartner> findByBFSOneCodeTemp(ClientInfo client, String codeTemp) {
    return fwdPartnerRepo.findByBFSOneCodeTemp(codeTemp);
  }

  public Account getOrCreateAccount(ClientInfo client, BFSOnePartner record) {
    String legacyLoginId = record.getBfsonePartnerCode();
    Account account = accountLogic.getAccountRepo().getByLegacyLoginId(legacyLoginId);
    if (account != null) return account;
    log.info("--- Create account: bfsone code = " + legacyLoginId);
    account = new Account();
    account.setLoginId(legacyLoginId);
    account.setAccountType(AccountType.ORGANIZATION);
    account.setFullName(record.getLabel());
    account.setLegacyLoginId(legacyLoginId);
    NewAccountModel model = accountLogic.createNewAccount(client, new NewAccountModel(account));
    account = model.getAccount();

    AccountContact contact = new AccountContact("Contact");
    List<Country> countries = countryLogic.findCountriesByPattern(client, record.getCountryLabel());
    if (net.datatp.util.ds.Collections.isNotEmpty(countries)) {
      final Country country = countries.get(0);
      contact.setCountryName(country.getCode());
      contact.setCountryLabel(country.getLabel());
    }
    contact.setEmail(Arrays.asSet(account.getEmail()));
    contact.setMobile(Arrays.asSet(account.getMobile()));
    contact.setOwnerType(OwnerType.Account);
    contact.setOwnerId(account.getId());
    contact.setOwnerIdentifier(account.getLoginId());
    contactLogic.saveContact(client, contact);
    return account;
  }

  @SuppressWarnings("unchecked")
  public List<SqlMapRecord> searchBFSOnePartnersBySaleman(ClientInfo client, Company company, List<Employee> employees) {
    SqlQueryParams sqlParams = new SqlQueryParams();
    sqlParams.addParam("contactIds", employees.stream()
      .map(Employee::getBfsoneCode)
      .filter(code -> code != null && !code.isEmpty())
      .collect(Collectors.toList()));

    String scriptDir = appEnv.addonPath("logistics", "groovy");
    ExecutableContext ctx =
      new ExecutableContext()
        .withScriptEnv(scriptDir, "cloud/datatp/bfsone/partner/BFSOnePartnerLogicUnit.java", "FetchBFSOnePartnerBySaleman")
        .withParam(this).withParam(client).withParam(company).withParam(sqlParams);
    return (List<SqlMapRecord>) executableUnitManager.execute(ctx);
  }

  @SuppressWarnings("unchecked")
  public List<SqlMapRecord> searchPublicBFSOnePartners(ClientInfo client, Company company, SqlQueryParams sqlParams) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    ExecutableContext ctx =
      new ExecutableContext()
        .withScriptEnv(scriptDir, "cloud/datatp/bfsone/partner/BFSOnePartnerLogicUnit.java", "FetchBFSOnePublicPartner")
        .withParam(this).withParam(client).withParam(company).withParam(sqlParams);
    return (List<SqlMapRecord>) executableUnitManager.execute(ctx);
  }

  @SuppressWarnings("unchecked")
  public BFSOnePartner fetchBFSOnePartnersByCode(ClientInfo client, Company company, String bfsonePartnerCode) {
    SqlQueryParams sqlParams = new SqlQueryParams();
    sqlParams.addParam("bfsonePartnerCode", bfsonePartnerCode);

    String scriptDir = appEnv.addonPath("logistics", "groovy");
    ExecutableContext ctx =
      new ExecutableContext()
        .withScriptEnv(scriptDir, "cloud/datatp/bfsone/partner/BFSOnePartnerLogicUnit.java", "FetchBFSOnePartnerBySaleman")
        .withParam(this).withParam(client).withParam(company).withParam(sqlParams);

    List<SqlMapRecord> records = (List<SqlMapRecord>) executableUnitManager.execute(ctx);
    if (net.datatp.util.ds.Collections.isNotEmpty(records)) return new BFSOnePartner(records.get(0));
    return null;
  }

  @SuppressWarnings("unchecked")
  public List<BFSOnePartner> fetchBFSOnePartners(ClientInfo client, Company company, SqlQueryParams sqlParams) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    ExecutableContext ctx =
      new ExecutableContext()
        .withScriptEnv(scriptDir, "cloud/datatp/bfsone/partner/BFSOnePartnerLogicUnit.java", "FetchBFSOnePartnerBySaleman")
        .withParam(this).withParam(client).withParam(company).withParam(sqlParams);
    List<BFSOnePartner> partners = new ArrayList<>();
    List<SqlMapRecord> records = (List<SqlMapRecord>) executableUnitManager.execute(ctx);
    for (SqlMapRecord record : records) {
      partners.add(new BFSOnePartner(record));
    }
    return partners;
  }

  public List<SqlMapRecord> checkBFSOnePartnerByTaxCode(ClientInfo client, Company company, String taxCode) {
    SqlQueryParams sqlParams = new SqlQueryParams();
    sqlParams.addParam("taxCode", taxCode.trim());
    Objects.assertNotNull(taxCode, "Tax code must be required!!!");
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    ExecutableContext ctx =
      new ExecutableContext()
        .withScriptEnv(scriptDir, "cloud/datatp/bfsone/partner/BFSOnePartnerLogicUnit.java", "CheckBFSOnePartnerByTaxCode")
        .withParam(this).withParam(client).withParam(company).withParam(sqlParams);
    return (List<SqlMapRecord>) executableUnitManager.execute(ctx);
  }

}