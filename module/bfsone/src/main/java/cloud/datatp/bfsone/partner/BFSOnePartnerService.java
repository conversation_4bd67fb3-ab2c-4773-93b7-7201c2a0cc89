package cloud.datatp.bfsone.partner;

import cloud.datatp.bfsone.partner.entity.BFSOnePartner;

import java.util.List;

import net.datatp.module.common.ClientInfo;
import net.datatp.module.company.entity.Company;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.hr.entity.Employee;
import net.datatp.module.service.BaseComponent;
import net.datatp.util.ds.MapObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("BFSOnePartnerService")
public class BFSOnePartnerService extends BaseComponent {

  @Autowired
  private BFSOnePartnerLogic bfsonePartnerLogic;

  @Transactional(readOnly = true)
  public BFSOnePartner getBFSOnePartner(ClientInfo client, Company company, Long id) {
    return bfsonePartnerLogic.getById(client, company, id);
  }

  @Transactional(readOnly = true)
  public BFSOnePartner getBFSOnePartnerByCode(ClientInfo client, Company company, String code) {
    return bfsonePartnerLogic.getByCode(client, code);
  }

  @Transactional(readOnly = true)
  public List<BFSOnePartner> findBFSOnePartnerByCodeTemp(ClientInfo client, Company company, String codeTemp) {
    return bfsonePartnerLogic.findByBFSOneCodeTemp(client, codeTemp);
  }

  @Transactional(readOnly = true)
  public List<BFSOnePartner> findByTaxCode(ClientInfo client, Company company, String taxCode) {
    return bfsonePartnerLogic.findByTaxCode(client, company, taxCode);
  }

  @Transactional
  public int deleteBFSOnePartners(ClientInfo client, Company company, List<Long> targetIds) {
    return bfsonePartnerLogic.deleteBFSOnePartner(client, company, targetIds);
  }

  @Transactional(readOnly = true)
  public BFSOnePartner getByPartnerId(ClientInfo client, Company company, Long partnerId) {
    return bfsonePartnerLogic.getByAccountId(client, company, partnerId);
  }

  @Transactional
  public BFSOnePartner saveBFSOnePartner(ClientInfo client, Company company, BFSOnePartner partner) {
    return bfsonePartnerLogic.saveBFSOnePartner(client, partner);
  }

  @Transactional
  public BFSOnePartner createBFSOnePartner(ClientInfo client, Company company, BFSOnePartner forwarderPartner) {
    return bfsonePartnerLogic.createBFSOnePartner(client, company, forwarderPartner);
  }

  @Transactional
  public List<BFSOnePartner> createBFSOnePartners(ClientInfo client, Company company, List<BFSOnePartner> partners) {
    return bfsonePartnerLogic.createBFSOnePartner(client, partners);
  }

  @Transactional
  public List<BFSOnePartner> createPartners(ClientInfo client, Company company, Employee saleman, List<BFSOnePartner> partners) {
    return bfsonePartnerLogic.createPartners(client, company, saleman, partners);
  }

  @Transactional
  public List<BFSOnePartner> createPartners(ClientInfo client, Company company, List<MapObject> partners) {
    return bfsonePartnerLogic.createPartners(client, company, partners);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchBFSOnePartners(ClientInfo client, Company company, SqlQueryParams params) {
    return bfsonePartnerLogic.searchBFSOnePartners(client, company, params);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchBFSOnePartnerCustomers(ClientInfo client, Company company, SqlQueryParams params) {
    return bfsonePartnerLogic.searchBFSOnePartnerCustomers(client, company, params);
  }

  @Transactional(readOnly = true)
  public BFSOnePartner fetchBFSOnePartnersByCode(ClientInfo client, Company company, String bfsonePartnerCode) {
    return bfsonePartnerLogic.fetchBFSOnePartnersByCode(client, company, bfsonePartnerCode);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> checkBFSOnePartnerByTaxCode(ClientInfo client, Company company, String taxCode) {
    return bfsonePartnerLogic.checkBFSOnePartnerByTaxCode(client, company, taxCode);
  }

}