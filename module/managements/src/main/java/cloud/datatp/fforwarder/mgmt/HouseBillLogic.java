package cloud.datatp.fforwarder.mgmt;

import cloud.datatp.bfsone.partner.BFSOnePartnerLogic;
import cloud.datatp.fforwarder.mgmt.QuickHouseBillModel.QuickContainerModel;
import cloud.datatp.fforwarder.mgmt.QuickHouseBillModel.QuickHouseBillCustomField;
import cloud.datatp.fforwarder.mgmt.accounting.CustomerTransportChargeInvoiceLogic;
import cloud.datatp.fforwarder.mgmt.accounting.LogisticMgmtHBInvoicePlugin;
import cloud.datatp.fforwarder.mgmt.air.entity.AirFreightHouseBill;
import cloud.datatp.fforwarder.mgmt.customclearance.entity.CustomClearanceHouseBill;
import cloud.datatp.fforwarder.mgmt.entity.HouseBill;
import cloud.datatp.fforwarder.mgmt.entity.HouseBillAttachment;
import cloud.datatp.fforwarder.mgmt.entity.HouseBillAttachment.Type;
import cloud.datatp.fforwarder.mgmt.entity.MasterBill;
import cloud.datatp.fforwarder.mgmt.entity.TrackableCargo;
import cloud.datatp.fforwarder.mgmt.po.BookingProcessLogic;
import cloud.datatp.fforwarder.mgmt.po.OrderProcessLogic;
import cloud.datatp.fforwarder.mgmt.po.POLogic;
import cloud.datatp.fforwarder.mgmt.po.entity.BookingProcess;
import cloud.datatp.fforwarder.mgmt.po.entity.PurchaseOrder;
import cloud.datatp.fforwarder.mgmt.po.entity.PurchaseOrderFollower;
import cloud.datatp.fforwarder.mgmt.rail.entity.RailFreightHouseBill;
import cloud.datatp.fforwarder.mgmt.repository.HouseBillAttachmentRepository;
import cloud.datatp.fforwarder.mgmt.sea.entity.SeaFreightHouseBill;
import cloud.datatp.fforwarder.mgmt.truck.entity.TruckFreightHouseBill;
import cloud.datatp.fforwarder.sales.booking.BookingLogic;
import cloud.datatp.fforwarder.sales.booking.entity.Booking;
import cloud.datatp.fforwarder.sales.booking.entity.BookingAirTransportCharge;
import cloud.datatp.fforwarder.sales.booking.entity.BookingRailTransportCharge;
import cloud.datatp.fforwarder.sales.booking.entity.BookingSeaTransportCharge;
import cloud.datatp.fforwarder.sales.inquiry.entity.Cargo;
import cloud.datatp.fforwarder.sales.inquiry.entity.Container;
import cloud.datatp.fforwarder.sales.inquiry.entity.SpecificServiceInquiry;
import cloud.datatp.fforwarder.settings.Purpose;
import cloud.datatp.fforwarder.settings.TransportationMode;
import cloud.datatp.fforwarder.settings.commodity.CommodityTypeLogic;
import jakarta.persistence.Table;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.Getter;
import net.datatp.module.account.AccountLogic;
import net.datatp.module.account.UserLogic;
import net.datatp.module.account.entity.Account;
import net.datatp.module.accounting.InvoiceItemModel;
import net.datatp.module.accounting.InvoiceItemProcessor;
import net.datatp.module.accounting.InvoiceLogic;
import net.datatp.module.accounting.InvoicePartnerMap;
import net.datatp.module.accounting.entity.Invoice;
import net.datatp.module.accounting.entity.InvoiceLink;
import net.datatp.module.accounting.settings.CashFlow;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.company.CompanyConfigLogic;
import net.datatp.module.company.CompanyLogic;
import net.datatp.module.company.entity.Company;
import net.datatp.module.company.entity.CompanyConfig;
import net.datatp.module.company.entity.CompanyInfo;
import net.datatp.module.company.settings.unit.converter.MassUnit;
import net.datatp.module.company.settings.unit.converter.VolumeUnit;
import net.datatp.module.core.security.SecurityLogic;
import net.datatp.module.data.db.DAOService;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.query.ClauseFilter;
import net.datatp.module.data.db.query.ConditionFilter;
import net.datatp.module.data.db.query.EntityTable;
import net.datatp.module.data.db.query.Join;
import net.datatp.module.data.db.query.OptionFilter;
import net.datatp.module.data.db.query.RangeFilter;
import net.datatp.module.data.db.query.SearchFilter;
import net.datatp.module.data.db.query.SelectField;
import net.datatp.module.data.db.query.SqlQuery;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.data.db.seq.SeqService;
import net.datatp.module.hr.EmployeeLogic;
import net.datatp.module.hr.entity.Employee;
import net.datatp.module.partner.PartnerLogic;
import net.datatp.module.partner.entity.Partner;
import net.datatp.module.resource.entity.OwnerType;
import net.datatp.module.resource.misc.BankAccountLogic;
import net.datatp.module.resource.misc.entity.BankAccount;
import net.datatp.module.storage.CompanyStorage;
import net.datatp.module.storage.IStorageService;
import net.datatp.util.dataformat.DataSerializer;
import net.datatp.util.ds.Arrays;
import net.datatp.util.ds.Collections;
import net.datatp.util.ds.Objects;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 */
abstract public class HouseBillLogic extends DAOService {

  @Autowired
  protected PartnerLogic partnerLogic;

  @Autowired
  protected BFSOnePartnerLogic bfsOnePartnerLogic;

  @Autowired
  protected CompanyLogic companyLogic;

  @Autowired
  protected UserLogic userLogic;

  @Autowired
  protected BankAccountLogic bankAccountLogic;

  @Autowired
  @Getter
  protected TrackingLogic trackingLogic;

  @Autowired
  protected TransportPlanLogic planLogic;

  @Autowired
  protected InvoiceLogic invLogic;

  @Autowired
  protected OrderProcessLogic orderProcessLogic;

  @Autowired
  protected POLogic poLogic;

  @Autowired
  protected BookingProcessLogic bpLogic;

  @Autowired
  protected CustomerTransportChargeInvoiceLogic chargeInvoiceLogic;

  @Autowired
  protected CompanyConfigLogic companyConfigLogic;

  @Autowired
  protected EmployeeLogic employeeLogic;

  @Autowired
  protected AccountLogic accountLogic;

  @Autowired
  protected IStorageService storageService;

  @Autowired
  private HouseBillAttachmentRepository attachmentRepo;

  @Autowired
  private CargoCalculator cargoCalculator;

  @Autowired
  protected BookingProcessCommodityLogic commodityLogic;

  @Autowired
  protected CommodityTypeLogic commodityTypeLogic;

  @Autowired
  protected SecurityLogic securityLogic;

  @Autowired
  protected BookingLogic bookingLogic;

  @Autowired
  protected SeqService seqService;

  @Autowired
  protected LogisticMgmtHBInvoicePlugin hbInvoicePlugin;

  @Autowired
  protected BookingProcessLogic processLogic;

  @Autowired
  protected InvoiceLinkFactory invoiceLinkFactory;

  abstract public <T extends HouseBill> T getHouseBill(ClientInfo client, Company company, Long id);

  abstract public <T extends HouseBill> T getHouseBill(ClientInfo client, Company company, String hbCode);

  protected <T extends HouseBill> List<T> findByMasterBill(ClientInfo client, Company company, Long mbId) {
    return new ArrayList<>();
  }

  ;

  public String genHouseBillCode(ClientInfo client, Company company, TransportationMode mode) {
    String seqName = "";
    String prefixConfig = "";
    if (Objects.isNull(mode) || TransportationMode.isUnknown(mode)) {
      seqName = CustomClearanceHouseBill.TABLE_NAME;
      prefixConfig = CustomClearanceHouseBill.CODE_PREFIX_CONFIG_NAME;
    } else if (TransportationMode.isSeaTransport(mode)) {
      seqName = SeaFreightHouseBill.TABLE_NAME;
      prefixConfig = SeaFreightHouseBill.CODE_PREFIX_CONFIG_NAME;
    } else if (TransportationMode.isAirTransport(mode)) {
      seqName = AirFreightHouseBill.TABLE_NAME;
      prefixConfig = AirFreightHouseBill.CODE_PREFIX_CONFIG_NAME;
    } else if (TransportationMode.isRailTransport(mode)) {
      seqName = RailFreightHouseBill.TABLE_NAME;
      prefixConfig = RailFreightHouseBill.CODE_PREFIX_CONFIG_NAME;
    } else if (TransportationMode.isTruckTransport(mode)) {
      seqName = TruckFreightHouseBill.TABLE_NAME;
      prefixConfig = TruckFreightHouseBill.CODE_PREFIX_CONFIG_NAME;
    }

    String defaultPrefix = company.getCode().toUpperCase(Locale.ROOT);
    if (Objects.isNull(mode) || TransportationMode.UNKNOWN.equals(mode)) defaultPrefix = defaultPrefix + "CC";
    else defaultPrefix = defaultPrefix + mode.getBfsAbb();
    CompanyConfig config = companyConfigLogic.getCompanyConfigByCompanyId(client, company.getId());
    String codePrefix = config.getAttributeAsString(prefixConfig, defaultPrefix);

    StringBuilder builder = new StringBuilder(codePrefix);
    builder.append(new SimpleDateFormat("yyMM").format(new Date()));
    if (StringUtil.isNotEmpty(seqName)) builder.append(String.format("%04d", seqService.nextSequence(seqName)));
    return builder.toString();
  }

  public QuickHouseBillModel newHouseBillModel(ClientInfo client, Company company, QuickHouseBillModel hbModel) {
    hbModel = Objects.ensureNotNull(hbModel, QuickHouseBillModel::new);
    QuickHouseBillModel clone = DataSerializer.JSON.clone(hbModel);
    clone.initialize();
    String houseBillCode = genHouseBillCode(client, company, clone.getMode());
    clone.setCode(houseBillCode);

    if (Objects.isNull(clone.getSaleEmployeeId()) && clone.getShipmentType().equals(ShipmentType.NOMINATED)) {
      Employee employee = employeeLogic.getEmployee(client, company, company.getAdminAccountLoginId());
      if (Objects.nonNull(employee)) {
        clone.setSaleEmployeeId(employee.getId());
        clone.setSaleLabel(company.getLabel());
      }
    }

    final QuickHouseBillCustomField customField = Objects.ensureNotNull(clone.getCustomField(), QuickHouseBillCustomField::new);
    if (Purpose.IMPORT.equals(clone.getPurpose())) {
      if (Objects.isNull(customField.getBankAccountNumber())) {
        List<BankAccount> companyRepBankAccounts =
            bankAccountLogic.findBankAccounts(null, OwnerType.Company, company.getId());
        BankAccount bankAcc = Collections.findFirst(companyRepBankAccounts, sel -> sel.getSwiftCode() != null);
        if (Objects.nonNull(bankAcc)) {
          customField.setBankAccountName(bankAcc.getAccountHolder());
          customField.setBankAccountNumber(bankAcc.getAccountNumber());
          customField.setBankAddress(bankAcc.getBankAddress());
        }
      }
      customField.setCustomPrintNotifyParty("SAME AS CONSIGNEE");

      if (Objects.isNull(customField.getCustomPrintDeliveryOrderContactNote())) {
        CompanyInfo companyInfo = companyLogic.getCompanyInfo(client, company.getCode());
        String deliveryContact = "";
        if (Objects.nonNull(companyInfo.getAddress()))
          deliveryContact = deliveryContact.concat(companyInfo.getAddress());
        if (Objects.nonNull(clone.getAssigneeLabel()))
          deliveryContact = deliveryContact.concat("\n" + clone.getAssigneeLabel());
        customField.setCustomPrintDeliveryOrderContactNote(deliveryContact);
      }
    } else {
      if (StringUtil.isEmpty(customField.getChargeCurrency())) customField.setChargeCurrency("USD");
      if (StringUtil.isEmpty(customField.getDeclaredValueForCarriage()))
        customField.setDeclaredValueForCarriage("N.V.D");
      if (StringUtil.isEmpty(customField.getDeclaredValueForCustoms())) customField.setDeclaredValueForCustoms("N.V.D");
      if (Objects.isNull(customField.getCustomPrintDocumentDate())) customField.setCustomPrintDocumentDate(new Date());
    }

    if (Objects.isNull(clone.getAssigneeEmployeeId())) {
      final Employee employee = employeeLogic.getEmployee(client, company, client.getRemoteUser());
      if (Objects.nonNull(employee)) {
        clone.setAssigneeEmployeeId(employee.getId());
        clone.setAssigneeLabel(employee.getLabel());
      }
    }
    Partner companyPartner = partnerLogic.getCompanyPartner(client, company);
    clone.setDefaultInvoicePartnerToId(companyPartner.getId());
    clone.setDefaultInvoicePartnerToFullName(companyPartner.getLabel());
    return clone;
  }

  public <T extends HouseBill> T updateMeasurement(ClientInfo client, Company company, T houseBill) {
    double weight = 0;
    double volume = 0;
    List<TrackableCargo> cargos = trackingLogic.findTrackableCargoByBPId(client, company, houseBill.getBookingProcessId());
    final String weightUnit = houseBill.getCargoWeightUnit();
    final String volumeUnit = houseBill.getCargoVolumeUnit();
    weight += cargoCalculator.calculateTotalGrossWeightTo(cargos, weightUnit);
    volume += cargoCalculator.calculateTotalVolumeTo(cargos, volumeUnit);
    houseBill.setCargoGrossWeight(weight);
    houseBill.setCargoVolume(volume);
    return houseBill;
  }

  protected void updateMasterBillMeasurement(HouseBill hb, MasterBill mb) {
    double mbOriginGrossWeight = mb.getGrossWeightInKGS();
    double mbOriginVolume = mb.getVolumeInCBM();
    double mbOriginCW = mb.getChargeableWeightInKGS();
    double hbGrossWeight = MassUnit.convert(hb.getCargoGrossWeight(), hb.getCargoWeightUnit(), MassUnit.KILOGRAM.getUnit());
    double hbCW = MassUnit.convert(hb.getCargoChargeableWeight(), hb.getCargoWeightUnit(), MassUnit.KILOGRAM.getUnit());
    double hbVolume = VolumeUnit.convert(hb.getCargoVolume(), hb.getCargoVolumeUnit(), VolumeUnit.CUBIC_METER.getUnit());
    mb.setGrossWeightInKGS(mbOriginGrossWeight + hbGrossWeight);
    mb.setVolumeInCBM(mbOriginVolume + hbVolume);
    mb.setChargeableWeightInKGS(mbOriginCW + hbCW);
  }

  private <T extends HouseBill> SqlQuery createSearchHouseBillQuery(ClientInfo client, Company company, Class<T> entity, SqlQueryParams params) {
    final Partner companyPartner = partnerLogic.getCompanyPartner(client, company);
    params.addParam("companyId", company.getId());
    params.addParam("company_partner_id", companyPartner.getId());
    String tableName = entity.getAnnotation(Table.class).name();

    String[] SEARCH_FIELDS = new String[]{"code", "consigneeLabel", "shipperLabel"};
    SqlQuery query = new SqlQuery().
        ADD_TABLE(new EntityTable(entity).selectAllFields())
        .JOIN(new Join("INNER JOIN", BookingProcess.class)
            .ON("id", entity, "bookingProcessId")
        )
        .JOIN(new Join("INNER JOIN", Booking.class)
            .ON("id", BookingProcess.class, "bookingId")
            .addSelectField("bookingCaseReference", "bookingCaseReference"))
        .JOIN(new Join("LEFT JOIN", InvoiceLink.class)
            .ON("entityId", entity, "id")
            .AND("linkName", "=", "'" + tableName + "'"))
        .JOIN(new Join("LEFT JOIN", Invoice.class)
            .addCustomSelectField("SUM( " +
                "CASE WHEN accounting_invoice.payer_partner_id = :company_partner_id " +
                "OR accounting_invoice.payee_partner_id = :company_partner_id " +
                "THEN " +
                "CASE accounting_invoice.cash_flow WHEN 'Inbound' " +
                "THEN accounting_invoice.domestic_total ELSE 0 END ELSE 0 END )", "revenue")
            .addCustomSelectField("SUM( " +
                "CASE " +
                "WHEN accounting_invoice.payee_partner_id = :company_partner_id AND accounting_invoice.cash_flow = 'Internal'" +
                "THEN accounting_invoice.domestic_total ELSE 0 END )", "internalRevenue")
            .addCustomSelectField("SUM( " +
                "CASE " +
                "WHEN accounting_invoice.payer_partner_id = :company_partner_id " +
                "OR accounting_invoice.payee_partner_id = :company_partner_id " +
                "THEN CASE accounting_invoice.cash_flow " +
                "WHEN 'Outbound' THEN accounting_invoice.domestic_total ELSE 0 END ELSE 0 END )", "expense")
            .addCustomSelectField("SUM( " +
                "CASE " +
                "WHEN accounting_invoice.payer_partner_id = :company_partner_id AND accounting_invoice.cash_flow = 'Internal'" +
                "THEN accounting_invoice.domestic_total ELSE 0 END )", "internalExpense")
            .addCustomSelectField("SUM( " +
                "CASE " +
                "WHEN accounting_invoice.payer_partner_id = :company_partner_id " +
                "OR accounting_invoice.payee_partner_id = :company_partner_id " +
                "THEN " +
                "CASE WHEN accounting_invoice.cash_flow = 'Inbound' " +
                "OR (accounting_invoice.cash_flow = 'Internal' " +
                "AND accounting_invoice.payee_partner_id = :company_partner_id) " +
                "THEN accounting_invoice.domestic_total WHEN accounting_invoice.cash_flow = 'Outbound' " +
                "OR (accounting_invoice.cash_flow = 'Internal' " +
                "AND accounting_invoice.payer_partner_id = :company_partner_id) " +
                "THEN -accounting_invoice.domestic_total ELSE 0 END ELSE 0 END)", "netProfit")
            .addCustomSelectField("SUM( " +
                "CASE " +
                "WHEN accounting_invoice.payer_partner_id != :company_partner_id " +
                "AND accounting_invoice.payee_partner_id != :company_partner_id " +
                "THEN accounting_invoice.domestic_total ELSE 0 END )", "onBehalf")
            .ON("id", InvoiceLink.class, "invoiceId"))
        .FILTER(ClauseFilter.company(entity))
        .FILTER(
            new ConditionFilter(entity, "masterBillId", "is null")
                .hasVariableCondition("excludeLinkMasterBill"))
        .FILTER(
            new ConditionFilter(entity, "masterBillId", "= :masterBillId")
                .hasVariableCondition("masterBillId"))
        .FILTER(SearchFilter.isearch(entity, SEARCH_FIELDS))
        .FILTER(
            OptionFilter.storageState(entity),
            OptionFilter.create(entity, "purpose", Purpose.ALL),
            RangeFilter.date(entity, "issuedDate"),
            RangeFilter.createdTime(entity),
            RangeFilter.modifiedTime(entity)).
        ORDERBY(new String[]{"issuedDate", "modifiedTime"}, "modifiedTime", "DESC");

    List<String> customFields = Arrays.asList("revenue", "expense", "netProfit", "onBehalf", "internalRevenue", "internalExpense");
    final Map<String, List<SelectField>> collect = query.getSelectFields()
        .stream()
        .filter(field -> !customFields.contains(field.getAlias()))
        .collect(Collectors.groupingBy(SelectField::getFieldExpression));
    query.GROUPBY(collect.keySet().toArray(new String[0]));
    return query;
  }

  public <T extends HouseBill> List<SqlMapRecord> searchHouseBill(
      ClientInfo client, Company company, Class<T> entity, SqlQueryParams params) {
    boolean moderator = securityLogic.hasModeratorPermission(client, company.getId(), "logistics", "logistics-managements");
    SqlQuery query = createSearchHouseBillQuery(client, company, entity, params);
    HashSet<SqlMapRecord> records = new HashSet<>();
    if (!moderator) {
      params.addParam("participant", client.getRemoteUser());
      records.addAll(searchHouseBillByAssignee(client, company, entity, params));
      records.addAll(searchHouseBillByPOFollower(client, company, entity, params));
      records.addAll(searchHouseBillByPOAssignee(client, company, entity, params));
      records.addAll(searchHouseBillByPOClient(client, company, entity, params));
    } else {
      return query(client, query, params).getSqlMapRecords();
    }
    return new ArrayList<>(records);
  }

  private <T extends HouseBill> List<SqlMapRecord> searchHouseBillByAssignee(
      ClientInfo client, Company company, Class<T> entity, SqlQueryParams params) {
    SqlQuery query = createSearchHouseBillQuery(client, company, entity, params)
        .JOIN(new Join("INNER JOIN", Employee.class).ON("id", entity, "assigneeEmployeeId"))
        .JOIN(new Join("INNER JOIN", Account.class).ON("id", Employee.class, "accountId"))
        .FILTER(new ClauseFilter(Account.class, "loginId", "=", ":participant"));

    return query(client, query, params).getSqlMapRecords();
  }

  private <T extends HouseBill> List<SqlMapRecord> searchHouseBillByPOFollower(
      ClientInfo client, Company company, Class<T> entity, SqlQueryParams params) {
    SqlQuery query = createSearchHouseBillQuery(client, company, entity, params)
        .JOIN(new Join("INNER JOIN", BookingProcess.class).ON("id", entity, "bookingProcessId"))
        .JOIN(new Join("INNER JOIN", PurchaseOrder.class).ON("id", BookingProcess.class, "purchaseOrderId"))
        .JOIN(new Join("INNER JOIN", PurchaseOrderFollower.class).ON("poId", PurchaseOrder.class, "id"))
        .JOIN(new Join("INNER JOIN", Account.class).ON("id", PurchaseOrderFollower.class, "followerAccountId"))
        .FILTER(new ClauseFilter(Account.class, "loginId", "=", ":participant"));

    return query(client, query, params).getSqlMapRecords();
  }

  private <T extends HouseBill> List<SqlMapRecord> searchHouseBillByPOAssignee(
      ClientInfo client, Company company, Class<T> entity, SqlQueryParams params) {
    SqlQuery query = createSearchHouseBillQuery(client, company, entity, params)
        .JOIN(new Join("INNER JOIN", BookingProcess.class).ON("id", entity, "bookingProcessId"))
        .JOIN(new Join("INNER JOIN", PurchaseOrder.class).ON("id", BookingProcess.class, "purchaseOrderId"))
        .JOIN(new Join("INNER JOIN", Employee.class).ON("id", PurchaseOrder.class, "assigneeEmployeeId"))
        .JOIN(new Join("INNER JOIN", Account.class).ON("id", Employee.class, "accountId"))
        .FILTER(new ClauseFilter(Account.class, "loginId", "=", ":participant"));
    return query(client, query, params).getSqlMapRecords();
  }

  private <T extends HouseBill> List<SqlMapRecord> searchHouseBillByPOClient(
      ClientInfo client, Company company, Class<T> entity, SqlQueryParams params) {
    SqlQuery query = createSearchHouseBillQuery(client, company, entity, params)
        .JOIN(new Join("INNER JOIN", BookingProcess.class).ON("id", entity, "bookingProcessId"))
        .JOIN(new Join("INNER JOIN", PurchaseOrder.class).ON("id", BookingProcess.class, "purchaseOrderId"))
        .JOIN(new Join("INNER JOIN", Partner.class).ON("id", PurchaseOrder.class, "clientPartnerId"))
        .JOIN(new Join("INNER JOIN", Account.class).ON("id", Partner.class, "accountId"))
        .FILTER(new ClauseFilter(Account.class, "loginId", "=", ":participant"));
    return query(client, query, params).getSqlMapRecords();
  }

  public List<HouseBillAttachment> findAttachments(ClientInfo client, Company company, Type type, String hbCode) {
    return attachmentRepo.findByHBCode(company.getId(), type, hbCode);
  }

  public List<HouseBillAttachment> uploadAttachments(
      ClientInfo client, Company company, Type type, String hbCode, List<HouseBillAttachment> attachments) {
    HouseBill hb = getHouseBill(client, company, hbCode);
    PurchaseOrder po = poLogic.getPOByBookingProcessId(client, company, hb.getBookingProcessId());
    Objects.assertNotNull(po, "PurchaseOrder is not found by BookingProcess {}", hb.getBookingProcessId());
    BookingProcess bp = bpLogic.getBookingProcess(client, company, hb.getBookingProcessId());
    Objects.assertNotNull(bp, "BookingProcess {} is not found", hb.getBookingProcessId());
    Booking booking = bookingLogic.getBooking(client, company, bp.getBookingId());
    Objects.assertNotNull(booking, "Booking {} is not found", bp.getBookingId());

    Partner poClient = partnerLogic.getById(client, company, po.getClientPartnerId());
    Objects.assertNotNull(poClient, "Partner {0} is not found", po.getClientLabel());
    String storagePath = HouseBillAttachment.getHBLogicalStoragePath(poClient.getLoginId(), booking.getBookingCaseReference());
    CompanyStorage storage = storageService.createCompanyStorage(client, company.getCode());
    storage.saveAttachments(storagePath, attachments, true);
    for (HouseBillAttachment attachment : attachments) {
      attachment.setHbCode(hbCode);
      attachment.setType(type);
      attachment.set(client, company.getId());
    }
    attachments = attachmentRepo.saveAll(attachments);
    List<Long> idSet = HouseBillAttachment.getIds(attachments);
    attachmentRepo.deleteOrphanHbCode(company.getId(), type, hbCode, idSet);
    return attachments;
  }

  public void genSalesman(ClientInfo client, Company company, HouseBill hb) {
    if (Objects.nonNull(hb.getSaleEmployeeId())) return;
    //TODO: Dan - fix this method
    if (ShipmentType.isNominated(hb.getShipmentType())) {
      Employee employee = getDefaultSaleman(client, company);
      hb.withSale(employee);
    } else {

      /*
      // logic need to put when set Booking
      Booking booking = bookingLogic.getByBookingProcessId(client, company, hb.getBookingProcessId());
      Objects.assertNotNull(booking, "Booking with case reference {0} is not found", hb.getBookingCaseReference());
      if (booking.getInquiry().getAssigneeEmployeeId() != null) {
        Employee employee = employeeLogic.getEmployee(client, company, booking.getInquiry().getAssigneeEmployeeId());
        if (employee != null) hb.withSale(employee);
      }
       */
    }
  }

  public Employee getDefaultSaleman(ClientInfo client, Company company) {
    Account account = accountLogic.getEditable(client, company.getAdminAccountLoginId());
    return employeeLogic.getByAccount(client, company, account.getId());
  }

  public Booking createBooking(ClientInfo client, Company company, QuickMasterBillModel model, QuickHouseBillModel hbModel) {
    Booking booking = new Booking();
    SpecificServiceInquiry inquiry = new SpecificServiceInquiry();
    inquiry.setClientPartnerId(hbModel.getClientPartnerId());
    inquiry.setClientLabel(hbModel.getClientLabel());
    inquiry.setSalemanLabel(hbModel.getAssigneeLabel());
    //inquiry.setSalemanEmployeeId(hbModel.getAssigneeEmployeeId());
    inquiry.setMode(hbModel.getMode());
    inquiry.setPurpose(hbModel.getPurpose());
    inquiry.setVolumeCbm(hbModel.getCargoVolume());
    inquiry.setChargeableWeight(hbModel.getCargoChargeableWeight());
    inquiry.setGrossWeightKg(hbModel.getCargoGrossWeight());
    inquiry.setReferenceCode(hbModel.getBookingCaseReference());
    if (Objects.nonNull(model)) {
      if (TransportationMode.isTruckTransport(model.getMode())) {
        booking.setBookingCaseReference(model.getCode());
      }
      inquiry.setFromLocationCode(model.getFromLocationCode());
      inquiry.setFromLocationLabel(model.getFromLocationLabel());
      inquiry.setToLocationCode(model.getToLocationCode());
      inquiry.setToLocationLabel(model.getToLocationLabel());
    }
    createContainerCargos(client, inquiry, hbModel);
    booking.setInquiry(inquiry);
    if (Objects.isNull(booking.getBookingCaseReference())) {
      booking.setBookingCaseReference(hbModel.getBookingCaseReference());
    }
    if (StringUtil.isNotBlank(model.getBookingNo())) {
      booking.setCode(model.getBookingNo());
    }
    booking.setBookingDate(hbModel.getIssuedDate());
    return bookingLogic.saveBooking(client, company, booking);
  }

  private Booking createBookingTransportCharges(Booking booking, QuickMasterBillModel mbModel, QuickHouseBillModel hbModel, Partner companyPartner) {
    if (TransportationMode.isAirTransport(hbModel.getMode())) {
      BookingAirTransportCharge charge = hbModel.createAirCharge(mbModel, companyPartner);
      booking.setCustomerAirTransportCharge(charge);
    } else if (TransportationMode.isRailTransport(hbModel.getMode())) {
      BookingRailTransportCharge charge = hbModel.createRailCharge(mbModel);
      booking.setCustomerRailTransportCharge(charge);
    } else if (TransportationMode.isSeaTransport(hbModel.getMode())) {
      BookingSeaTransportCharge charge = hbModel.createSeaCharge(mbModel);
      booking.setCustomerSeaTransportCharge(charge);
    }
    return booking;
  }

  private void createContainerCargos(ClientInfo client, SpecificServiceInquiry inquiry, QuickHouseBillModel hbModel) {
    List<QuickContainerModel> containerModels = hbModel.getContainerModels();
    int i = 1;
    for (QuickContainerModel sel : containerModels) {
      final String containerNo = sel.getContainerNo();
      if (Objects.nonNull(containerNo) && !TransportationMode.isAirTransport(hbModel.getMode())) {
        Container container = new Container();
        container.setContainerType(sel.getContainerType());
        container.setTotalCargoWeight(sel.getCargoGrossWeight());
        container.setCargoWeightUnit(sel.getCargoWeightUnit());
        inquiry.getContainers().add(container);
      }

      final double quantity = sel.getQuantity();
      if (quantity > 0) {
        Cargo cargo = new Cargo();
        cargo.setPackagingType(sel.getPackagingType());
        cargo.setQuantity(quantity);
        cargo.setVolume(sel.getCargoVolume() / quantity);
        cargo.setTotalVolume(sel.getCargoVolume());
        cargo.setVolumeUnit(sel.getCargoVolumeUnit());
        cargo.setGrossWeight(sel.getCargoGrossWeight() / quantity);
        cargo.setTotalGrossWeight(sel.getCargoGrossWeight());
        cargo.setGrossWeightUnit(sel.getCargoWeightUnit());
        //inquiry.getCargos().add(cargo);
        i++;
      }
    }
  }

  public PurchaseOrder createPurchaseOrder(ClientInfo client, Company company, QuickHouseBillModel hbModel) {
    PurchaseOrder purchaseOrder = new PurchaseOrder();
    purchaseOrder.setCode("PO" + DateUtil.asCompactDateId(new Date()) + seqService.nextSequence(PurchaseOrder.SEQUENCE));
    purchaseOrder.setLabel("PO for " + hbModel.getClientLabel());
    purchaseOrder.setAssigneeLabel(hbModel.getAssigneeLabel());
    purchaseOrder.setAssigneeEmployeeId(hbModel.getAssigneeEmployeeId());
    purchaseOrder.setClientLabel(hbModel.getClientLabel());
    purchaseOrder.setClientPartnerId(hbModel.getClientPartnerId());
    return poLogic.savePurchaseOrder(client, company, purchaseOrder);
  }

  public BookingProcess createBookingImportShipment(ClientInfo client, Company company, QuickMasterBillModel mbModel, QuickHouseBillModel hbModel) {
    Booking booking = createBooking(client, company, mbModel, hbModel);
    PurchaseOrder purchaseOrder = createPurchaseOrder(client, company, hbModel);
    //TODO: update only booking process id in booking entity instead
    processLogic.addBooking(client, company, purchaseOrder, booking);
    booking = bookingLogic.getBooking(client, company, booking.getId());
    BookingProcess bookingProcess = processLogic.getBookingProcess(client, company, booking.getBookingProcessId());
    bookingProcess.setClosedDate(hbModel.getClosedDate());
    if (Objects.nonNull(mbModel)) {
      bookingProcess.setInputSource(mbModel.getInputSource());
      if (mbModel.isBFSOneInputSource()) bookingProcess.setState(FileState.CLOSED);
      bookingProcess.setIgnoreSync(mbModel.isIgnoreSync());
    }
    processLogic.saveBookingProcess(client, company, bookingProcess);
    //TODO: Nhat - remove this code after resolve bug in logic convert container
    trackingLogic.deleteTrackableCargoByBPId(client, company, bookingProcess.getId());
    String caseReference = hbModel.getBookingCaseReference();
    if (mbModel != null && mbModel.getCode() != null) caseReference = mbModel.getCode();
    processLogic.computeContainerModel(client, company, hbModel.getContainerModels(), bookingProcess, caseReference);

    //TODO: move this code into createBooking
    final Partner companyPartner = partnerLogic.getCompanyPartner(client, company);
    createBookingTransportCharges(booking, mbModel, hbModel, companyPartner);
    bookingLogic.saveBooking(client, company, booking);
    return bookingProcess;
  }

  public void createHouseBillInvoice(ClientInfo client, Company company, HouseBill hb, List<InvoiceItemModel> invItems) {
    if (Collections.isEmpty(invItems)) return;
    Invoice invTemplate = new Invoice();
    if (Collections.isEmpty(invTemplate.getLinks())) invTemplate.setLinks(new ArrayList<>());
    invoiceLinkFactory.updateLinksByHouseBill(client, company, invTemplate.getLinks(), hb);
    BookingProcess bp = bpLogic.getBookingProcess(client, company, hb.getBookingProcessId());
    Objects.assertNotNull(bp, "BookingProcess {} is not found", hb.getBookingProcessId());
    Booking booking = bookingLogic.getBooking(client, company, bp.getBookingId());
    Objects.assertNotNull(booking, "Booking {} is not found", bp.getBookingId());

    invTemplate.setInternalCaseReference(hb.getCode());
    invTemplate = hbInvoicePlugin.newInvoice(client, company, invTemplate);
    createInvoice(client, company, hb, invItems, invTemplate);
  }

  private void createInvoice(ClientInfo client, Company company, HouseBill hb, List<InvoiceItemModel> invItems, Invoice invoiceTemplate) {
    final InvoiceItemProcessor itemProcessor = new InvoiceItemProcessor(invItems);
    final Partner companyPartner = partnerLogic.getCompanyPartner(client, company);
    final InvoicePartnerMap companyInvoiceMap = new InvoicePartnerMap(invoiceTemplate);
    List<Invoice> companyInvoices = companyInvoiceMap.mapInvoices(partnerLogic, client, company, itemProcessor.getCompanyItems());
    for (Invoice inv : companyInvoices) {
      if (CashFlow.isOutbound(inv.getCashflow())) {
        inv.withPayer(companyPartner);
        inv.setPayeePartnerId(inv.getPartnerId());
        inv.setPayeePartnerName(inv.getPartnerLabel());
      } else if (CashFlow.isInbound(inv.getCashflow())) {
        inv.withPayee(companyPartner);
        inv.setPayerPartnerId(inv.getPartnerId());
        inv.setPayerPartnerName(inv.getPartnerLabel());
      }
      inv = inv.withGenLabel(true);
      invLogic.saveInvoice(client, company, inv);
    }

    for (Long pobPartnerId : itemProcessor.getPayOnBehalfItemMap().keySet()) {
      final Partner pobPartner = Optional.ofNullable(partnerLogic.getById(client, company, pobPartnerId))
          .orElseThrow(() -> new IllegalArgumentException("Pay On Behalf Partner must be not null"));
      invoiceTemplate.setLabel("Pay on Behalf");
      final InvoicePartnerMap invoicePartnerMap = new InvoicePartnerMap(invoiceTemplate);
      List<Invoice> otherInvoices = invoicePartnerMap
          .mapInvoices(partnerLogic, client, company, itemProcessor.getPayOnBehalfItemMap().get(pobPartnerId));
      for (Invoice inv : otherInvoices) {
        Long seq = seqService.nextSequence("accounting:invoice");
        if (CashFlow.isOutbound(inv.getCashflow())) {
          inv.withPayer(pobPartner);
        } else if (CashFlow.isInbound(inv.getCashflow())) {
          inv.withPayee(pobPartner);
        }
        invLogic.saveInvoice(client, company, inv);
      }
    }
  }

  protected void updateInvoiceCaseReference(ClientInfo client, Company company, String tableName, Long entityId, String caseRef) {
    List<Invoice> hbInv = invLogic.findInvoiceByLinkEntityId(client, company, tableName, entityId);
    for (Invoice inv : hbInv) {
      inv.setCaseReference(caseRef);
    }
  }

  public <T extends MasterBill, U extends HouseBill> Booking updateBookingCaseReference(ClientInfo client, Company company, T masterBill, Long bookingId) {
    if (bookingId == null) return null;
    List<U> houseBills = findByMasterBill(client, company, masterBill.getId());
    int seq = houseBills.size();
    String caseReference = masterBill.getCode() + "/" + seq;
    caseReference = recheckBookingCaseReferece(client, company, caseReference, masterBill.getCode(), seq);
    Booking booking = bookingLogic.getBooking(client, company, bookingId);
    if (booking.getBookingCaseReference() != null) return booking;
    booking.setBookingCaseReference(caseReference);
    booking.getInquiry().setReferenceCode(caseReference);
    return bookingLogic.saveBooking(client, company, booking);
  }

  protected <T extends HouseBill> String genBookingCaseRefereceForNewHBL(ClientInfo client, Company company, String mbCode, Long masterBillId) {
    List<T> houseBills = findByMasterBill(client, company, masterBillId);
    int seq = houseBills.size();
    String caseReference = mbCode + "/" + (seq + 1);
    caseReference = recheckBookingCaseReferece(client, company, caseReference, mbCode, seq);
    return caseReference;
  }

  protected String recheckBookingCaseReferece(ClientInfo client, Company company, String caseReference, String mbCode, int size) {
    Booking bookingExist = bookingLogic.getBookingByCaseRef(client, company, caseReference);
    if (bookingExist != null) {
      for (int i = 0; i < size; i++) {
        caseReference = mbCode + "/" + (i + 1);
        bookingExist = bookingLogic.getBookingByCaseRef(client, company, caseReference);
        if (bookingExist == null) break;
      }
    }
    return caseReference;
  }

  protected void unlinkBooking(ClientInfo client, Company company, Long bookingProsessId) {
    BookingProcess bookingProcess = bpLogic.getBookingProcess(client, company, bookingProsessId);
    Booking booking = bookingLogic.getBooking(client, company, bookingProcess.getBookingId());
    booking.removeCaseReference();
    booking = bookingLogic.saveBooking(client, company, booking);
  }
}