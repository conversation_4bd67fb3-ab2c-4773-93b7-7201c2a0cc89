package cloud.datatp.fforwarder.mgmt.po;


import cloud.datatp.fforwarder.mgmt.BookingProcessCommodityLogic;
import cloud.datatp.fforwarder.mgmt.FileState;
import cloud.datatp.fforwarder.mgmt.QuickBookingProcessModel;
import cloud.datatp.fforwarder.mgmt.QuickHouseBillModel;
import cloud.datatp.fforwarder.mgmt.QuickHouseBillModel.QuickContainerModel;
import cloud.datatp.fforwarder.mgmt.TrackingLogic;
import cloud.datatp.fforwarder.mgmt.TransportPlanLogic;
import cloud.datatp.fforwarder.mgmt.air.AirHouseBillLogic;
import cloud.datatp.fforwarder.mgmt.customclearance.CustomClearanceHBLogic;
import cloud.datatp.fforwarder.mgmt.customclearance.entity.CustomClearanceHouseBill;
import cloud.datatp.fforwarder.mgmt.entity.BookingProcessCommodity;
import cloud.datatp.fforwarder.mgmt.entity.OrderTransportPlan;
import cloud.datatp.fforwarder.mgmt.entity.TrackableCargo;
import cloud.datatp.fforwarder.mgmt.entity.TrackableContainer;
import cloud.datatp.fforwarder.mgmt.entity.TransportPlan;
import cloud.datatp.fforwarder.mgmt.po.entity.BookingProcess;
import cloud.datatp.fforwarder.mgmt.po.entity.PurchaseOrder;
import cloud.datatp.fforwarder.mgmt.po.entity.PurchaseOrderFollower;
import cloud.datatp.fforwarder.mgmt.po.repository.BookingProcessRepository;
import cloud.datatp.fforwarder.mgmt.rail.RailHouseBillLogic;
import cloud.datatp.fforwarder.mgmt.sea.SeaHouseBillLogic;
import cloud.datatp.fforwarder.mgmt.truck.TruckHouseBillLogic;
import cloud.datatp.fforwarder.mgmt.truck.entity.TruckFreightHouseBill;
import cloud.datatp.fforwarder.sales.booking.BookingLogic;
import cloud.datatp.fforwarder.sales.booking.entity.Booking;
import cloud.datatp.fforwarder.sales.booking.entity.BookingCustomClearance;
import cloud.datatp.fforwarder.sales.booking.entity.BookingTruckTransportCharge;
import cloud.datatp.fforwarder.sales.inquiry.entity.InquiryCommodity;
import cloud.datatp.fforwarder.sales.inquiry.entity.SpecificServiceInquiry;
import cloud.datatp.fforwarder.settings.Purpose;
import cloud.datatp.fforwarder.settings.TransportationMode;
import cloud.datatp.fforwarder.settings.commodity.CommodityTypeLogic;
import cloud.datatp.fforwarder.settings.commodity.entity.CommodityType;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import net.datatp.module.account.entity.Account;
import net.datatp.module.accounting.entity.Invoice;
import net.datatp.module.accounting.entity.InvoiceLink;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.company.entity.Company;
import net.datatp.module.core.security.SecurityLogic;
import net.datatp.module.data.db.DAOService;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.EditMode;
import net.datatp.module.data.db.query.ClauseFilter;
import net.datatp.module.data.db.query.ConditionFilter;
import net.datatp.module.data.db.query.EntityTable;
import net.datatp.module.data.db.query.Join;
import net.datatp.module.data.db.query.OptionFilter;
import net.datatp.module.data.db.query.RangeFilter;
import net.datatp.module.data.db.query.SearchFilter;
import net.datatp.module.data.db.query.SelectField;
import net.datatp.module.data.db.query.SqlQuery;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.hr.EmployeeLogic;
import net.datatp.module.hr.entity.Employee;
import net.datatp.module.partner.PartnerLogic;
import net.datatp.module.partner.entity.Partner;
import net.datatp.util.dataformat.DataSerializer;
import net.datatp.util.ds.Arrays;
import net.datatp.util.ds.Collections;
import net.datatp.util.ds.Objects;
import net.datatp.util.error.ErrorType;
import net.datatp.util.error.RuntimeError;
import net.datatp.util.text.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class BookingProcessLogic extends DAOService {
  @Autowired
  private BookingProcessRepository bpRepo;

  @Getter
  @Autowired
  private BookingLogic bookingLogic;

  @Autowired @Getter
  private TrackingLogic trackingLogic;

  @Autowired
  private TransportPlanLogic transportPlanLogic;

  @Autowired
  private OrderProcessLogic opLogic;

  @Autowired
  protected CommodityTypeLogic commodityTypeLogic;

  @Autowired
  private SeaHouseBillLogic seaHouseBillLogic;

  @Autowired
  private AirHouseBillLogic airHouseBillLogic;

  @Autowired
  private RailHouseBillLogic railHouseBillLogic;

  @Autowired
  private TruckHouseBillLogic truckHBLogic;

  @Autowired
  private CustomClearanceHBLogic customClearanceHBLogic;

  @Autowired
  private POLogic poLogic;

  @Autowired
  private SecurityLogic securityLogic;

  @Autowired
  private EmployeeLogic employeeLogic;

  @Autowired
  private PartnerLogic partnerLogic;

  @Autowired
  private BookingProcessCommodityLogic commodityLogic;

  @Autowired
  private OrderProcessLogic orderProcessLogic;

  @Autowired
  private TransportPlanLogic planLogic;

  public BookingProcess getBookingProcess(ClientInfo client, Company company, String code) {
    return bpRepo.getByCode(company.getId(), code);
  }

  public BookingProcess getBookingProcess(ClientInfo client, Company company, Long id) {
    return bpRepo.getById(company.getId(), id);
  }

  public BookingProcess saveBookingProcess(ClientInfo client, Company company, BookingProcess bp) {
    bp.set(client, company);
    return bpRepo.save(bp);
  }

  public List<BookingProcess> saveBookingProcess(ClientInfo clientInfo, Company company, List<BookingProcess> processes) {
    List<BookingProcess> holder = new ArrayList<>();
    for (BookingProcess bp : processes) {
      BookingProcess processInDb = getBookingProcess(clientInfo, company, bp.getId());
      processInDb.setLabel(bp.getLabel());
      processInDb.setDescription(bp.getDescription());
      processInDb.setEditMode(bp.getEditMode());
      processInDb.setClosedDate(bp.getClosedDate());
      processInDb.setIgnoreSync(bp.isIgnoreSync());
      BookingProcess updated = saveBookingProcess(clientInfo, company, processInDb);
      holder.add(updated);
    }
    return holder;
  }

  public BookingProcess updateBookingProcess(ClientInfo client, Company company, BookingProcess process) {
    if (process.isNew()) throw new RuntimeError(ErrorType.IllegalArgument, "Cannot create a new BookingProcess");
    return saveBookingProcess(client, company, process);
  }

  public List<BookingProcess> findByPOId(ClientInfo client, Company company, Long poId) {
    return bpRepo.findByPOId(company.getId(), poId);
  }

  private SqlQuery createSearchBookingProcessesQuery(ClientInfo client, Company company, SqlQueryParams params) {
    final Partner companyPartner = partnerLogic.getCompanyPartner(client, company);
    params.addParam("companyId",  company.getId());
    params.addParam("company_partner_id",  companyPartner.getId());

    String[] SEARCH_FIELDS = new String[] {"code", "label"};
    SqlQuery query =  new SqlQuery()
      .ADD_TABLE(new EntityTable(BookingProcess.class).selectAllFields())
      .JOIN(new Join("LEFT JOIN", PurchaseOrder.class)
        .addSelectField("assigneeLabel", "assigneeLabel")
        .addSelectField("assigneeEmployeeId", "assigneeEmployeeId")
        .addSelectField("clientLabel", "clientLabel")
        .addSelectField("clientPartnerId", "clientPartnerId")
        .addSelectField("code", "purchaseOrderCode")
        .ON("id", BookingProcess.class, "purchaseOrderId"))
      .JOIN(new Join("LEFT JOIN", Booking.class)
        .addSelectField("bookingCaseReference", "bookingCaseReference")
        .addSelectField("code", "bookingCode")
        .ON("id", BookingProcess.class, "bookingId"))
      .JOIN(new Join("LEFT JOIN", InvoiceLink.class)
        .ON("entityId", BookingProcess.class, "id")
        .AND("linkName", "=", "'" + BookingProcess.TABLE_NAME + "'"))
      .JOIN(new Join("LEFT JOIN", Invoice.class)
        .addCustomSelectField("SUM( " +
          "CASE WHEN accounting_invoice.payer_partner_id = :company_partner_id " +
          "OR accounting_invoice.payee_partner_id = :company_partner_id " +
          "THEN " +
          "CASE accounting_invoice.cash_flow WHEN 'Inbound' " +
          "THEN accounting_invoice.domestic_total ELSE 0 END ELSE 0 END )", "revenue")
        .addCustomSelectField("SUM( " +
          "CASE " +
          "WHEN accounting_invoice.payee_partner_id = :company_partner_id AND accounting_invoice.cash_flow = 'Internal'" +
          "THEN accounting_invoice.domestic_total ELSE 0 END )", "internalRevenue")
        .addCustomSelectField("SUM( " +
          "CASE " +
          "WHEN accounting_invoice.payer_partner_id = :company_partner_id " +
          "OR accounting_invoice.payee_partner_id = :company_partner_id " +
          "THEN CASE accounting_invoice.cash_flow " +
          "WHEN 'Outbound' THEN accounting_invoice.domestic_total ELSE 0 END ELSE 0 END )", "expense")
        .addCustomSelectField("SUM( " +
          "CASE " +
          "WHEN accounting_invoice.payer_partner_id = :company_partner_id AND accounting_invoice.cash_flow = 'Internal'" +
          "THEN accounting_invoice.domestic_total ELSE 0 END )", "internalExpense")
        .addCustomSelectField("SUM( " +
          "CASE " +
          "WHEN accounting_invoice.payer_partner_id = :company_partner_id " +
          "OR accounting_invoice.payee_partner_id = :company_partner_id " +
          "THEN " +
          "CASE WHEN accounting_invoice.cash_flow = 'Inbound' " +
          "OR (accounting_invoice.cash_flow = 'Internal' " +
          "AND accounting_invoice.payee_partner_id = :company_partner_id) " +
          "THEN accounting_invoice.domestic_total WHEN accounting_invoice.cash_flow = 'Outbound' " +
          "OR (accounting_invoice.cash_flow = 'Internal' " +
          "AND accounting_invoice.payer_partner_id = :company_partner_id) " +
          "THEN -accounting_invoice.domestic_total ELSE 0 END ELSE 0 END)", "netProfit")
        .addCustomSelectField("SUM( " +
          "CASE " +
          "WHEN accounting_invoice.payer_partner_id != :company_partner_id " +
          "AND accounting_invoice.payee_partner_id != :company_partner_id " +
          "THEN accounting_invoice.domestic_total ELSE 0 END )", "onBehalf")
        .ON("id", InvoiceLink.class, "invoiceId"))
      .FILTER(ClauseFilter.company(BookingProcess.class))
      .FILTER(SearchFilter.isearch(BookingProcess.class, SEARCH_FIELDS))
      .FILTER(new ConditionFilter(BookingProcess.class, "purchaseOrderId", "= :poId").hasVariableCondition("poId"))
      .FILTER(
        OptionFilter.storageState(BookingProcess.class),
        RangeFilter.date(BookingProcess.class,"closedDate"),
        RangeFilter.createdTime(BookingProcess.class),
        RangeFilter.modifiedTime(BookingProcess.class))
      .ORDERBY(new String[]{"closedDate", "mode"}, "closedDate", "DESC");

    List<String> customFields = Arrays.asList("revenue", "expense", "netProfit", "onBehalf", "internalRevenue", "internalExpense");
    final Map<String, List<SelectField>> collect = query.getSelectFields()
      .stream()
      .filter(field -> !customFields.contains(field.getAlias()))
      .collect(Collectors.groupingBy(SelectField::getFieldExpression));
    query.GROUPBY(collect.keySet().toArray(new String[0]));
    return query;
  }

  public List<SqlMapRecord> searchBookingProcesses(ClientInfo client, Company company, SqlQueryParams params) {
    boolean moderator = securityLogic.hasModeratorPermission(client, company.getId(), "logistics", "logistics-managements");
    SqlQuery query = createSearchBookingProcessesQuery(client, company, params);
    List<SqlMapRecord> records = new ArrayList<>();
    if(!moderator) {
      params.addParam("participant", client.getRemoteUser());
      records.addAll(searchBookingProcessesByPOFollower(client, company, params));
      records.addAll(searchBookingProcessesByPOAssignee(client, company, params));
      records.addAll(searchBookingProcessesByPOClient(client, company, params));
    } else {
      records.addAll(query(client, query, params).getSqlMapRecords());
    }
    Set<Long> ids = new HashSet<>();
    List<SqlMapRecord> result = new ArrayList<>();
    if (Collections.isNotEmpty(records)) {
      for (SqlMapRecord record : records) {
        Long id = record.getLong("id");
        if (id != null) {
          if (!ids.contains(id)) result.add(record);
          ids.add(id);
        }
      }
    }
    return result;
  }

  private List<SqlMapRecord> searchBookingProcessesByPOFollower(ClientInfo client, Company company, SqlQueryParams params) {
    SqlQuery query = createSearchBookingProcessesQuery(client, company, params)
      .JOIN(new Join("INNER JOIN", PurchaseOrderFollower.class).ON("poId", PurchaseOrder.class, "id"))
      .JOIN(new Join("INNER JOIN", Account.class).ON("id", PurchaseOrderFollower.class, "followerAccountId"))
      .FILTER(new ClauseFilter(Account.class, "loginId", "=", ":participant"));

    return query(client, query, params).getSqlMapRecords();
  }

  private List<SqlMapRecord> searchBookingProcessesByPOAssignee(ClientInfo client, Company company, SqlQueryParams params) {
    SqlQuery query = createSearchBookingProcessesQuery(client, company, params)
      .JOIN(new Join("INNER JOIN", Employee.class).ON("id", PurchaseOrder.class, "assigneeEmployeeId"))
      .JOIN(new Join("INNER JOIN", Account.class).ON("id", Employee.class, "accountId"))
      .FILTER(new ClauseFilter(Account.class, "loginId", "=", ":participant"));
    return query(client, query, params).getSqlMapRecords();
  }

  private List<SqlMapRecord> searchBookingProcessesByPOClient(ClientInfo client, Company company, SqlQueryParams params) {
    SqlQuery query = createSearchBookingProcessesQuery(client, company, params)
      .JOIN(new Join("INNER JOIN", Partner.class).ON("id", PurchaseOrder.class, "clientPartnerId"))
      .JOIN(new Join("INNER JOIN", Account.class).ON("id", Partner.class, "accountId"))
      .FILTER(new ClauseFilter(Account.class, "loginId", "=", ":participant"));
    return query(client, query, params).getSqlMapRecords();
  }

  public BookingProcess createBookingProcess(ClientInfo client, Company company, PurchaseOrder po, Booking booking) {
    Long clientPartnerIdPo = po.getClientPartnerId();
    SpecificServiceInquiry inquiry = booking.getInquiry();
    Long clientPartnerIdInquiry = inquiry.getClientPartnerId();
    if (clientPartnerIdPo == null) throw new RuntimeError(ErrorType.IllegalState, "Client must be not null");
    if (clientPartnerIdInquiry == null) throw new RuntimeError(ErrorType.IllegalState, "Client's Inquiry must be not null");
    if( clientPartnerIdPo.longValue() != clientPartnerIdInquiry.longValue()) {
      throw new RuntimeError(ErrorType.IllegalState, "Client must be the same on client's Inquiry");
    }
    BookingProcess bp = saveBookingProcess(client, company, new BookingProcess(po, booking));
    booking.setBookingProcessId(bp.getId());
    bookingLogic.saveBooking(client, company, booking);
    if(Objects.nonNull(bp.getMode())) transportPlanLogic.saveTransportPlan(client, company, new TransportPlan(bp));
    //trackingLogic.createTrackableCargos(client, company, bp, inquiry.getCargos());
    return bp;
  }

  public PurchaseOrder addBooking(ClientInfo client, Company company, PurchaseOrder po, Booking booking) {
    po.addFollower(employeeLogic, partnerLogic, client, company, booking);
    po = poLogic.savePurchaseOrder(client, company, po);
    BookingProcess bookingProcess = createBookingProcess(client, company, po, booking);
    //TODO: review code
    opLogic.createOrderProcesses(client, company, new BPToOPInstruction(bookingProcess, booking));
    return po;
  }

  public PurchaseOrder newBookingProcess(ClientInfo client, Company company, PurchaseOrder po, Booking booking) {
    Booking savedBooking = bookingLogic.saveBooking(client, company, booking);
    return addBooking(client, company, po, savedBooking);
  }

  public void deleteAllProcess(ClientInfo client, Company company, Long bookingProcessId) {
    final Long purchaseOrderId = delete(client, company, bookingProcessId);

    final List<Long> idsByPOId = bpRepo.findIdsByPOId(company.getId(), purchaseOrderId);
    if(Collections.isEmpty(idsByPOId)) poLogic.deletePurchaseOrderByIds(client, company, Arrays.asList(purchaseOrderId));
  }

  private Long delete(ClientInfo client, Company company, Long bookingProcessId) {
    BookingProcess bp = bpRepo.findById(bookingProcessId).get();
    final Long purchaseOrderId = bp.getPurchaseOrderId();
    Objects.assertTrue(EditMode.isDraft(bp.getEditMode()), "Booking Process must be Draft, case reference = {}", bp.getCode());
    bookingLogic.deleteBookings(client, company, Arrays.asList(bp.getBookingId()));
    final TransportPlan plan = transportPlanLogic.getByBPId(client, company, bookingProcessId);
    if(Objects.isNull(plan)) log.warn("Transport Plan not found with booking process {}", bp.getCode());
    else transportPlanLogic.getTransportPlanRepo().delete(plan);

    opLogic.deleteByBookingProcessId(client, company, bp.getId());
    commodityLogic.deleteByBPId(client, company, bp.getId());
    trackingLogic.deleteTrackableCargoByBPId(client, company, bp.getId());
    bpRepo.delete(bp);
    return purchaseOrderId;
  }

  public void deleteByPurchaseOrderId(ClientInfo client, Company company, Long purchaseOrderId) {
    final List<Long> idsByPOId = bpRepo.findIdsByPOId(company.getId(), purchaseOrderId);
    for (Long id: idsByPOId) {
      deleteAllProcess(client, company, id);
    }
  }

  public Boolean delete(ClientInfo client, Company company, List<Long> ids) {
    Collections.apply(ids, (id) -> deleteAllProcess(client, company, id));
    return true;
  }

  public List<BookingProcess> findByCompany(ClientInfo client, Company company) {
    return bpRepo.findByCompanyId(company.getId());
  }

  public QuickBookingProcessModel newBookingProcessModel(ClientInfo client, Company company, QuickBookingProcessModel model) {
    model = Objects.ensureNotNull(model, QuickBookingProcessModel::new);
    QuickBookingProcessModel clone = DataSerializer.JSON.clone(model);
    final Employee employee = employeeLogic.getEmployee(client, company, client.getRemoteUser());
    clone.setAssigneeLabel(employee.getLabel());
    clone.setAssigneeEmployeeId(employee.getId());

    if(Objects.isNull(clone.getPurpose())) clone.setPurpose(Purpose.DOMESTIC);
    final TransportationMode mode = clone.getMode();
    final boolean isTruckingOrCustom = TransportationMode.isTruckTransport(mode) || TransportationMode.isUnknown(mode);
    Objects.assertTrue(isTruckingOrCustom, "Must be Trucking or Custom Clearance");
    final String bookingCaseReference = bookingLogic.genBookingCode(client, company);
    clone.setBookingCaseReference(bookingCaseReference);
    clone.setClosedDate(new Date());
    QuickHouseBillModel hbModel = new QuickHouseBillModel();
    Employee companyEmployee = employeeLogic.getEmployee(client, company, company.getAdminAccountLoginId());
    if (Objects.nonNull(companyEmployee)) {
      hbModel.setSaleEmployeeId(companyEmployee.getId());
      hbModel.setSaleLabel(company.getLabel());
    }
    hbModel.setBookingCaseReference(bookingCaseReference);
    if(Objects.isNull(hbModel.getPurpose())) hbModel.setPurpose(Purpose.DOMESTIC);
    hbModel.setMode(mode);
    hbModel.setType(clone.getType());
    hbModel = newBookingProcessHouseBillModel(client, company, hbModel);
    clone.withHouseBill(hbModel);
    return clone;
  }

  public QuickHouseBillModel newBookingProcessHouseBillModel(ClientInfo client, Company company, QuickHouseBillModel hbModel) {
    if(TransportationMode.isTruckTransport(hbModel.getMode())) {
      hbModel = truckHBLogic.newHouseBillModel(client, company, hbModel);
    } else if(TransportationMode.isUnknown(hbModel.getMode()) || Objects.nonNull(hbModel.getType())) {
      hbModel = customClearanceHBLogic.newHouseBillModel(client, company, hbModel);
    }
    return hbModel;
  }

  public BookingProcess createBookingProcessModel(ClientInfo client, Company company, QuickBookingProcessModel model) {
    PurchaseOrder purchaseOrder;
    if(Objects.nonNull(model.getPurchaseOrderId())) {
      purchaseOrder = poLogic.getPurchaseOrder(client, company, model.getPurchaseOrderId());
      Objects.assertNotNull(purchaseOrder, "Purchase Order not found");
    } else {
      purchaseOrder = model.computePurchaseOrder();
      purchaseOrder = poLogic.savePurchaseOrder(client, company, purchaseOrder);
    }
    if (Collections.isNotEmpty(model.getHouseBills())) model.setBookingCaseReference(model.getHouseBills().get(0).getBookingCaseReference());
    model.setClientLabel(purchaseOrder.getClientLabel());
    model.setClientPartnerId(purchaseOrder.getClientPartnerId());
    Booking booking = model.computeBooking();
    createInquiryCommodity(client, company, booking, model);
    booking = bookingLogic.saveBooking(client, company, booking);
    Long clientPartnerIdPo = purchaseOrder.getClientPartnerId();
    SpecificServiceInquiry inquiry = booking.getInquiry();
    Long clientPartnerIdInquiry = inquiry.getClientPartnerId();
    if (clientPartnerIdPo == null) throw new RuntimeError(ErrorType.IllegalState, "Client must be not null");
    if (clientPartnerIdInquiry == null) throw new RuntimeError(ErrorType.IllegalState, "Client's Inquiry must be not null");
    if(clientPartnerIdPo.longValue() != clientPartnerIdInquiry.longValue()) {
      throw new RuntimeError(ErrorType.IllegalState, "Client must be the same on client's Inquiry");
    }
    BookingProcess bp = saveBookingProcess(client, company, new BookingProcess(purchaseOrder, booking));
    if(Objects.nonNull(bp.getMode())) transportPlanLogic.saveTransportPlan(client, company, new TransportPlan(bp));
    computeContainerModel(client, company, model.getContainerModels(), bp, booking.getBookingCaseReference());
    booking.setBookingProcessId(bp.getId());
    List<BookingTruckTransportCharge> truckCharges = new ArrayList<>();
    List<BookingCustomClearance> customClearances = new ArrayList<>();
    for (QuickHouseBillModel hbModel: model.getHouseBills()) {
      if (TransportationMode.isTruckTransport(hbModel.getMode())) {
        BookingTruckTransportCharge truckCharge = hbModel.createTruckCharge();
        truckCharges.add(truckCharge);
        TruckFreightHouseBill houseBill = new TruckFreightHouseBill();
        houseBill.withBookingProcess(bp);
        hbModel.mapTruckHouseBill(houseBill);
        houseBill = orderProcessLogic.createTruckOrderProcess(client, company, houseBill);
        OrderTransportPlan orderTransportPlan = planLogic.getOrderTransportPlanByOPId(client, company, houseBill.getOrderProcessId());
        hbModel.mapTruckPlan(houseBill, orderTransportPlan);
        planLogic.saveOrderTransportPlan(client, company, orderTransportPlan);
        truckHBLogic.createHouseBillInvoice(client, company, houseBill, hbModel.getInvoiceItems());
      } else if(TransportationMode.isUnknown(model.getMode()) || Objects.nonNull(model.getType())) {
        BookingCustomClearance customClearance = hbModel.createCustomClearance();
        customClearances.add(customClearance);
        CustomClearanceHouseBill houseBill = new CustomClearanceHouseBill();
        houseBill.withBookingProcess(bp);
        hbModel.mapCustomClearanceHouseBill(houseBill);
        houseBill = orderProcessLogic.createClearanceOrderProcess(client, company, houseBill);
        customClearanceHBLogic.createHouseBillInvoice(client, company, houseBill, hbModel.getInvoiceItems());
      }

      createBookingProcessCommodity(client, company, bp, hbModel);
    }
    if (Collections.isNotEmpty(truckCharges)) booking.getCustomerTruckTransportCharges().addAll(truckCharges);
    if (Collections.isNotEmpty(customClearances)) booking.getCustomerCustomClearances().addAll(customClearances);
    bookingLogic.saveBooking(client, company, booking);
    return bp;
  }

  private void createBookingProcessCommodity(ClientInfo client, Company company, BookingProcess bp, QuickHouseBillModel hbModel) {
    BookingProcessCommodity commodity = new BookingProcessCommodity().withBookingProcess(bp);
    commodity.setDescription(hbModel.getDescriptionsOfGoods());
    if (StringUtil.isNotEmpty(hbModel.getCommodities())) {
      String[] commodityTypes = hbModel.getCommodities().split(",");
      for (String hsCode : commodityTypes) {
        CommodityType commodityType = commodityTypeLogic.getCommodityTypeByHsCode(client, company, hsCode.trim());
        if (Objects.nonNull(commodityType)) {
          commodity.getTypes().add(commodityType);
        }
      }
    }
    commodityLogic.saveCommodity(client, company, commodity);
  }

  private void createInquiryCommodity(ClientInfo client, Company company, Booking booking, QuickBookingProcessModel model) {
    if (Collections.isEmpty(model.getHouseBills())) return;
    for (QuickHouseBillModel hbModel : model.getHouseBills()) {
      if (StringUtil.isEmpty(hbModel.getDescriptionsOfGoods()) && StringUtil.isEmpty(hbModel.getCommodities())) return;
      InquiryCommodity commodity = new InquiryCommodity();
      commodity.setName("commodity");
      commodity.setDescription(hbModel.getDescriptionsOfGoods());
      String commodities = hbModel.getCommodities();
      if (StringUtil.isNotEmpty(commodities)) {
        List<String> hsCodes  = StringUtil.split(commodities, ',');
        for(String str : hsCodes) {
          CommodityType type = commodityTypeLogic.getCommodityTypeByHsCode(client, company, str.trim());
          commodity.withType(type);
        }
      }
    }
  }

  public void computeContainerModel(
    ClientInfo client, Company company, List<QuickContainerModel> containers, BookingProcess process, String caseReference) {
    if (Collections.isEmpty(containers)) return;
    List<TrackableContainer> containersDb = trackingLogic.findTrackableContainers(client, company, caseReference);

    final Map<String, List<TrackableContainer>> containerNoMap = containersDb.stream()
      .filter(sel -> StringUtil.isEmpty(sel.getLabel()))
      .collect(Collectors.groupingBy(TrackableContainer::getLabel));

    for (QuickContainerModel sel : containers) {
      final double quantity = sel.getQuantity();
      String containerNo = sel.getContainerNo();
      if(StringUtil.isNotEmpty(containerNo)) containerNo = containerNo.trim();
      if(StringUtil.isNotEmpty(containerNo) && !containerNoMap.containsKey(containerNo)) {
        TrackableContainer newContainer = new TrackableContainer(caseReference);
        newContainer.setSealNo(sel.getSealNo());
        newContainer.setLabel(containerNo);
        newContainer.setContainerType(sel.getContainerType());
        newContainer.setCarrierTrainMasterBillCode(sel.getCarrierTrainMAWBNo());
        newContainer.setGrossWeightUnit(sel.getCargoWeightUnit());
        newContainer = trackingLogic.saveTrackableContainer(client, company, newContainer);
        containerNoMap.put(newContainer.getLabel(), Arrays.asList(newContainer));
      }

      if (quantity > 0) {
        TrackableCargo cargo = new TrackableCargo(process);
        if(StringUtil.isNotEmpty(containerNo) && containerNoMap.containsKey(containerNo)) {
          final List<TrackableContainer> trackableContainers = containerNoMap.get(containerNo);
          final Optional<TrackableContainer> first = trackableContainers.stream().findFirst();
          if (first.isPresent() && !TransportationMode.isAirTransport(process.getMode())) {
            final TrackableContainer trackableContainer = first.get();
            cargo.setContainerId(trackableContainer.getId());
            cargo.setContainerNo(trackableContainer.getLabel());
          }
        }

        cargo.setPackagingType(sel.getPackagingType());
        cargo.setQuantity(quantity);
        cargo.setGrossVolume(sel.getCargoVolume() / quantity);
        cargo.setTotalVolume(sel.getCargoVolume());
        cargo.setGrossVolumeUnit(sel.getCargoVolumeUnit());
        cargo.setTotalGrossWeight(sel.getCargoGrossWeight());
        cargo.setGrossWeightOfEachPackage(sel.getCargoGrossWeight() / quantity);
        cargo.setGrossWeightUnit(sel.getCargoWeightUnit());
        cargo.genLabel();
        trackingLogic.saveTrackableCargo(client, company, cargo);
      }
    }
  }

  public List<BookingProcess> findByIds(ClientInfo client, Company company, List<Long> bProcessIds) {
    return bpRepo.findByIds(company.getId(), bProcessIds);
  }

  public void updateShipmentState(ClientInfo client, Company company, FileState state, List<Long> bProcessIds) {
    if(FileState.isClosed(state)) {
      bpRepo.closedBookingProcess(company.getId(), new Date(), client.getRemoteUser(), bProcessIds);
    } else {
      bpRepo.openBookingProcess(company.getId(), new Date(), client.getRemoteUser(), bProcessIds);
    }
  }
}