package cloud.datatp.fforwarder.mgmt;

import cloud.datatp.fforwarder.mgmt.QuickHouseBillModel.QuickContainerModel;
import cloud.datatp.fforwarder.mgmt.po.entity.PurchaseOrder;
import cloud.datatp.fforwarder.price.entity.CustomClearanceType;
import cloud.datatp.fforwarder.sales.booking.entity.Booking;
import cloud.datatp.fforwarder.sales.inquiry.entity.Cargo;
import cloud.datatp.fforwarder.sales.inquiry.entity.Container;
import cloud.datatp.fforwarder.sales.inquiry.entity.DeliveryServiceInquiry;
import cloud.datatp.fforwarder.sales.inquiry.entity.SpecificServiceInquiry;
import cloud.datatp.fforwarder.settings.Purpose;
import cloud.datatp.fforwarder.settings.TransportationMode;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.util.ds.Arrays;
import net.datatp.util.ds.Collections;
import net.datatp.util.ds.Objects;
import net.datatp.util.text.DateUtil;

//Dan - for trucking, custom clearance
@NoArgsConstructor
@Getter
@Setter
public class QuickBookingProcessModel {
  private Purpose purpose = Purpose.DOMESTIC;
  private TransportationMode mode;
  private CustomClearanceType type;

  // support for input bfsone sync
  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  private Date closedDate = new Date();

  private String bookingCaseReference;

  private Long purchaseOrderId;

  private Long clientPartnerId;
  private String clientLabel;

  private Long assigneeEmployeeId;
  private String assigneeLabel;

  private List<QuickHouseBillModel> houseBills = new ArrayList<>();
  // for truck container
  private List<QuickContainerModel> containerModels = new ArrayList<>();

  public QuickBookingProcessModel withHouseBill(QuickHouseBillModel hb) {
    houseBills = Arrays.addToList(houseBills, hb);
    return this;
  }

  public QuickBookingProcessModel withContainer(QuickContainerModel c) {
    containerModels = Arrays.addToList(containerModels, c);
    return this;
  }

  public Booking computeBooking() {
    Booking booking = new Booking();
    SpecificServiceInquiry inquiry = new SpecificServiceInquiry();
    ensureClientNotNull();
    inquiry.setClientPartnerId(clientPartnerId);
    inquiry.setClientLabel(clientLabel);
    inquiry.setSalemanLabel(assigneeLabel);
    //inquiry.setSalemanEmployeeId(assigneeEmployeeId);
    inquiry.setPurpose(purpose);

    double volume = 0;
    double grossWeight = 0;
    boolean firstBill = true;
    for (QuickHouseBillModel hb : houseBills) {
      volume += hb.getCargoVolume();
      grossWeight += hb.getCargoGrossWeight();
      if (firstBill) {
        inquiry.setMode(hb.getMode());
        if (TransportationMode.isTruckTransport(hb.getMode())) {
          inquiry.setPickupAddress(hb.getFromAddress());
          inquiry.setDeliveryAddress(hb.getToAddress());
        } else {
          inquiry.setFromLocationCode(hb.getFromLocationCode());
          inquiry.setFromLocationLabel(hb.getFromLocationLabel());
          inquiry.setToLocationCode(hb.getToLocationCode());
          inquiry.setToLocationLabel(hb.getToLocationLabel());
        }
      }
      firstBill = false;
    }
    inquiry.setVolumeCbm(volume);
    inquiry.setGrossWeightKg(grossWeight);
    booking.setInquiry(inquiry);
    booking.setBookingCaseReference(bookingCaseReference);
    booking.setCode(bookingCaseReference);
    booking.setBookingDate(new Date());
    booking.setCustomerTruckTransportCharges(new ArrayList<>());
    booking.setCustomerCustomClearances(new ArrayList<>());
    return booking;
  }

  private void ensureClientNotNull() {
    if (Objects.nonNull(clientPartnerId)) return;
    final QuickHouseBillModel first = Collections.findFirst(houseBills, Objects::nonNull);
    if (Objects.nonNull(first)) {
      clientLabel = first.getClientLabel();
      clientPartnerId = first.getClientPartnerId();
    }
  }

  private void computeContainer(SpecificServiceInquiry inquiry) {
    int i = 1;
    for (QuickContainerModel sel : containerModels) {
      final String containerNo = sel.getContainerNo();
      Container container = new Container();
      container.setContainerType(sel.getContainerType());
      container.setTotalCargoWeight(sel.getCargoGrossWeight());
      container.setCargoWeightUnit(sel.getCargoWeightUnit());
      inquiry.withContainer(container);

      final double quantity = sel.getQuantity();
      if (quantity > 0) {
        Cargo cargo = new Cargo();
        cargo.setPackagingType(sel.getPackagingType());
        cargo.setQuantity(quantity);
        cargo.setVolume(sel.getCargoVolume() / quantity);
        cargo.setTotalVolume(sel.getCargoVolume());
        cargo.setVolumeUnit(sel.getCargoVolumeUnit());
        cargo.setGrossWeight(sel.getCargoGrossWeight() / quantity);
        cargo.setTotalGrossWeight(sel.getCargoGrossWeight());
        cargo.setGrossWeightUnit(sel.getCargoWeightUnit());
        i++;
      }
    }
  }

  public PurchaseOrder computePurchaseOrder() {
    ensureClientNotNull();
    PurchaseOrder purchaseOrder = new PurchaseOrder();
    purchaseOrder.setCode(bookingCaseReference);
    purchaseOrder.setLabel("PO for " + clientLabel);
    purchaseOrder.setAssigneeLabel(assigneeLabel);
    purchaseOrder.setAssigneeEmployeeId(assigneeEmployeeId);
    purchaseOrder.setClientLabel(clientLabel);
    purchaseOrder.setClientPartnerId(clientPartnerId);
    return purchaseOrder;
  }
}