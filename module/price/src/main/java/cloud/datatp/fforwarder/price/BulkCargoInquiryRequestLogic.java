package cloud.datatp.fforwarder.price;

import cloud.datatp.fforwarder.price.common.BulkCargoShipmentDetail;
import cloud.datatp.fforwarder.price.entity.BulkCargoInquiryRequest;
import cloud.datatp.fforwarder.price.entity.InquiryRequest;
import cloud.datatp.fforwarder.price.repository.BulkCargoInquiryRequestRepository;
import jakarta.annotation.PostConstruct;
import java.util.Date;
import java.util.List;
import lombok.Getter;
import net.datatp.module.account.AccountLogic;
import net.datatp.module.account.entity.Account;
import net.datatp.module.bot.BotService;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.communication.CommunicationMessageLogic;
import net.datatp.module.communication.MailMessage;
import net.datatp.module.communication.entity.CommunicationAccount;
import net.datatp.module.company.CompanyConfigLogic;
import net.datatp.module.company.entity.Company;
import net.datatp.module.company.entity.CompanyConfig;
import net.datatp.module.core.security.SecurityLogic;
import net.datatp.module.core.security.entity.AppPermission;
import net.datatp.module.data.db.DAOService;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.data.db.seq.SeqService;
import net.datatp.module.graphapi.GraphApiService;
import net.datatp.module.hr.EmployeeLogic;
import net.datatp.module.zalo.ZaloLogic;
import net.datatp.util.ds.Collections;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Getter
@Component
public class BulkCargoInquiryRequestLogic extends DAOService {

  @Autowired
  private EmployeeLogic employeeLogic;

  @Autowired
  private AccountLogic accountLogic;

  @Autowired
  private CompanyConfigLogic companyConfigLogic;

  @Autowired
  private SecurityLogic securityLogic;

  @Autowired
  private CommunicationMessageLogic messageLogic;

  @Autowired
  private BulkCargoInquiryRequestRepository inquiryRequestRepo;

  @Autowired
  private GraphApiService graphApiService;

  @Autowired
  private SeqService seqService;

  @Autowired
  private ZaloLogic zaloLogic;

  @Autowired
  private BotService botService;

  @PostConstruct
  private void onInit() {
    seqService.createIfNotExists(BulkCargoInquiryRequest.SEQUENCE, 10);
  }

  public BulkCargoInquiryRequest getBulkCargoInquiryRequest(ClientInfo client, Company company, Long requestId) {
    return inquiryRequestRepo.findById(requestId).get();
  }

  public BulkCargoInquiryRequest initBulkCargoInquiryRequest(ClientInfo client, Company company, BulkCargoInquiryRequest request) {
    Account account = accountLogic.getAccountByLoginId(client, client.getRemoteUser());
    Objects.assertNotNull(account, "Account not found!!!, login id: " + client.getRemoteUser());
    CommunicationAccount messageAccount = messageLogic.getCommunicationAccount(client, client.getRemoteUser());
    if (messageAccount != null) {
      request.setSalemanEmail(messageAccount.getEmail());
      request.setSalemanPhone(messageAccount.getMobile());
    }
    request.setSalemanAccountId(account.getId());
    request.setSalemanLabel(account.getFullName());
    //set default mail to in request
    String builder = "mail.request.pricing.";
    CompanyConfig companyConfig = companyConfigLogic.getCompanyConfigByCompanyId(client, company.getId());
    String mailTo = companyConfig.getAttributeAsString(builder, null);
    if (StringUtil.isNotEmpty(mailTo)) request.withTo(mailTo);
    return request;
  }

  public BulkCargoInquiryRequest sendBulkCargoInquiryRequest(ClientInfo client, Company company, BulkCargoInquiryRequest request) {
    BulkCargoInquiryRequest priceCheckRequest = saveBulkCargoInquiryRequest(client, company, request);
    MailMessage mailMessage = new MailMessage();
    mailMessage.setMessage(priceCheckRequest.getMailMessage());
    mailMessage.setFrom(priceCheckRequest.getSalemanEmail());
    mailMessage.setSubject(priceCheckRequest.getMailSubject());
    mailMessage.setTo(priceCheckRequest.getToList());
    mailMessage.setCc(priceCheckRequest.getCcList());
    mailMessage.setAttachments(request.getAttachments());
    graphApiService.sendEmailWithHtmlFormat(client, company, mailMessage);
    return priceCheckRequest;
  }

  public List<MapObject> saveBulkCargoInquiryRequestRecords(ClientInfo client, Company company, List<MapObject> requests) {
    if (Collections.isNotEmpty(requests)) {
      for (MapObject request : requests) {
        BulkCargoInquiryRequest priceCheckRequest = new BulkCargoInquiryRequest();
        Long id = request.getLong("id", null);
        if (id != null) priceCheckRequest = getBulkCargoInquiryRequest(client, company, id);
        priceCheckRequest = priceCheckRequest.mergeFromMapObject(request);
        if (StringUtil.isEmpty(priceCheckRequest.getTo())) {
          CommunicationAccount messageAccount = messageLogic.getCommunicationAccount(client, client.getRemoteUser());
          Objects.assertNotNull(messageAccount, "Message Account not found!!!, login id: " + client.getRemoteUser());
          priceCheckRequest.setTo(messageAccount.getEmail());
        }
        BulkCargoInquiryRequest savedRequest = saveBulkCargoInquiryRequest(client, company, priceCheckRequest);
        request.put("id", savedRequest.getId());
      }
    }
    return requests;
  }

  public BulkCargoInquiryRequest saveBulkCargoInquiryRequest(ClientInfo client, Company company, BulkCargoInquiryRequest request) {
    if(StringUtil.isEmpty(request.getCode())) {
      BulkCargoShipmentDetail shipmentDetail = request.getShipmentDetail();
      Objects.assertNotNull(shipmentDetail, "Shipment Detail must be not null!");
      String prefix = shipmentDetail.getCargoType().getCode() + DateUtil.asCompactMonthId(new Date());
      String code = prefix + String.format("%05d", seqService.nextSequence(InquiryRequest.SEQUENCE));
      request.setCode(code);
    }

    request.computeMailSubject();
    if (request.getRequestDate() == null) {
      request.setRequestDate(new Date());
    }
    if (request.getSalemanAccountId() == null) {
      CommunicationAccount messageAccount = messageLogic.getCommunicationAccount(client, client.getRemoteUser());
      Objects.assertNotNull(messageAccount, "Message Account not found!!!, login id: " + client.getRemoteUser());
      request.setSalemanEmail(messageAccount.getEmail());
      request.setSalemanPhone(messageAccount.getMobile());
    }
    request.set(client, company);
    return inquiryRequestRepo.save(request);
  }

  public List<SqlMapRecord> searchBulkCargoInquiryRequests(ClientInfo client, Company company, SqlQueryParams sqlParams) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/price/groovy/BulkCargoInquiryRequestSql.groovy";
    sqlParams.addParam("accessAccountId", client.getAccountId());
    sqlParams.addParam("companyId", company.getId());
    AppPermission pricePermission = securityLogic.getAppPermission(client, company.getId(), "logistics", "user-logistics-prices");
    AppPermission salePermission = securityLogic.getAppPermission(client, company.getId(), "logistics", "user-logistics-sales");
    if (pricePermission == null && salePermission == null) return java.util.Collections.emptyList();
    if (!sqlParams.hasParam("space")) sqlParams.addParam("space", "User");
    if (sqlParams.hasParam("filterMode") && StringUtil.isNotEmpty(sqlParams.getString("filterMode"))) {
      CommunicationAccount messageAccount = messageLogic.getCommunicationAccountByAccountId(client, client.getAccountId());
      Objects.assertNotNull(messageAccount, "Message Account not found!!!, login id: " + client.getRemoteUser());
      sqlParams.addParam("mailPattern", messageAccount.getEmail());
    }
    return searchDbRecords(client, scriptDir, scriptFile, "SearchBulkCargoInquiryRequest", sqlParams);
  }

  public List<SqlMapRecord> searchBulkCargoInquiryRequestSpace(ClientInfo client, Company company, SqlQueryParams sqlParams) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/price/groovy/BulkCargoInquiryRequestSql.groovy";
    sqlParams.addParam("accessAccountId", client.getAccountId());
    sqlParams.addParam("companyId", company.getId());
    AppPermission pricePermission = securityLogic.getAppPermission(client, company.getId(), "logistics", "user-logistics-prices");
    AppPermission salePermission = securityLogic.getAppPermission(client, company.getId(), "logistics", "user-logistics-sales");
    if (pricePermission == null && salePermission == null) return java.util.Collections.emptyList();
    if (!sqlParams.hasParam("space")) sqlParams.addParam("space", "User");
    return searchDbRecords(client, scriptDir, scriptFile, "SearchBulkCargoInquiryRequestSpace", sqlParams);
  }

  public boolean deleteBulkCargoInquiryRequests(ClientInfo client, Company company, List<Long> ids) {
    inquiryRequestRepo.deleteAllById(ids);
    return true;
  }

  public List<BulkCargoInquiryRequest> findByCompany(ClientInfo client, Company company) {
    return inquiryRequestRepo.findByCompany(company.getId());
  }

}