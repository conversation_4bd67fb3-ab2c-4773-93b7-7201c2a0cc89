
package cloud.datatp.tms.bill.entity;

import java.io.Serial;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import cloud.datatp.fleet.vehicle.entity.VehicleTripGoodsTracking;
import cloud.datatp.fleet.vehicle.entity.VehicleTripGoodsTracking.VehicleTripGoodsTrackingStatus;
import cloud.datatp.jobtracking.entity.ISupportJobTracking;
import cloud.datatp.tms.TMSUtils;
import cloud.datatp.tms.bill.entity.TMSBillForwarderTransport.TransportationMode;
import cloud.datatp.tms.settings.entity.TMSTransportType;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Embedded;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.Index;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import jakarta.persistence.UniqueConstraint;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.company.entity.Company;
import net.datatp.module.company.entity.CompanyEntity;
import net.datatp.util.dataformat.DataSerializer;
import net.datatp.util.ds.MapObject;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;
import net.datatp.util.text.TokenUtil;

@Entity
@Table(
  name = TMSBill.TABLE_NAME,
  uniqueConstraints = { @UniqueConstraint( name = TMSBill.TABLE_NAME + "_code", columnNames = { "company_id", "code" }) },
  indexes = {
    @Index(name = TMSBill.TABLE_NAME + "_code_idx", columnList = "code"),
    @Index(name = TMSBill.TABLE_NAME + "_customer_id_idx", columnList = "customer_id"),
    @Index(name = TMSBill.TABLE_NAME + "_responsible_account_id_idx", columnList = "responsible_account_id"),
    @Index(name = TMSBill.TABLE_NAME + "_receiver_location_id_idx", columnList = "receiver_location_id"),
    @Index(name = TMSBill.TABLE_NAME + "_label_idx", columnList = "label"),
    @Index(name = TMSBill.TABLE_NAME + "_responsible_full_name_idx", columnList = "responsible_full_name"),
    @Index(name = TMSBill.TABLE_NAME + "_customer_full_name_idx", columnList = "customer_full_name"),
    @Index(name = TMSBill.TABLE_NAME + "_company_id_idx",columnList = "company_id"),
    @Index(name = TMSBill.TABLE_NAME + "_storage_state_idx",columnList = "storage_state"),
    @Index(name = TMSBill.TABLE_NAME + "_original_customer_code_idx", columnList = "original_customer_code"),
  }
)

@JsonInclude(Include.NON_NULL)
@NoArgsConstructor @Getter @Setter
public class TMSBill extends CompanyEntity implements ISupportJobTracking {
  @Serial
  private static final long serialVersionUID = 1L;

  public static final String TABLE_NAME                                = "lgc_tms_bill";
  
  public static final String SEQUENCE                                  = "tms:lgc_tms_bill";
  public static final String COLLECT_SEQUENCE                          = "tms:lgc_tms_bill_collect";
  public static final String TMS_BILL_JOB_TRACKING_COMPANY_CONFIG_CODE = "lgc.tms.bill-job-tracking-code";
  public static final String TMS_BILL_JOB_TRACKING_DEFAULT_RULE_ID     = "lgc.tms.job-tracking-rule-default-id";
  public static final String AUTO_VERIFY_JOB_TRACKING                  = "lgc.tms.auto-verify-job-tracking";
  
  static public enum TMSBillProcessStatus {
    PLAN, PENDING, PROCESSING, DONE, CANCELED;
    
    public static TMSBillProcessStatus parseSpreadSheetStatus(String value) {
      switch (value) {
        case "Plan": {
          return PLAN;
        }
        case "Pending": {
          return PENDING;
        }
        case "Done": {
          return DONE;
        }
        default: return PLAN;
      }
    }
  }

  @Column(name = "job_tracking_id")
  private Long jobTrackingId;
  
  @Column(name = "tms_house_bill_id")
  private Long tmsHouseBillId;

  @NotNull
  @Column(length=50)
  private String code;
  
  //RENAME -> fileNo
  @NotNull @Column(length=1024)
  private String label;
  
  @Column(name = "ref_source", length=50)
  private String refSource;
  
  @Column(length=50, name = "ref_id")
  private String refId;
  
  @Column(name = "ref_code", length=50)
  private String refCode;
  
  //rename from vendor_bill_id
  @Column(name = "origin_vendor_bill_id")
  private Long originVendorBillId;
  
  //review maybe is origin company code
  @Column(name = "original_customer_code")
  private String originalCustomerCode;
  
  @Column(name = "origin_total_vendor_attach_files")
  private Integer originTotalVendorAttachFiles;
  
  @Column(name = "sync_vendor_bill_authorization_token", length=1024)
  private String syncVendorBillAuthorizationToken;
  
  @Column(name = "tms_bill_print_authorization_token", length=1024)
  private String tmsBillPrintAuthorizationToken;
  
  private String collect;
  
  @Column(name = "office")
  private String office;
  
  @Column(name = "transport_report_id")
  private Long transportReportId;
  
  @Column(name = "container_in_hand_plan_id")
  private Long containerInHandPlanId;
  
  @Enumerated(EnumType.STRING)
  private TMSBillType type = TMSBillType.IMS;
  
  @Enumerated(EnumType.STRING)
  @Column(name = "process_status")
  private TMSBillProcessStatus processStatus = TMSBillProcessStatus.PLAN;

  @Column(name = "booking_date")
  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  private Date bookingDate;
  
//  @Column(name = "accounting_report_date")
//  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
//  private Date accountingReportDate;
  
  @Column(name = "vehicle_info_lock_date")
  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  private Date vehicleInfoLockDate;
  
  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "delivery_plan")
  private Date deliveryPlan;
 
  private String time;
  
  @Column(name="estimate_time")
  private String estimateTime;
  
  @Column(name="delayed_time")
  private Integer delayedTime;
  
  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name="modified_estimate_time")
  private Date modifiedEstimateTime;
  
  @Column(name="responsible_account_id")
  private Long responsibleAccountId;

  @Column(name="responsible_full_name")
  private String responsibleFullName;

  @Column(length=1024 * 4)
  private String description;
  
  @Embedded
  private TMSBillCustomer customer = new TMSBillCustomer();
  
  @Embedded
  private TMSBillSender   sender   = new TMSBillSender();
  
  @Embedded
  private TMSBillReceiver receiver = new TMSBillReceiver();
  
  @Embedded
  private TMSBillGoods    tmsBillGoods = new TMSBillGoods();
  
  @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
  @JoinColumn(name = "tms_bill_id", referencedColumnName = "id")
  private List<TMSBillStopLocation> stopLocations = new ArrayList<>();

  //Migration table
  @OneToOne(cascade = CascadeType.ALL, orphanRemoval = true)
  @JoinColumn(name = "tms_bill_transport_id", referencedColumnName = "id")
  private TMSBillTransport          tmsBillTransport;
  
  @OneToOne(fetch = FetchType.EAGER, cascade = CascadeType.ALL, orphanRemoval = true)
  @JoinColumn(name = "tms_bill_forwarder_transport_id", referencedColumnName = "id")
  private TMSBillForwarderTransport tmsBillForwarderTransport;
  
  @OneToOne(cascade = CascadeType.ALL, orphanRemoval = true)
  @JoinColumn(name = "tms_bill_fee_id", referencedColumnName = "id")
  private TMSBillFee                tmsBillFee = new TMSBillFee();
  
  @Transient
  private boolean sendEmail;
  
  @Column(name = "message_id")
  private Long messageId;
  
//  @OneToOne(cascade = CascadeType.MERGE, optional = false)
//  @JoinColumn(name = "current_responsibility_id", referencedColumnName = "id")
//  private TMSBillResponsibility currentResponsibility;

  public TMSBill(String refSouce, String refCode) {
    this.code = refCode;
    this.label = refCode;
    this.refCode = refCode;
    this.refSource = refSouce;
  }
  
  public TMSBill(TMSBillType type) {
    this.type = type;
    if(type == TMSBillType.IMS) this.tmsBillTransport = new TMSBillTransport();
    if(type == TMSBillType.FORWARDER) this.tmsBillForwarderTransport = new TMSBillForwarderTransport();
  }
  
  public TMSBill genCode(String companyCode,Long seq) {
    if(type == TMSBillType.IMS) this.code = "IMS/" + companyCode.toUpperCase() + "/" + DateUtil.asCompactYearMonth(new Date()) + "/" + String.format("%06d", seq);
    if(type == TMSBillType.FORWARDER) this.code = "FWD/" + companyCode.toUpperCase() + "/" + DateUtil.asCompactYearMonth(new Date()) + "/" + String.format("%06d", seq);
    return this;
  }
  
  public TMSBill genCodeWithTimeId() {
    if(type == TMSBillType.IMS) this.code =  TokenUtil.idWithDateTime("IMS").replace("-", "/");
    if(type == TMSBillType.FORWARDER) this.code = TokenUtil.idWithDateTime("FWD").replace("-", "/");
    return this;
  }
  
  public static String genCollectToken(Long seq) {
   return "COL" + DateUtil.asCompactYearMonth(new Date()) + "/" + String.format("%06d", seq);
  }
  
  public TMSBill addCostItem(TMSBillCostItem item) {
   this.getTmsBillFee().getCostItems().add(item);
   return this;
  }
  
  public TMSBillCustomer getCustomer() {
    if(this.customer == null) this.customer =  new TMSBillCustomer();
    return this.customer;
  }
  
  public TMSBillSender getSender() {
    if(this.sender == null) this.sender =  new TMSBillSender();
    return this.sender;
  }
  
  public TMSBillReceiver getReceiver() {
    if(this.receiver == null) this.receiver =  new TMSBillReceiver();
    return this.receiver;
  }
  
  public String getWarehouseLabel() {
    if(tmsBillForwarderTransport.isExport() && receiver != null) return receiver.getReceiverAddress();
    return null;
  }
  
  public Date getDeliveryTime() {
    if(deliveryPlan == null) return null;
    if (getTime() != null && getTime().length() == 5 && getTime().split(":").length == 2) {
      LocalDateTime localDateTime = deliveryPlan.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
      localDateTime = localDateTime
          .withHour(Integer.parseInt(getTime().split(":")[0]))
          .withMinute(Integer.parseInt(getTime().split(":")[1]));
      return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }
    return deliveryPlan;
  }
  
  public String getShipmentInfo() {
    if(tmsBillForwarderTransport == null) return null;
    String shipmentInfo = "";
    if(tmsBillForwarderTransport.isFcl() || (tmsBillForwarderTransport.isDomestic() && StringUtil.isNotBlank(tmsBillForwarderTransport.getContainerNo()))) {
      shipmentInfo+= tmsBillForwarderTransport.getModeLabel() + " " + tmsBillForwarderTransport.getTruckType();
      shipmentInfo+= "\n" + tmsBillForwarderTransport.getContainerNo() + "/" + tmsBillForwarderTransport.getSealNo();
      shipmentInfo+= "/" + tmsBillGoods.getWeight() + " (" + tmsBillGoods.getWeightUnit() + ")";
    } else {
      shipmentInfo+= tmsBillForwarderTransport.getModeLabel() + " " + tmsBillForwarderTransport.getTruckType();
      shipmentInfo+= "\n" + tmsBillGoods.getQuantity() + " (" + tmsBillGoods.getQuantityUnit() + ")";
      shipmentInfo+= "/" + tmsBillGoods.getWeight() + " (" + tmsBillGoods.getWeightUnit() + ")";
      shipmentInfo+= "/" + tmsBillGoods.getVolumeAsText() + " (" + tmsBillGoods.getVolumeUnit() + ")";
    }
    return shipmentInfo;
  }
  
  public TMSBill withTmsType(TMSTransportType type) {
    this.tmsBillTransport.setTmsType(type);
    return this;
  }
  
  public TMSBill withSyncVendorBillAuthorizationToken(String authorization) {
    if(authorization == null) return this;
    this.syncVendorBillAuthorizationToken = authorization;
    return this;
  }
  
  public TMSBill withTMSBillPrintAuthorizationToken(String authorization) {
    if(authorization == null) return this;
    this.tmsBillPrintAuthorizationToken = authorization;
    return this;
  }
  
  public TMSBill withOriginVendorBill(Long originVendorBillId, Integer totalAttFiles) {
    if(originVendorBillId != null) this.originVendorBillId                 = originVendorBillId;
    if(totalAttFiles != null)      this.originTotalVendorAttachFiles = totalAttFiles;
    return this;
  }
  
  public MapObject toTMSBillMap() {
    MapObject data = new MapObject();
    data.add("type", getType());
    data.add("processStatus", getProcessStatus());
    data.add("deliveryPlan", getDeliveryPlan());
    data.add("description", getDescription());
    data.add("office", getOffice());
    
    TMSBillForwarderTransport forwarder = getTmsBillForwarderTransport();
    data.add("truckType", forwarder.getTruckType());
    data.add("mode", forwarder.getMode());
    data.add("containerNo", forwarder.getContainerNo());
    data.add("sealNo", forwarder.getSealNo());
    data.add("vendorId", forwarder.getVendorId());
    data.add("vendorFullName", forwarder.getVendorFullName());
    
    TMSBillGoods goods = getTmsBillGoods();
    data.add("quantity", goods.getQuantity());
    data.add("quantityUnit", goods.getQuantityUnit());
    data.add("weight", goods.getWeight());
    data.add("weightUnit", goods.getWeightUnit());
    data.add("volume", goods.getVolume());
    data.add("volumeAsText", goods.getVolumeAsText());
    data.add("volumeUnit", goods.getVolumeUnit());
    data.add("goodsDescription", goods.getGoodsDescription());
    return data;
  }
  
  public VehicleTripGoodsTracking toVehicleTripGoodsTracking() {
    VehicleTripGoodsTracking goodsTracking = new VehicleTripGoodsTracking();
    goodsTracking.setCode(this.code);
    goodsTracking.setLabel("/");
    goodsTracking.setTmsBillId(this.id);
    goodsTracking.setGoods(tmsBillGoods.getGoods());
    goodsTracking.setQuantity(tmsBillGoods.getQuantity());
    goodsTracking.setQuantityUnit(tmsBillGoods.getQuantityUnit());
    goodsTracking.setVolume(tmsBillGoods.getVolume());
    goodsTracking.setVolumeAsText(tmsBillGoods.getVolumeAsText());
    goodsTracking.setChargeableVolume(tmsBillGoods.getChargeableVolume());
    goodsTracking.setVolumeUnit(tmsBillGoods.getVolumeUnit());
    goodsTracking.setWeight(tmsBillGoods.getWeight());
    goodsTracking.setChargeableWeight(tmsBillGoods.getChargeableWeight());
    goodsTracking.setWeightUnit(tmsBillGoods.getWeightUnit());
    goodsTracking.setResponsibleAccountId(responsibleAccountId);
    goodsTracking.setResponsibleFullName(responsibleFullName);
    goodsTracking.setStatus(VehicleTripGoodsTrackingStatus.NEED_CONFIRM);
    if(tmsBillForwarderTransport != null) {
      goodsTracking.setVehicleType(tmsBillForwarderTransport.getTruckType());
      if(tmsBillForwarderTransport.isImport() && tmsBillForwarderTransport.isFcl() && sender != null) {
        goodsTracking.setPickupContainerLocation(sender.getSenderAddress());
        goodsTracking.setPickupContainerLocationId(sender.getSenderLocationId());
      }
      if(tmsBillForwarderTransport.isExport() && tmsBillForwarderTransport.isFcl() && receiver != null) {
        goodsTracking.setReturnContainerLocation(receiver.getReceiverAddress());
        goodsTracking.setReturnContainerLocationId(receiver.getReceiverLocationId());
      }
      
    }
    return goodsTracking;
  }
  
  public TMSBill withRefFieldSBFSOneData(MapObject bfsOneMap) {
    this.refId = bfsOneMap.getString("transId");
    this.refCode = bfsOneMap.getString("transId") + "/HWB" + bfsOneMap.getString("hwbNo");
    String containerId = bfsOneMap.getString("containerId");
    if(StringUtil.isNotBlank(containerId)) this.refCode = this.refCode + "/CONT" + containerId;
    return this;
  }
  
  public TMSBill convertBFSOneData(MapObject bfsOneMap) {
    if(sender == null)   sender = new TMSBillSender();
    if(receiver == null) receiver = new TMSBillReceiver();
    TMSBillForwarderTransport forwarder = new TMSBillForwarderTransport();
    
    this.label = bfsOneMap.getString("transId");
    this.description = bfsOneMap.getString("note");
    String typeOfService = bfsOneMap.getString("typeOfService");
    forwarder.withModeByBFSOneTypeService(typeOfService);
    
    if(forwarder.isExport()) {
      forwarder.setWarehouseId(0L);
      forwarder.setWarehouseLabel(bfsOneMap.getString("portOfLading"));
      forwarder.setCutOffTime(bfsOneMap.getDate("etd", null));
      sender.setSenderAddress(bfsOneMap.getString("shipperAddress"));
    }
    if(forwarder.isImport()) {
      forwarder.setWarehouseId(0L);
      forwarder.setWarehouseLabel(bfsOneMap.getString("portOfUnlading"));
      forwarder.setEta(bfsOneMap.getDate("eta", null));
      receiver.setReceiverAddress(bfsOneMap.getString("shipperAddress"));
    }
    forwarder.setBookingCode(bfsOneMap.getString("hwbNo"));
    setTmsBillForwarderTransport(forwarder);
    
    TMSBillGoods goods = new TMSBillGoods();
    goods.setQuantity(TMSUtils.ROUNDED(bfsOneMap.getDouble("quantity", 0D), 2));
    goods.setQuantityUnit(bfsOneMap.getString("quantityUnit"));
    goods.setChargeableWeight(TMSUtils.ROUNDED(bfsOneMap.getDouble("chargeableWeight", 0D), 2));
    goods.setWeight(TMSUtils.ROUNDED(bfsOneMap.getDouble("grossWeight", 0D), 2));
    goods.setWeightUnit("KGM");
    goods.setVolume(TMSUtils.ROUNDED(bfsOneMap.getDouble("cbmSea", 0D),2));
    goods.setVolumeUnit("CBM");
    setTmsBillGoods(goods);
    
    String containerType = bfsOneMap.getString("containerType");
    if(StringUtil.isNotBlank(containerType)) {
      containerType = containerType.replaceAll("`", "").trim();
    }
    forwarder.setTruckType(containerType);
    forwarder.setContainerNo(bfsOneMap.getString("containerNo"));
    forwarder.setSealNo(bfsOneMap.getString("sealNo"));
    forwarder.setHwbNo(bfsOneMap.getString("hwbNo"));
    return this;
  }
  
  public TMSBill clone(){
    TMSBill clone = DataSerializer.JSON.clone(this);
    clone.clearIds();
    if(clone.getTmsBillForwarderTransport() != null) clone.getTmsBillForwarderTransport().clearIds();
    if(clone.getTmsBillTransport() != null) clone.getTmsBillTransport().clearIds();
    if(clone.getTmsBillFee() != null) {
      clone.getTmsBillFee().clearIds();
      clone.getTmsBillFee().clearIds(clone.getTmsBillFee().getItems());
    }
    clone.setCode(null);
    clone.setJobTrackingId(null);
    clone.setCompanyId(null);
    clone.setCreatedBy(null);
    clone.setCreatedTime(null);
    clone.setModifiedBy(null);
    clone.setModifiedTime(null);
    return clone;
  }
  
  public TMSBill merge(TMSBill source) {
    source.getTmsBillGoods().setGoodItems(null);
    String [] ignoreProperties = 
      {
          "refSource", "refId", "refCode", "jobTrackingId", 
          "collect", "transportReportId", 
          "tmsBillFee", "tmsBillGoods", "currentResponsibility",
          "originTotalOriginVendorAttachFiles",
          "originVendorBillId", "originTotalOriginVendorAttachFiles"
      };
    super.merge(source, ignoreProperties);
    
    if(this.tmsBillGoods == null) this.tmsBillGoods = new TMSBillGoods();
    this.tmsBillGoods.merge(source.getTmsBillGoods(), "goodItems");
    
    if(this.tmsBillFee == null) this.tmsBillFee = new TMSBillFee(this.code);
    this.tmsBillFee.merge(source.getTmsBillFee(), "code", "items");
    
    return this;
  }
  
  @Override
  public void set(ClientInfo client, Company company) {
    super.set(client, company);
    set(client, company, stopLocations);
    if(tmsBillForwarderTransport != null) tmsBillForwarderTransport.set(client);
    if(tmsBillTransport != null) tmsBillTransport.set(client);
    if(tmsBillFee != null) tmsBillFee.set(client);
  }

  @Override
  public Long computeCustomJobTrackingId() {
    if (customer == null) return null;
    return customer.getCustomerId();
  }
  
  @Override
  public Long getJobTrackingProjectRuleId() {
    return null;
  }

  @Override
  public String computeCustomJobTrackingType() {
    return "tms-bill";
  }
}