package cloud.datatp.tms.housebill.entity;

import java.io.Serial;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import cloud.datatp.tms.bill.entity.TMSBill;
import jakarta.persistence.Column;
import jakarta.persistence.Embedded;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.company.entity.CompanyEntity;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;

@Entity
@Table(
  name = TMSHouseBill.TABLE_NAME,
//  uniqueConstraints = { 
//      @UniqueConstraint(
//          name = TMSHouseBill.TABLE_NAME + "_hbl_no", columnNames = { "company_id", "hbl_no" }
//          )
//      },
  indexes = {
    @Index(name = TMSHouseBill.TABLE_NAME + "_hbl_no_idx",columnList = "hbl_no")
  }
)

@JsonInclude(Include.NON_NULL)
@NoArgsConstructor @Getter @Setter
public class TMSHouseBill extends CompanyEntity {
  @Serial
  private static final long serialVersionUID = 1L;

  public static final String TABLE_NAME = "lgc_tms_house_bill";
  
  public enum Purpose              {Export, Import, Domestic, Cbt, PortTransfer}
  public enum PackingMethod        {Fcl, Lcl}
  public enum TypeOfTransportation {Road, Rail, Sea, Air, Pipeline, Other}
  
  @Column(name = "hbl_no")
  private String hblNo;
  
  private String code;
  
  private String mawb;
  
  @Column(name = "last_shipping_date")
  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  private Date lastShippingDate;
  
  @Column(name = "file_no")
  private String fileNo;
  
  @Enumerated(EnumType.STRING)
  private Purpose       purpose = Purpose.Domestic;
  
  @Enumerated(EnumType.STRING)
  private PackingMethod method  = PackingMethod.Lcl;
  
  @Column(name = "type_of_transportation")
  @Enumerated(EnumType.STRING)
  private TypeOfTransportation typeOfTransportation = TypeOfTransportation.Road;
  
  private String root;//source
  
  @Column(name = "customer_full_name")
  private String customerFullName;
  
  @Column(name = "customer_id")
  private Long customerId;
  
  @Embedded
  private TMSImportAndExport importAndExport  = new TMSImportAndExport();
  
  @Embedded
  private TMSHouseBillGoods  houseBillGoods   = new TMSHouseBillGoods();
  
  @Column(name = "declaration_number")
  private String declarationNumber;
  
  @Column(name = "booking_code")
  private String bookingCode;
  
  @Column(name = "verify_hbl_no")
  private Boolean verifyHblNo;
  
  @Column(length=1024*4)
  private String note;
  
  @Column(length=1024)
  private String error;
  
  public boolean isExport() {
    return Purpose.Export.equals(purpose);
  }
  
  public boolean isImport() {
    return Purpose.Import.equals(purpose);
  }
  
  public TMSHouseBill(SqlMapRecord bfsOneRec) {
    converTypeOfService(bfsOneRec);
    fileNo      = bfsOneRec.getString("transId");
    mawb        =  bfsOneRec.getString("mawb");
    hblNo       = bfsOneRec.getString("hwbNo");
    bookingCode = bfsOneRec.getString("bookingId");
    verifyHblNo = true;
    note        = bfsOneRec.getString("note");
    
    Date eta = bfsOneRec.getDate("eta", null);
    Date etd = bfsOneRec.getDate("etd", null);
    if(isImport()) {
      if(eta != null) {
        importAndExport.setEtaCutOffTime(eta);
        importAndExport.setEtaCutOffTimeNote(DateUtil.asCompactDateTime(eta));
      }
      String portOfUnlading = bfsOneRec.getString("portOfUnlading");
      importAndExport.setWarehouseLocationLabel(portOfUnlading);
    }
    if(isExport()) {
      if(eta != null) {
        importAndExport.setEtaCutOffTime(etd);
        importAndExport.setEtaCutOffTimeNote(DateUtil.asCompactDateTime(etd));
      }
      String portOfLading = bfsOneRec.getString("portOfLading");
      importAndExport.setWarehouseLocationLabel(portOfLading);
    }
  }
  
  private void converTypeOfService(SqlMapRecord bfsOneData) {
    String typeOfService = bfsOneData.getString("typeOfService", "");
    
    if(typeOfService.equals("LogisticsCrossBorder")) {
      typeOfTransportation = TypeOfTransportation.Road;
      purpose = Purpose.Cbt;
      if(StringUtil.isNotBlank(bfsOneData.getString("containerNo"))) method = PackingMethod.Fcl; 
      return;
    }
    typeOfService = typeOfService.toUpperCase();
    if(typeOfService.contains("SEA")) typeOfTransportation = TypeOfTransportation.Sea;
    if(typeOfService.contains("AIR")) typeOfTransportation = TypeOfTransportation.Air;
    if(typeOfService.contains("RAIL")) typeOfTransportation = TypeOfTransportation.Rail;
    if(typeOfService.contains("ROAD")) typeOfTransportation = TypeOfTransportation.Road;
    if(typeOfService.contains("PIPELINE")) typeOfTransportation = TypeOfTransportation.Pipeline;
    
    if(typeOfService.contains("IMP")) purpose = Purpose.Import;
    if(typeOfService.contains("Exp")) purpose = Purpose.Export;
    
    if(typeOfService.contains("FCL")) method = PackingMethod.Fcl;
  }
  
  public TMSBill updateTMSBill(TMSBill bill) {
    bill.getCustomer().setCustomerId(customerId);
    bill.getCustomer().setCustomerFullName(customerFullName);
    bill.setLabel(fileNo);
    bill.setOffice(root);
    bill.getTmsBillForwarderTransport().setHwbNo(hblNo);
    bill.setOffice(root);
    
    String mode = typeOfTransportation + "_" + purpose + "_" + method;
    bill.getTmsBillForwarderTransport().setMode(mode.toUpperCase());
    bill.getTmsBillForwarderTransport().setBookingCode(bookingCode);
    bill.getTmsBillForwarderTransport().setDeclarationNumber(declarationNumber);
    if(importAndExport != null) {
      bill.getTmsBillForwarderTransport().setCarrierFullName(importAndExport.getCarrierFullName());
      bill.getTmsBillForwarderTransport().setCarrierId(importAndExport.getCarrierId());
      bill.getTmsBillForwarderTransport().setEtaCutOffTime(importAndExport.getEtaCutOffTimeNote());
    }
    return bill;
  }
}