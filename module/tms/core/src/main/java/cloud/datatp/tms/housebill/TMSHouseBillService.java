package cloud.datatp.tms.housebill;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cloud.datatp.tms.housebill.entity.TMSHouseBill;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.company.entity.Company;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.util.ds.MapObject;

@Service("TMSHouseBillService")
public class TMSHouseBillService {
 
  @Autowired
  private TMSHouseBillLogic logic;
  
  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchTMSBills(ClientInfo client, Company company, SqlQueryParams params) {
    return logic.searchTMSBills(client, company, params);
  }
  
  @Transactional(readOnly = true)
  public TMSHouseBill getTMSHouseBillById(ClientInfo client, Company company, Long id) {
    return logic.getById(client, company, id);
  }
  
  @Transactional
  public TMSHouseBill saveTMSHouseBill(ClientInfo client, Company company, TMSHouseBill houseBill) {
    return logic.saveTMSHouseBill(client, company, houseBill);
  }
  
  @Transactional
  public MapObject createTMSHouseBillWithBFSOneData(ClientInfo client, Company company, String hblNo) {
    return logic.createTMSHouseBillWithBFSOneData(client, company, hblNo);
  }
}