package cloud.datatp.tms.bill.models;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

import cloud.datatp.jobtracking.entity.ISupportJobTracking;
import cloud.datatp.tms.bill.entity.TMSBill;
import cloud.datatp.tms.bill.entity.TMSBill.TMSBillProcessStatus;
import cloud.datatp.tms.bill.entity.TMSBillCustomer;
import cloud.datatp.tms.bill.entity.TMSBillFee;
import cloud.datatp.tms.bill.entity.TMSBillForwarderTransport;
import cloud.datatp.tms.bill.entity.TMSBillGoods;
import cloud.datatp.tms.bill.entity.TMSBillJob;
import cloud.datatp.tms.bill.entity.TMSBillRating;
import cloud.datatp.tms.bill.entity.TMSBillReceiver;
import cloud.datatp.tms.bill.entity.TMSBillSender;
import cloud.datatp.tms.bill.entity.TMSBillType;
import cloud.datatp.tms.housebill.entity.TMSHouseBill;
import cloud.datatp.tms.housebill.entity.TMSImportAndExport;
import cloud.datatp.tms.housebill.entity.TMSHouseBill.PackingMethod;
import cloud.datatp.tms.housebill.entity.TMSHouseBill.Purpose;
import cloud.datatp.tms.housebill.entity.TMSHouseBill.TypeOfTransportation;
import jakarta.persistence.Column;
import lombok.Getter;
import lombok.Setter;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.data.db.entity.EditState;
import net.datatp.module.data.db.entity.StorageState;
import net.datatp.module.resource.location.LocationLogic;
import net.datatp.module.resource.location.entity.Location;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;

@Getter @Setter
public class TMSBillModel implements ISupportJobTracking {
  //Bill info
  private Long id;
  private Long jobTrackingId;
  private Long jobTrackingProjectId;
  private String uikey;
  private String label;
  private TMSBillType type;
  private TMSBillProcessStatus processStatus;
  
  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  private Date    dateTime;
  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  private Date   deliveryPlan;
  
  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  private Date    closePayment;
  
  private String  description;
  
  private String  time;
  private String  estimateTime;
  private Integer delayedTime;
  
  //forwarder
  private String  truckType;
  private String  truckNo;
  private String  licensePlate;
  
  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "modified_truck_no")
  private Date   modifiedTruckNo;
  private String modifiedTruckNoBy;
  
  private String mode;
  private Long   containerInHandPlanId;
  private String containerNo;
  private String sealNo;
  private Long   vendorId;
  private String vendorFullName;
  private String collect;
  private String office;
  
  //goods
  private double quantity;
  private double goodsCountQuantity;
  private String quantityUnit;
  private double weight;
  private double chargeableWeight;
  private String weightUnit;
  private double volume;
  private String volumeAsText;
  private double chargeableVolume;
  private String volumeUnit;
  private String goodsDescription;
  
  //review. not need
  private String goods;
  private String goodsType;
  private String goodsEstimatedValueCurrency;
  private double goodsEstimatedValue;
  
  private String senderFullName;
  private String senderContact;
  private String senderAddress;
  private String senderStreetName;
  private String senderMapAddress;
  private Double senderMapLat;
  private Double senderMapLng;
  private Long   senderLocationId;
  private Location senderLocation;
  
  private String receiverFullName;
  private String receiverContact;
  private String receiverAddress;
  private String receiverStreetName;
  private String receiverMapAddress;
  private Double receiverMapLat;
  private Double receiverMapLng;
  private Long   receiverLocationId;
  private Location receiverLocation;
  
  //Booking
  private String hwbNo;;
//  private String verifySummaryNote;

  //Rating
  private String driverRating;
  private String vehicleArrivedOnTime;
  private String updateInfo;
  private String updateVehicleLocation;
  private String vendorRating;
  
  //Fee
  private double fixedPayment;
  private double extraPayment;
  private String feedback;
  private String paymentNote;
  private Long   payOnBehalfCustomerId;
  private String payOnBehalfCustomerName;
  private List<TMSBillJob> jobs;
  
  private boolean sendEmail;
  
  //Job Tracking
  private int stepDoneCount;
  private String currentStep;
  private Long tmsJobTrackingRuleId;
  //
  private EditState editState = EditState.ORIGIN;
  private StorageState storageState;
  
  
  //hbleFile
  private Long   hblId;
  private String fileNo;
  private String hblNo;
  private Long   customerId;
  private String customerFullName;
  private String root;
  private Purpose              purpose;
  private PackingMethod        method;
  private TypeOfTransportation typeOfTransportation;
  private Long   carrierId;
  private String carrierLabel;
  private String bookingCode;
  private String declarationNumber;
  private String etaCutOffTimeNote;
  
  //remove
  private String etaCutOffTime;
  private String carrierFullName;
  
  private boolean updateVehicleInfo;
  private boolean updateHouseBill;
  
  public boolean isDeleted() {
    return EditState.DELETED.equals(editState) ? true : false;
  }
  
  public String getUikey() {
    return StringUtil.isBlank(uikey) ? id.toString() : uikey;
  }
  
  public TMSBill mergeToTMSBill(TMSBill bill) {
    if(bill.isNew()) {
      bill.setStorageState(StorageState.ACTIVE);
    } else {
      bill.setStorageState(storageState);
    }
    if(StringUtil.isBlank(label)) label = "/";
    bill.setJobTrackingId(jobTrackingId);
    bill.setTmsHouseBillId(hblId);
    bill.setSendEmail(sendEmail);
    bill.setLabel(label);
    bill.setType(type);
    bill.setDeliveryPlan(null);
    if(this.dateTime != null)     bill.setDeliveryPlan(dateTime);
    if(this.deliveryPlan != null) bill.setDeliveryPlan(deliveryPlan);
    bill.setDescription(description);
    bill.setTime(time);
    bill.setProcessStatus(processStatus);
    bill.setCollect(StringUtil.isBlank(collect) ? null : collect);
    bill.setOffice(office);
    bill.setContainerInHandPlanId(containerInHandPlanId);
    bill.setDelayedTime(delayedTime);
    if((estimateTime != null && !estimateTime.equals(bill.getEstimateTime()) || (bill.getEstimateTime() != null && bill.getEstimateTime().equals(estimateTime)))) {
      bill.setModifiedEstimateTime(new Date());
    }
    if(StringUtil.isEmpty(estimateTime)) {
      bill.setDelayedTime(null);
    } 
    bill.setEstimateTime(estimateTime);
    //
    TMSBillForwarderTransport forwarder = bill.getTmsBillForwarderTransport();
    forwarder.setTruckType(truckType);
    forwarder.setTruckNo(truckNo);
    forwarder.setModifiedTruckNo(modifiedTruckNo);
    forwarder.setModifiedTruckNoBy(modifiedTruckNoBy);
    forwarder.setMode(mode);
    forwarder.setContainerNo(containerNo);
    forwarder.setSealNo(sealNo);
    forwarder.setCarrierId(carrierId);
    forwarder.setCarrierFullName(carrierFullName);
    forwarder.setVendorId(vendorId);
    forwarder.setVendorFullName(vendorFullName);
    //Booking
    forwarder.setEtaCutOffTime(etaCutOffTime);
    forwarder.setDeclarationNumber(declarationNumber);
    forwarder.setBookingCode(bookingCode);
    forwarder.setHwbNo(hwbNo);
    //
    TMSBillGoods goods = bill.getTmsBillGoods();
    goods.setQuantity(quantity);
    goods.setGoodsCountQuantity(goodsCountQuantity);
    goods.setQuantityUnit(quantityUnit);
    goods.setWeight(weight);
    goods.setChargeableWeight(chargeableWeight);
    goods.setWeightUnit(weightUnit);
    goods.setChargeableVolume(chargeableVolume);
    goods.setVolume(volume);
    goods.setVolumeAsText(volumeAsText);
    goods.setVolumeUnit(volumeUnit);
    goods.setGoodsDescription(goodsDescription);
    
    TMSBillCustomer customer = bill.getCustomer();
    if(customer == null) customer = new TMSBillCustomer();
    customer.setCustomerFullName(customerFullName);
    customer.setCustomerId(customerId);
    bill.setCustomer(customer);
    
    bill = updateSenderReceiver(bill);
    
    TMSBillFee fee = bill.getTmsBillFee();
    fee.setPayOnBehalfCustomerId(payOnBehalfCustomerId);
    fee.setPayOnBehalfCustomerName(payOnBehalfCustomerName);
    
    fee.updateFixedPayment(vendorFullName, vendorId, truckType, fixedPayment);
    fee.updateExtraPayment(vendorFullName, vendorId, truckType, extraPayment);
    fee.setClosePayment(closePayment);
    
    if(fee.getExtraPayment() != extraPayment || fee.getFixedPayment() != fixedPayment) fee.caculateProfit();
    fee.setPaymentNote(paymentNote);
    return bill;
  }
  
  public TMSBill updateLocation(ClientInfo client, LocationLogic locationLogic, TMSBill bill) {
    updateSenderLocation(client, locationLogic, bill);
    updateReceiverLocation(client, locationLogic, bill);
    return bill;
  }
  
  public TMSBill updateSenderReceiver(TMSBill bill) {
    TMSBillReceiver receiver = bill.getReceiver();
    if(receiver == null) receiver = new TMSBillReceiver();
    receiver.setReceiverFullName(receiverFullName);
    receiver.setReceiverContact(receiverContact);
    receiver.setReceiverAddress(receiverAddress);
    receiver.setReceiverStreetName(receiverStreetName);
    receiver.setReceiverLat(receiverMapLat);
    receiver.setReceiverLng(receiverMapLng);
    bill.setReceiver(receiver);
    
    TMSBillSender sender = bill.getSender();
    if(sender == null) sender = new TMSBillSender();
    sender.setSenderFullName(senderFullName);
    sender.setSenderContact(senderContact);
    sender.setSenderAddress(senderAddress);
    sender.setSenderStreetName(senderStreetName);
    sender.setSenderLat(senderMapLat);
    sender.setSenderLng(senderMapLng);
    bill.setSender(sender);
    return bill;
  }
  
  public TMSBill updateSenderLocation(ClientInfo client, LocationLogic locationLogic, TMSBill bill) {
    if(senderLocationId == null && bill.getTmsBillForwarderTransport().isImport()) {
      bill.getTmsBillForwarderTransport().setWarehouseLabel(null);
      bill.getTmsBillForwarderTransport().setWarehouseId(null);
    }
    
    if(bill.getSender() == null) bill.setSender(new TMSBillSender());
    bill.getSender().setSenderLocationId(senderLocationId);
    bill.getTmsBillForwarderTransport().setWarehouseLabel(senderAddress);
    bill.getTmsBillForwarderTransport().setWarehouseId(senderLocationId);
    return bill;
  }
  
  public TMSBill updateReceiverLocation(ClientInfo client, LocationLogic locationLogic, TMSBill bill) {
    if(receiverLocationId == null && bill.getTmsBillForwarderTransport().isExport()) {
      bill.getTmsBillForwarderTransport().setWarehouseLabel(null);
      bill.getTmsBillForwarderTransport().setWarehouseId(null);
    }
    
    if(bill.getReceiver() == null) bill.setReceiver(new TMSBillReceiver());
    bill.getReceiver().setReceiverLocationId(receiverLocationId);
    bill.getTmsBillForwarderTransport().setWarehouseLabel(receiverAddress);
    bill.getTmsBillForwarderTransport().setWarehouseId(receiverLocationId);
    return bill;
  }
  
  public TMSBillRating mergeToTMSBillRating(TMSBillRating rating) {
    rating.setDriverRating(driverRating);
    rating.setVehicleArrivedOnTime(vehicleArrivedOnTime);
    rating.setUpdateInfo(updateInfo);
    rating.setUpdateVehicleLocation(updateVehicleLocation);
    rating.setVendorRating(vendorRating);

    return rating;
  }
  
  public boolean isExport() {
    if(mode != null && mode.toUpperCase().contains("EXPORT")) return true;
    return false;
  }
  
  public boolean isImport() {
    if(mode != null && mode.toUpperCase().contains("IMPORT")) return true;
    return false;
  }
  
  public TMSHouseBill updateHouseBill(TMSHouseBill houseBill) {
    houseBill.setHblNo(hblNo);
    houseBill.setFileNo(fileNo);
    houseBill.setCustomerId(customerId);
    houseBill.setCustomerFullName(customerFullName);
    houseBill.setRoot(root);
    houseBill.setBookingCode(bookingCode);
    houseBill.setDeclarationNumber(declarationNumber);
    
    houseBill.setPurpose(purpose);
    houseBill.setMethod(method);
    houseBill.setTypeOfTransportation(typeOfTransportation);
    
    TMSImportAndExport importAndExport = houseBill.getImportAndExport();
    if(importAndExport == null) importAndExport = new TMSImportAndExport();
    importAndExport.setCarrierId(carrierId);
    importAndExport.setCarrierFullName(carrierFullName);
    importAndExport.setEtaCutOffTimeNote(etaCutOffTimeNote);
    if(houseBill.isExport()) {
      importAndExport.setWarehouseContact(receiverContact);
      importAndExport.setWarehouseLocationId(receiverLocationId);
      importAndExport.setWarehouseLocationLabel(receiverAddress);
    }
    if(houseBill.isImport()) {
      importAndExport.setWarehouseContact(senderContact);
      importAndExport.setWarehouseLocationId(senderLocationId);
      importAndExport.setWarehouseLocationLabel(senderAddress);
    }
    houseBill.setImportAndExport(importAndExport);
    
    return houseBill;
  }
  
  public static Long [] asArrayIds(List<TMSBillModel> models) {
    Long [] ids = new Long[models.size()];
    for(int i = 0 ; i < models.size(); i++) {
      Long tmsBillId = models.get(i).getId();
      if(tmsBillId == null) continue;
      ids[i] = tmsBillId;
    }
    return ids;
  }

  @Override
  public Long computeCustomJobTrackingId() {
    return customerId;
  }

  @Override
  public String computeCustomJobTrackingType() {
    return "tms-bill";
  }
  
  @Override
  public Long getJobTrackingProjectRuleId() {
    return tmsJobTrackingRuleId;
  }
}