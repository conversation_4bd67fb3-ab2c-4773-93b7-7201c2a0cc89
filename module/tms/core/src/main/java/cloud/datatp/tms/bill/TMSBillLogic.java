package cloud.datatp.tms.bill;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.collections4.map.HashedMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import cloud.datatp.bfsone.BFSOneDataLogic;
import cloud.datatp.fleet.vehicle.VehicleTripGoodsTrackingLogic;
import cloud.datatp.fleet.vehicle.entity.VehicleFleet;
import cloud.datatp.fleet.vehicle.entity.VehicleTrip;
import cloud.datatp.fleet.vehicle.entity.VehicleTripGoodsTracking;
import cloud.datatp.fleet.vehicle.entity.VehicleTripGoodsTracking.VehicleTripGoodsTrackingStatus;
import cloud.datatp.jobtracking.JobTrackingIssueLogic;
import cloud.datatp.jobtracking.JobTrackingLogic;
import cloud.datatp.jobtracking.JobTrackingProjectLogic;
import cloud.datatp.jobtracking.entity.JobTracking;
import cloud.datatp.jobtracking.rule.JobTrackingRuleConfigLogic;
import cloud.datatp.jobtracking.rule.JobTrackingRuleService;
import cloud.datatp.tms.bill.entity.TMSBill;
import cloud.datatp.tms.bill.entity.TMSBillActivity;
import cloud.datatp.tms.bill.entity.TMSBillAttachment;
import cloud.datatp.tms.bill.entity.TMSBillFee;
import cloud.datatp.tms.bill.entity.TMSBillFee.CostCopySource;
import cloud.datatp.tms.bill.entity.TMSBillForwarderTransport;
import cloud.datatp.tms.bill.entity.TMSBillGoods;
import cloud.datatp.tms.bill.entity.TMSBillRating;
import cloud.datatp.tms.bill.entity.TMSBillResponsibility;
import cloud.datatp.tms.bill.entity.TMSBillResponsibility.TMSBillResponsibilityType;
import cloud.datatp.tms.bill.entity.TMSBillType;
import cloud.datatp.tms.bill.jobtracking.event.TMSBillBotJobTrackingRuleEvent;
import cloud.datatp.tms.bill.models.TMSBillModel;
import cloud.datatp.tms.bill.models.VerifyTMSBillFeeRequest;
import cloud.datatp.tms.bill.models.VerifyTMSBillVehicleInfoRequest;
import cloud.datatp.tms.bill.repository.TMSBillActivityRepository;
import cloud.datatp.tms.bill.repository.TMSBillAttachmentRepository;
import cloud.datatp.tms.bill.repository.TMSBillCollectRepository;
import cloud.datatp.tms.bill.repository.TMSBillForwarderTransportRepository;
import cloud.datatp.tms.bill.repository.TMSBillRatingRepository;
import cloud.datatp.tms.bill.repository.TMSBillRepository;
import cloud.datatp.tms.bill.repository.TMSBillResponsibilityRepository;
import cloud.datatp.tms.bill.repository.TMSBillWorkingStepRepository;
import cloud.datatp.tms.event.TMSBotSyncVendorBillEvent;
import cloud.datatp.tms.housebill.TMSHouseBillLogic;
import cloud.datatp.tms.housebill.entity.TMSHouseBill;
import cloud.datatp.tms.partner.TMSPartnerLogic;
import cloud.datatp.vendor.TMSVendorBillLogic;
import cloud.datatp.vendor.entity.TMSVendorBill;
import cloud.datatp.vendor.entity.TMSVendorBillTracking;
import cloud.datatp.vendor.entity.TMSVendorBillTracking.TripStatus;
import jakarta.annotation.PostConstruct;
import lombok.Getter;
import net.datatp.lib.executable.ExecutableContext;
import net.datatp.module.account.AccountLogic;
import net.datatp.module.account.entity.Account;
import net.datatp.module.accounting.settings.CashFlow;
import net.datatp.module.app.AppEnv;
import net.datatp.module.bot.BotEvent.ProcessMode;
import net.datatp.module.bot.BotService;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.company.CompanyConfigLogic;
import net.datatp.module.company.entity.Company;
import net.datatp.module.company.entity.CompanyConfig;
import net.datatp.module.core.security.SecurityLogic;
import net.datatp.module.data.db.DAOService;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.entity.StorageState;
import net.datatp.module.data.db.query.ClauseFilter;
import net.datatp.module.data.db.query.ConditionFilter;
import net.datatp.module.data.db.query.EntityTable;
import net.datatp.module.data.db.query.Join;
import net.datatp.module.data.db.query.OptionFilter;
import net.datatp.module.data.db.query.RangeFilter;
import net.datatp.module.data.db.query.SearchFilter;
import net.datatp.module.data.db.query.SqlQuery;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.data.db.seq.SeqService;
import net.datatp.module.groovy.GroovyRepositoryManager;
import net.datatp.module.monitor.SourceType;
import net.datatp.module.resource.location.LocationLogic;
import net.datatp.module.service.ExecutableUnitManager;
import net.datatp.module.storage.CompanyStorage;
import net.datatp.module.storage.IStorageService;
import net.datatp.util.dataformat.DataSerializer;
import net.datatp.util.ds.Collections;
import net.datatp.util.ds.MapObject;
import net.datatp.util.error.RuntimeError;
import net.datatp.util.text.StringUtil;

@Component
public class TMSBillLogic extends DAOService {
  @Autowired
  GroovyRepositoryManager groovyRepoManager;

  @Autowired
  ExecutableUnitManager executableUnitManager;

  @Getter
  @Autowired
  TMSBillRepository tmsBillRepo;

  @Getter
  @Autowired
  TMSBillForwarderTransportRepository forwarderTransportRepo;

  @Autowired
  TMSBillResponsibilityRepository tmsBillResponsibilityRepo;

  @Autowired
  TMSBillActivityRepository activityRepo;

  @Autowired
  TMSBillRatingRepository tmsBillRatingRepo;

  @Autowired
  TMSBillWorkingStepRepository tmsBillWorkingStepRepo;

  @Autowired
  TMSBillAttachmentRepository attachmentRepo;

  @Autowired
  IStorageService storageService;

  @Autowired
  JobTrackingProjectLogic jobTrackingProjectLogic;

  @Autowired
  JobTrackingLogic jobTrackingLogic;

  @Autowired
  JobTrackingIssueLogic jobTrackingIssueLogic;

  @Autowired
  JobTrackingRuleService jobTrackingRuleService;

  @Autowired
  JobTrackingRuleConfigLogic jobTrackingRuleConfigLogic;
  
  @Autowired
  AccountLogic accountLogic;

  @Autowired
  TMSPartnerLogic tmsPartnerLogic;

  @Autowired
  SecurityLogic securityLogic;

  @Autowired
  CompanyConfigLogic companyConfigLogic;

  @Autowired
  LocationLogic locationLogic;

  @Autowired
  SeqService seqService;

  @Autowired
  TMSVendorBillLogic vendorBillLogic;
  
  @Autowired
  VehicleTripGoodsTrackingLogic trackingLogic;

  @Autowired
  TMSBillCollectRepository tmsBillCollectRepo;
  
  @Autowired
  BFSOneDataLogic   bfsOneDataLogic;
  
  @Autowired
  TMSHouseBillLogic houseBillLogic;
  
  @Autowired 
  BotService botService;

  String SCRIPT_DIR;

  @Autowired
  AppEnv appEnv;

  @Value("${app.env:#{null}}")
  String env;


  @PostConstruct
  public void onInit() {
    SCRIPT_DIR = appEnv.addonPath("logistics", "groovy/lgc/tms/");
    seqService.createIfNotExists(TMSBill.SEQUENCE, 10);
    seqService.createIfNotExists(TMSBill.COLLECT_SEQUENCE, 10);
  }

  public TMSBill getTMSBillByCode(ClientInfo client, Company company, String code) {
    return tmsBillRepo.getByCode(company.getId(), code);
  }

  public TMSBill getTMSBillById(ClientInfo client, Company company, Long id) {
    return tmsBillRepo.getById(company.getId(), id);
  }

  public boolean deleteTMSBills(ClientInfo client, Company company, List<Long> ids) {
    tmsBillRepo.deleteAllById(ids);
    return true;
  }
  
  public boolean deleteTMSBillOwnerByIds(ClientInfo client, Company company, List<Long> ids) {
    Account account = accountLogic.getEditable(client, client.getRemoteUser());
    tmsBillRepo.deleteTMSBillOwnerByIds(company.getId(), account.getId(), ids);
    return true;
  }

  public TMSBill getTMSBillByRefCode(ClientInfo client, Company company, String refSource, String refCode) {
    return tmsBillRepo.getByRefCode(company.getId(), refSource, refCode);
  }

  public TMSBill getTMSBillByRefId(ClientInfo client, Company company, String refSource, String refId) {
    return tmsBillRepo.getByRefId(company.getId(),refSource, refId);
  }
  
  public List<TMSBill> findByHouseBill(ClientInfo client, Company company, Long hblId) {
    return tmsBillRepo.findByHouseBill(company.getId(), hblId);
  }
  
  
  public boolean updateMessageId(ClientInfo client, Company company,  Long messageId, List<Long> tmsBillIds) {
    tmsBillRepo.updateMessageId(company.getId(), messageId, tmsBillIds);
    return true;
  }
  
  public TMSBill saveTMSBill(ClientInfo client, Company company, TMSBill bill) {
    return saveTMSBill(client, company, bill, true);
  }

  public TMSBill saveTMSBill(ClientInfo client, Company company, TMSBill bill, boolean autoVerifyJobTracking) {
    if (bill.isNew()) {
      if(bill.getResponsibleAccountId() == null && bill.getResponsibleAccountId() == null) {
        Account account = accountLogic.getEditable(client, client.getRemoteUser());
        bill.setResponsibleAccountId(account.getId());
        bill.setResponsibleFullName(account.getFullName());
      }
    }
    if(bill.getTmsBillFee() != null) bill.getTmsBillFee().caculateCost();
    bill.set(client, company);
    bill = tmsBillRepo.save(bill);
    return bill;
  }

  public boolean isAutoVerifyJobTrackingEvenHandler(ClientInfo client, Company company) {
    CompanyConfig companyConfig = companyConfigLogic.getCompanyConfigByCompanyId(client, company.getId());
    Boolean autoVerifyJobTracking = companyConfig.getAttributeAsBoolean(TMSBill.AUTO_VERIFY_JOB_TRACKING);
    return autoVerifyJobTracking;
  }
  
  public void autoBotTMSBillJobTracking(ClientInfo client, Company company, List<TMSBillModel> models){
    if (isAutoVerifyJobTrackingEvenHandler(client, company)) {
      TMSBillBotJobTrackingRuleEvent botEvent = new TMSBillBotJobTrackingRuleEvent(client, company, models);
      botEvent.withProcessMode(ProcessMode.Queueable);
      botService.broadcast(SourceType.UserBot, botEvent);
    }
  }

  public  List<TMSBillModel> saveTMSBillModels(ClientInfo client, Company company, List<TMSBillModel> models) {
    List<TMSBill>          billUpdateds  = new ArrayList<>();
    Map<Long, TMSHouseBill> hbUpdates     = new HashedMap<>();
    for(TMSBillModel sel : models) {
      Long id = sel.getId();
      if(sel.isDeleted()) {
        if(id == null) continue;
        TMSBill bill = getTMSBillById(client, company, id);
        bill.setStorageState(StorageState.ARCHIVED);
        bill = saveTMSBill(client, company, bill);
        continue;
      }

      TMSBill bill = new TMSBill(sel.getType());
      if(id != null) {
        bill = getTMSBillById(client, company, id);
      } else {
        bill.genCode(company.getCode(), seqService.nextSequence(TMSBill.SEQUENCE));
        bill.setTmsBillFee(new TMSBillFee(bill.getCode()));
        Account account = accountLogic.getEditable(client, client.getRemoteUser());
        bill.setResponsibleAccountId(account.getId());
        bill.setResponsibleFullName(account.getFullName());
        if(getTMSBillByCode(client, company, bill.getCode()) != null) {
          bill.genCodeWithTimeId();
        }
      }
      if(bill == null) throw RuntimeError.UnknownError("Bill is Null!!! {0}", DataSerializer.JSON.toString(sel));
      bill = sel.mergeToTMSBill(bill);
      bill = sel.updateLocation(client, locationLogic, bill);
      bill = saveTMSBill(client, company, bill);
      sel.setId(bill.getId());
      updateVendorBill(client, company, sel);
      billUpdateds.add(bill);
      
      if(sel.isUpdateHouseBill() && sel.getHblId() != null) {
        TMSHouseBill hb = houseBillLogic.getById(client, company, sel.getHblId());
        hb = sel.updateHouseBill(hb);
        hbUpdates.put(sel.getHblId(), hb);
      }
    }
    
    if(Collections.isNotEmpty(hbUpdates.values())) {
      tmsBillRepo.flush();
      for(TMSHouseBill hb : hbUpdates.values()) {
        houseBillLogic.saveTMSHouseBill(client, company, hb);
      }
    }
    
    TMSBotSyncVendorBillEvent event = new TMSBotSyncVendorBillEvent(client, company, billUpdateds);
    event.setProcessMode(ProcessMode.Queueable);
    botService.broadcast(SourceType.UserBot, event);
    
    autoBotTMSBillJobTracking(client, company, models);
    return models;
  }
  
  private TMSVendorBill updateVendorBill(ClientInfo client, Company company, TMSBillModel billModel) {
    TMSVendorBill vendorBill = vendorBillLogic.getByTMSBillId(client, company, billModel.getId());
    if(vendorBill != null) {
      vendorBill.setFeedback(billModel.getFeedback());
      vendorBill = vendorBillLogic.saveVendorBill(client, company, vendorBill) ;
    }
    if(!billModel.isUpdateVehicleInfo()) return vendorBill;
    if(vendorBill == null) vendorBill = vendorBillLogic.getOrCreateVendorBillByTmsBill(client, company, billModel.getId());
    vendorBill.getBillTrackings().clear();
    TMSVendorBillTracking billTracking = new TMSVendorBillTracking();
    billTracking.setLicensePlate(billModel.getLicensePlate());
    billTracking.setDescription(billModel.getTruckNo());
    billTracking.setStatus(VehicleTripGoodsTrackingStatus.CONFIRMED);
    billTracking.setTripStatus(TripStatus.DONE);
    billTracking.setTransportType(billModel.getTruckNo());
    vendorBill.setFeedback(billModel.getFeedback());
    vendorBill.getBillTrackings().add(billTracking);
    return vendorBillLogic.saveVendorBill(client, company, vendorBill) ;
  }

  public List<TMSBill> findTMSBills(ClientInfo client, Company company, Long[] ids) {
    return tmsBillRepo.findTMSBills(company.getId(), ids);
  }
  
  public TMSBill newTMSBill(ClientInfo client, Company company, TMSBill bill) {
    Account account = accountLogic.getEditable(client, client.getRemoteUser());
    bill.setType(TMSBillType.FORWARDER);
    bill.genCode(company.getCode(), seqService.nextSequence(TMSBill.SEQUENCE));
    bill.setLabel(bill.getCode());
    bill.setResponsibleAccountId(account.getId());
    bill.setResponsibleFullName(account.getFullName());

    TMSBillFee fee = bill.getTmsBillFee();
    fee.setTmsBillCode(bill.getCode());
    fee.setCurrency("VND");

    TMSBillGoods goods = bill.getTmsBillGoods();
    goods.setQuantityUnit("PKG");
    goods.setWeightUnit("KGM");
    goods.setVolumeUnit("CBM");
    bill.setTmsBillGoods(goods);
//    bill.setCurrentResponsibility(newTMSBillResponsibility(client, company, bill));
    return bill;
  }

  public boolean changeTMSBillStorageState(ClientInfo client, ChangeStorageStateRequest req) {
    tmsBillRepo.setStorageState(req.getNewStorageState(), req.getEntityIds());
    return true;
  }

  public List<SqlMapRecord> findTMSBills(ClientInfo client, Company company, SqlQueryParams params) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/tms/groovy/TMSSql.groovy";
    params.addParam("companyId", company.getId());
    return searchDbRecords(client, scriptDir, scriptFile, "FindTMSBillQuery", params);
  }

  public List<SqlMapRecord> searchTMSBills(ClientInfo client, Company company, SqlQueryParams params) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    ExecutableContext ctx =
        new ExecutableContext()
            .withScriptEnv(scriptDir, "cloud/datatp/tms/bill/SearchLogicUnit.java", "SearchTMSBill")
            .withParam(this).withParam(client).withParam(company).withParam(params);
    return (List<SqlMapRecord>) executableUnitManager.execute(ctx);
  }

  public List<SqlMapRecord> searchTMSBillReport(ClientInfo client, Company company, SqlQueryParams params) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    ExecutableContext ctx =
        new ExecutableContext()
        .withScriptEnv(scriptDir, "cloud/datatp/tms/bill/SearchLogicUnit.java", "SearchTMSBillReport")
        .withParam(this).withParam(client).withParam(company).withParam(params);
    return (List<SqlMapRecord>) executableUnitManager.execute(ctx);
  }

  public List<TMSBill> collectBills(ClientInfo client, Company company, Long [] ids) {
    List<TMSBill> bills = findTMSBills(client, company, ids);
    String token = TMSBill.genCollectToken(seqService.nextSequence(TMSBill.COLLECT_SEQUENCE));
    for(TMSBill bill : bills) {
      bill.setCollect(token);
      bill = saveTMSBill(client, company, bill);
    }
    return bills;
  }

  // TMSBill Responsibility

  public List<TMSBillAttachment> findTMSBillAttachments(ClientInfo client, Company company, Long tmsBillId) {
    return attachmentRepo.findTMSBillAttachments(company.getId(), tmsBillId);
  }
  
  public TMSBillResponsibility getTMSBillResponsibilityByCode(ClientInfo client, Company company, String code) {
    return tmsBillResponsibilityRepo.getByCode(company.getId(), code);
  }

  public TMSBillResponsibility getTMSBillResponsibilityByType(ClientInfo client, Company company, String tmsBillCode, TMSBillResponsibilityType type) {
    return tmsBillResponsibilityRepo.getByTMSBillCodeAndType(company.getId(), tmsBillCode, type);
  }

  public List<TMSBillResponsibility> saveTMSBillResponsibilities(ClientInfo client, Company company, List<TMSBillResponsibility> responsibilities) {
    return tmsBillResponsibilityRepo.saveAll(responsibilities);
  }

  public void flushResponsibility() {
    tmsBillResponsibilityRepo.flush();
  }

  public TMSBillResponsibility saveTMSBillResponsibility(ClientInfo client, Company company, TMSBillResponsibility br) {
    br.set(client, company);
    return tmsBillResponsibilityRepo.save(br);
  }

//  public TMSBill createTMSBillResponsibility(ClientInfo client, Company company, TMSBillResponsibilityRequest req) {
//    TMSBill tmsBill = req.getTmsBill();
//    TMSBillResponsibility oldState = tmsBill.getCurrentResponsibility();
//    oldState.setActive(false);
//    oldState = saveTMSBillResponsibility(client, company, oldState);
//
//    TMSBillResponsibility newResponsibility = req.getResponsibility();
//    newResponsibility = saveTMSBillResponsibility(client, company, newResponsibility);
//
//    if(Objects.nonNull(req.getActivity())) saveTMSBillActivity(client, company, req.getActivity());
//
//    tmsBill.setCurrentResponsibility(newResponsibility);
//    tmsBill.set(client, company);
//    return tmsBillRepo.save(tmsBill);
//  }

  public List<SqlMapRecord> searchTMSBillResponsibilities(ClientInfo client, Company company, SqlQueryParams params) {
    params.addParam("companyId", company.getId());
    SqlQuery query = new SqlQuery()
        .ADD_TABLE(new EntityTable(TMSBillResponsibility.class).selectAllFields())
        .FILTER(ClauseFilter.company(TMSBillResponsibility.class))
        .FILTER(new ConditionFilter(TMSBillResponsibility.class, "code", "= :code").hasVariableCondition("code"))
        .FILTER(new ConditionFilter(TMSBillResponsibility.class, "tmsBillCode", "= :tmsBillCode").hasVariableCondition("tmsBillCode"))
        .FILTER(
            SearchFilter.isearch(TMSBillResponsibility.class, new String[]{"code", "label", "description"}))
        .FILTER(
            OptionFilter.storageState(TMSBillResponsibility.class),
            RangeFilter.modifiedTime(TMSBillResponsibility.class))
        .ORDERBY(new String[]{"label", "modifiedTime", "fromTime"}, "fromTime", "DESC");
    return query(client, query, params).getSqlMapRecords();
  }

  //TMSBill Activity
  public TMSBillActivity getTMSBillActivityByCode(ClientInfo client, Company company, String code) {
    return activityRepo.getByCode(company.getId(), code);
  }


  public List<TMSBillActivity> saveTMSBillActivities(ClientInfo client, Company company, List<TMSBillActivity> activities) {
    return activityRepo.saveAll(activities);
  }

  public TMSBillActivity saveTMSBillActivity(ClientInfo client, Company company, TMSBillActivity pa) {
    pa.set(client, company);
    return activityRepo.save(pa);
  }

  public List<SqlMapRecord> searchTMSBillActivities(ClientInfo client, Company company, SqlQueryParams params) {
    params.addParam("companyId", company.getId());
    SqlQuery query = new SqlQuery()
        .ADD_TABLE(new EntityTable(TMSBillActivity.class).selectAllFields())
        .JOIN(
            new Join("JOIN", TMSBillResponsibility.class)
            .ON("code", TMSBillActivity.class, "tmsBillResponsibilityCode")
            .addSelectField("label", "tmsBillResponsibilityLabel")
          )
        .FILTER(ClauseFilter.company(TMSBillActivity.class))
        .FILTER(new ConditionFilter(TMSBillActivity.class, "tmsBillCode", "= :tmsBillCode").hasVariableCondition("tmsBillCode"))
        .FILTER(new ConditionFilter(TMSBillActivity.class, "tmsBillResponsibilityCode", "= :tmsBillResponsibilityCode")
            .hasVariableCondition("tmsBillResponsibilityCode"))
        .FILTER(
            SearchFilter.isearch(TMSBillActivity.class, new String[]{"code", "label", "description"}))
        .FILTER(
            OptionFilter.storageState(TMSBillActivity.class),
            RangeFilter.modifiedTime(TMSBillActivity.class))
        .ORDERBY(new String[]{"label", "modifiedTime", "atTime"}, "atTime", "DESC");
    return query(client, query, params).getSqlMapRecords();
  }

  public TMSBillRating getTMSBillRatingByTMSBillId(ClientInfo client, Company company, Long tmsBillId) {
    return tmsBillRatingRepo.getByTMSBllId(tmsBillId);
  }

  public TMSBillRating saveTMSBillRating(ClientInfo client, Company company, TMSBillRating rating) {
    rating.set(client);
    return tmsBillRatingRepo.save(rating);
  }

  public List<TMSBillAttachment> findTMSBillAttachmentByTMSBillIds(ClientInfo client, Company company, Long [] ids) {
    List<TMSBillAttachment> attFiles = new ArrayList<>();
    for(Long id : ids) {
      List<TMSBillAttachment> subAtts = attachmentRepo.findTMSBillAttachments(company.getId(), id);
      if(Collections.isEmpty(subAtts)) continue;
      TMSBill bill = getTMSBillById(client, company, id);
      for(TMSBillAttachment sel : subAtts) {
        sel.setFileNo(bill.getLabel());
      }
      attFiles.addAll(subAtts);
    }

    return attFiles;
  }

  public List<TMSBillAttachment> saveTMSBillAttachments(
    ClientInfo client, Company company, Long tmsBillId, List<TMSBillAttachment> attachments, boolean removeOrphan) {
    CompanyStorage storage = storageService.createCompanyStorage(client, company.getCode());
    String storagePath = TMSBillAttachment.getTMSBillAttachmentStoragePath(tmsBillId);
    storage.saveAttachments(storagePath, attachments, removeOrphan);
    for (TMSBillAttachment attachment : attachments) {
      attachment.setTmsBillId(tmsBillId);
      attachment.set(client, company.getId());
    }
    attachments = attachmentRepo.saveAll(attachments);
    if(removeOrphan) {
      List<Long> idSet = TMSBillAttachment.getIds(attachments);
      if(Collections.isEmpty(idSet)) {
        attachmentRepo.deleteWithTMSBillId(company.getId(), tmsBillId);
      } else {
        attachmentRepo.deleteOrphan(company.getId(), tmsBillId, idSet);
      }
    }
    return attachments;
  }

  public TMSBill copyToTmsBillCost(ClientInfo client, Company company, Long id) {
    TMSBill tmsBill = getTMSBillById(client, company, id);
    if (tmsBill == null) {
      throw RuntimeError.IllegalArgument("Bill is not found with id:{0}", id);
    }
    TMSBillForwarderTransport transport  = tmsBill.getTmsBillForwarderTransport();
    TMSVendorBill             vendorBill = vendorBillLogic.getByTMSBillId(client, company, tmsBill.getId());
    TMSBillFee                tmsBillFee = tmsBill.getTmsBillFee();
    List<VehicleTripGoodsTracking> trackings = trackingLogic.findVehicleTripGoodTrackingByTMSBillId(client, company, id);
    if(Collections.isNotEmpty(trackings)) {
      tmsBillFee.getItems().removeIf(sel -> CashFlow.Outbound.equals(sel.getCashFlow()));
      for(VehicleTripGoodsTracking tracking : trackings) {
        VehicleTrip trip = trackingLogic.getVehicleLogic().getVehicleTrip(client, company, tracking.getVehicleTripId());
        if(trip == null || trip.getFleetId() == null) {
          tmsBillFee.addFixedPayment(tracking.getId(), VehicleTripGoodsTracking.TABLE_NAME, null, null, tracking.getVehicleType(), 0, null);
          continue;
        };
        Long fleetId = trip.getFleetId();
        VehicleFleet fleet = trackingLogic.getVehicleFleetLogic().getVehicleFleetById(client, company, fleetId);
        String vehicleLabel =  trip.getVehicleLabel() == null ? "" : trip.getVehicleLabel();
        String fleetLabel   =  trip.getFleetLabel() == null ? "" : trip.getFleetLabel();
        String note = fleetLabel + ": " + vehicleLabel;
        tmsBillFee.addFixedPayment(tracking.getId(), VehicleTripGoodsTracking.TABLE_NAME, fleet.getLabel(), fleet.getId(), tracking.getVehicleType(), tracking.getFixedCharge(), note);
        if(tracking.getExtraCharge() > 0)
          tmsBillFee.addExtraPayment(tracking.getId(), VehicleTripGoodsTracking.TABLE_NAME, fleet.getLabel(), fleet.getId(), tracking.getVehicleType(), tracking.getExtraCharge(), note);
      }
    } else if(vendorBill != null) {
      tmsBillFee.updateFixedPayment(transport.getVendorFullName(), transport.getVendorId(), transport.getTruckType(), vendorBill.getFixed());
      tmsBillFee.updateExtraPayment(transport.getVendorFullName(), transport.getVendorId(), transport.getTruckType(), vendorBill.getExtra());
    }
    return tmsBill;
  }


  public boolean syncCost(ClientInfo client, Company company, Long id, CostCopySource costSource) {
    TMSBill tmsBill = getTMSBillById(client, company, id);
    Account account = accountLogic.getEditable(client, client.getRemoteUser());
    if (tmsBill == null) {
      throw RuntimeError.IllegalArgument("Bill is not found with id:{0}", id);
    }
    TMSVendorBill vendorBill = vendorBillLogic.getOrCreateVendorBillByTmsBill(client, company, tmsBill.getId());
    if(costSource == CostCopySource.VendorBill) {
      TMSBill bill = copyToTmsBillCost(client, company, id);
      bill.getTmsBillFee().setCostCopySource(TMSVendorBill.TABLE_NAME);
      saveTMSBill(client, company, bill);
    } else if(costSource == CostCopySource.TMSBill) {
      TMSBillFee tmsBillFee = tmsBill.getTmsBillFee();
      vendorBill.setFixed(tmsBillFee.getFixedPayment());
      vendorBill.setExtra(tmsBillFee.getExtraPayment());
      vendorBill.setCost(tmsBillFee.getTotalPayment());
      
      tmsBill.getTmsBillFee().setCostCopySource(TMSBill.TABLE_NAME);
      saveTMSBill(client, company, tmsBill);
    }
    vendorBill.setAccountIdConfirmed(account.getId());
    vendorBill.setAccountFullNameConfirmed(account.getFullName());
    vendorBill.setVendorCostStatus(TMSVendorBill.VendorCostStatus.MANUAL_CONFIRM);
    vendorBill.set(client, company);
    vendorBillLogic.getRepo().save(vendorBill);
    return true;
  }

  public boolean updateTMSBillJobTrackingIdAndFlush(ClientInfo client, Company company, Long tmsBillId, Long jobTrackingId) {
    tmsBillRepo.updateJobTrackingId(company.getId(), tmsBillId, jobTrackingId);
    tmsBillRepo.flush();
    return true;
  }

  public boolean updateTMSBillJobTrackingId(ClientInfo client, Company company, Long tmsBillId, Long jobTrackingId) {
    tmsBillRepo.updateJobTrackingId(company.getId(), tmsBillId, jobTrackingId);
    return true;
  }
  
  public List<MapObject> findJobTrackingDataByTMSBillIds(ClientInfo client, Company company, Long [] ids) {
    List<TMSBill>   bills   = findTMSBills(client, company, ids);
    List<MapObject> results = new ArrayList<>();
    for(TMSBill bill : bills) {
      Long jobTrackingId = bill.getJobTrackingId();
      if(jobTrackingId == null) continue;
      JobTracking jobTracking = jobTrackingLogic.getJobTracking(client, company, jobTrackingId);
      MapObject data = DataSerializer.JSON.getObjectMapper().convertValue(jobTracking, MapObject.class);
      data.add("tmsBillId", bill.getId());
      results.add(data);
    }
    return results;
  }
  
  public List<TMSBill> verifyAndClosePaymentTMSBillFees(ClientInfo client, Company company, List<VerifyTMSBillFeeRequest> requests) {
    Map<Long, VerifyTMSBillFeeRequest> recordMap = new HashedMap<>();
    for(VerifyTMSBillFeeRequest rq : requests) {
      Long tmsBillId = rq.getTmsBillId();
      recordMap.put(tmsBillId, rq);
    }
    
    List<Long> tmsBillIds = Collections.transform(requests, request -> request.getTmsBillId());
    List<TMSBill>  bills  = findTMSBills(client, company, tmsBillIds.toArray(new Long[tmsBillIds.size()]));
    for(TMSBill bill : bills) {
      VerifyTMSBillFeeRequest rq = recordMap.get(bill.getId());
      TMSBillFee fee = bill.getTmsBillFee();
      fee.setVerifyPaymentNote(rq.getVerifyPaymentNote());
      if(StringUtil.isBlank(rq.getVerifyPaymentNote())) {
        fee.setVerifyPaymentInfo(true);
      } else {
        fee.setVerifyPaymentInfo(false);
      }
      if(rq.isClosePayment() && fee.getVerifyPaymentInfo()) fee.setClosePayment(new Date());
      TMSBillForwarderTransport transport = bill.getTmsBillForwarderTransport();
      transport.setVerifyVehicleInfoNote(rq.getVerifyVehicleInfoNote());
      if(StringUtil.isBlank(rq.getVerifyVehicleInfoNote())) {
        transport.setVerifyVehicleInfo(true);
      } else {
        transport.setVerifyVehicleInfo(false);
      }
      if(rq.isClosePayment() && transport.getVerifyVehicleInfo()) bill.setVehicleInfoLockDate(new Date());
    }
    return tmsBillRepo.saveAll(bills); 
  }
  
  public List<TMSBill> verifyAndCloseTMSBillVehicleInfo(ClientInfo client, Company company, List<VerifyTMSBillVehicleInfoRequest> requests) {
    Map<Long, VerifyTMSBillVehicleInfoRequest> recordMap = new HashedMap<>();
    for(VerifyTMSBillVehicleInfoRequest rq : requests) {
      Long tmsBillId = rq.getTmsBillId();
      recordMap.put(tmsBillId, rq);
    }
    
    List<Long> tmsBillIds = Collections.transform(requests, request -> request.getTmsBillId());
    List<TMSBill>  bills  = findTMSBills(client, company, tmsBillIds.toArray(new Long[tmsBillIds.size()]));
    for(TMSBill bill : bills) {
      VerifyTMSBillVehicleInfoRequest rq = recordMap.get(bill.getId());
      TMSBillForwarderTransport transport = bill.getTmsBillForwarderTransport();
      transport.setVerifyVehicleInfoNote(rq.getVerifyVehicleInfoNote());
      if(StringUtil.isBlank(rq.getVerifyVehicleInfoNote())) {
        transport.setVerifyVehicleInfo(true);
      } else {
        transport.setVerifyVehicleInfo(false);
      }
      if(rq.isCloseVehicleInfo() && transport.getVerifyVehicleInfo()) bill.setVehicleInfoLockDate(new Date());
    }
    return tmsBillRepo.saveAll(bills); 
  }
  
  public List<TMSBill> verifyTMSBillHblNos(ClientInfo client, Company company, List<Long> ids) {
    List<TMSBill>  bills  = findTMSBills(client, company, ids.toArray(new Long[ids.size()]));
    Set<String>    hblNos = new HashSet<>();
    for(TMSBill bill : bills) {
      String hblNo = bill.getTmsBillForwarderTransport().getHwbNo();
      if(StringUtil.isNotBlank(hblNo)) {
        hblNos.add(hblNo);
      }
    }
    SqlQueryParams params = new SqlQueryParams();
    params.addParam("hblNos", new ArrayList<>(hblNos));
    List<SqlMapRecord> bfsHbls   = bfsOneDataLogic.searchBFSOneHouseBill(client, company, params);
    List<String>       bfsHblNos = Collections.transform(bfsHbls, b -> b.getString("hawbNo"));
    for(TMSBill bill : bills) {
      String hblNo = bill.getTmsBillForwarderTransport().getHwbNo();
      if(bfsHblNos.contains(hblNo)) { 
        bill.getTmsBillForwarderTransport().setVerifyHblNo(true);
      } else {
        bill.getTmsBillForwarderTransport().setVerifyHblNo(false);
      }
    }
    return tmsBillRepo.saveAll(bills);
  }

//  public boolean updatePaymentStatus(ClientInfo clientInfo, ICompany company, Long billFeeId, PaymentStatus paymentStatus) {
//    tmsBillCollectRepo.updatePaymentStatus(paymentStatus, billFeeId);
//    return true;
//  }
}