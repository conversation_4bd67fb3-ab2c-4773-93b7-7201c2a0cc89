package cloud.datatp.tms.document;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.map.HashedMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cloud.datatp.tms.document.entity.InvoiceReconcileDocument;
import cloud.datatp.tms.document.entity.InvoiceSummaryReconcile;
import cloud.datatp.tms.document.event.InvReconcileBotCalculateDeplayedPaymentEvent;
import cloud.datatp.tms.document.repository.InvoiceReconcileDocumentRepository;
import cloud.datatp.tms.document.repository.InvoiceSummaryReconcileRepository;
import jakarta.annotation.PostConstruct;
import lombok.Getter;
import net.datatp.module.account.AccountLogic;
import net.datatp.module.account.entity.Account;
import net.datatp.module.account.entity.UserProfile;
import net.datatp.module.account.repository.UserProfileRepository;
import net.datatp.module.bot.BotService;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.communication.CommunicationMessageLogic;
import net.datatp.module.communication.entity.CommunicationAccount;
import net.datatp.module.communication.entity.Message;
import net.datatp.module.communication.entity.MessageDeliverType;
import net.datatp.module.communication.entity.TargetRecipient;
import net.datatp.module.company.entity.Company;
import net.datatp.module.core.security.entity.DataScope;
import net.datatp.module.data.db.DAOService;
import net.datatp.module.data.db.RecordGroupByMap;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.data.db.seq.SeqService;
import net.datatp.module.document.DocumentSetLogic;
import net.datatp.module.document.entity.DocumentSet;
import net.datatp.module.monitor.SourceType;
import net.datatp.util.ds.Collections;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;

@Component
public class InvReconcileDocumentLogic extends DAOService {
  @Autowired @Getter
  private InvoiceSummaryReconcileRepository repo;
  
  @Autowired
  private InvoiceReconcileDocumentRepository documentRepo;

  @Autowired
  private AccountLogic accountLogic;

  @Autowired
  private CommunicationMessageLogic messageLogic;

  @Autowired
  private DocumentSetLogic documentSetLogic;

  @Autowired
  private UserProfileRepository userProfileRepo;

  @Autowired
  private BotService botService;
  
  @Autowired
  SeqService seqService;

  @PostConstruct
  public void onInit() {
    seqService.createIfNotExists(InvoiceSummaryReconcile.SEQUENCE, 1);
  }


  public InvoiceSummaryReconcile loadInvoiceSummaryReconcileById(ClientInfo clientInfo, Company company, Long id) {
    return repo.loadById(company.getId(), id);
  }
  
  // User DynamicEntity
  public InvoiceSummaryReconcile saveInvoiceSummaryReconcile(ClientInfo clientInfo, Company company, InvoiceSummaryReconcile lolo) {
    InvReconcileBotCalculateDeplayedPaymentEvent botEvent = new InvReconcileBotCalculateDeplayedPaymentEvent(clientInfo, company, lolo);
    botService.broadcast(SourceType.UserBot, botEvent);
    lolo.set(clientInfo, company);
    if(lolo.isNew()) {
      lolo.genCode(seqService.nextSequence(InvoiceSummaryReconcile.SEQUENCE), lolo.getType());
    }
    return repo.save(lolo);
  }
  
  
  // User DynamicEntity
  public List<InvoiceSummaryReconcile> saveInvoiceSummaryReconciles(ClientInfo clientInfo, Company company, List<InvoiceSummaryReconcile> lolos) {
    for(InvoiceSummaryReconcile lolo: lolos){
      saveInvoiceSummaryReconcile(clientInfo, company, lolo);
    }
    return lolos;
  }
  
  public List<SqlMapRecord> searchInvoiceSummaryReconciles(ClientInfo client , Company company, SqlQueryParams sqlParams) {
    Account account = accountLogic.getEditable(client, client.getRemoteUser());
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/tms/document/groovy/TMSDocumentSql.groovy";
    String dataScope = (String) sqlParams.getParam("dataScope");
    if(DataScope.Owner.toString().equals(dataScope)) {
      sqlParams.addParam("accountId", account.getId());
    }
    sqlParams.addParam("companyId", company.getId());

    List<SqlMapRecord> lolos = searchDbRecords(client, scriptDir, scriptFile, "SearchTMSLOLOInvoiceReconciles", sqlParams);
    if(lolos.isEmpty()) return lolos;
    List<Long> loloIds = Collections.transform(lolos, (lolo) -> lolo.getLong("id"));
    List<SqlMapRecord> documentSetRecords = searchDbRecords(client, scriptDir, scriptFile, "FindTMSLOLOInvoiceReconcileDocumentsByLoLoId", sqlParams.addParam("loloIds", loloIds));
    
    Map<Long, Long> documentSetReconcileIdMap = new HashedMap<>();
    for(SqlMapRecord rec : documentSetRecords) {
      Long reconcileId   = rec.getLong("irId");
      Long documentSetId = rec.getLong("documentSetId");
      documentSetReconcileIdMap.put(documentSetId, reconcileId);
    }
    List<Long> ids = new ArrayList<>(documentSetReconcileIdMap.keySet());
    List<DocumentSet> documentSets = documentSetLogic.findDocumentSet(client, company, ids);

    RecordGroupByMap<Long, DocumentSet> documentSetGroupByMap = 
        new RecordGroupByMap<>(documentSets, (documentSet) -> {
          Long irId = documentSetReconcileIdMap.get(documentSet.getId());
      return irId;
    });
    
    for(SqlMapRecord lolo: lolos) {
      Long id = lolo.getLong("id");
      List<DocumentSet> docSets = documentSetGroupByMap.get(id) == null ? new ArrayList<>(): documentSetGroupByMap.get(id);
      lolo.put("documentSets", docSets);
    }
    return lolos;
  }

  public boolean deleteInvoiceSummaryReconciles(ClientInfo client , Company company, List<Long> ids) {
    repo.deleteAllById(ids);
    return true;
  }

  public boolean changeInvoiceSummaryReconcileStorageState(ClientInfo client, Company company, ChangeStorageStateRequest req) {
    repo.setStorageState(req.getNewStorageState(), req.getEntityIds());
    return true;
  }
  
  
  public InvoiceReconcileDocument createInvoiceReconcileDocument(ClientInfo client, Company company, Long docSetId, Long loloId) {
    InvoiceReconcileDocument loloDoc = new InvoiceReconcileDocument(docSetId, loloId);
    return documentRepo.save(loloDoc);
  }

  public boolean deleteInvoiceReconcileDocument(ClientInfo client, Company company, Long docSetId, Long loloId){
    documentRepo.deleteInvoiceReconcileDocument(docSetId, loloId);
    return true;
  }

  public Message createInvoiceReconcileMessage(ClientInfo client, Company company, Long loloId) {
    InvoiceSummaryReconcile lolo = loadInvoiceSummaryReconcileById(client, company, loloId);
    Message message = new Message();
    String name = "";
    String email = null;
    String gender = null;
    if(lolo.getResponsibleAccountId() != null) {
      Account account = accountLogic.getAccountById(client, lolo.getResponsibleAccountId());
      CommunicationAccount communicationAccount = messageLogic.getCommunicationAccount(client, account.getLoginId());
      UserProfile userProfile = userProfileRepo.getByLoginId(account.getLoginId());
      name = userProfile.getNickname();
      gender = userProfile.getGender();
      if(StringUtil.isBlank(name)) name = userProfile.getFullName();
      if(StringUtil.isBlank(name)) name = lolo.getResponsibleFullName();
      email = communicationAccount.getEmail();
      List<TargetRecipient> targetRecipients = new ArrayList<>();
      TargetRecipient recipient = new TargetRecipient(MessageDeliverType.Email, communicationAccount.getEmail(), communicationAccount.getEmail());
      targetRecipients.add(recipient);
      message.setRecipients(targetRecipients);
    }
    String content = "Dear ";
    if("male".equalsIgnoreCase(gender)) {
      content += "Mr ";
    } else if("female".equalsIgnoreCase(gender)) {
      content += "Ms ";
    }
    content += name;
    if(StringUtil.isNotBlank(email)) {
      content += " (" + email +")";
    }
    String vendorName = lolo.getVendorName() != null ? lolo.getVendorName() : "";
    content += ",<br/>Đề nghị";
    if("male".equalsIgnoreCase(gender)) {
      content += " anh";
    } else if("female".equalsIgnoreCase(gender)) {
      content += " chị";
    }
    content += " nhập thanh toán chi phí hóa đơn nâng hạ theo bảng kê đính kèm trên hệ thống TMS(Mã code: <b>" + lolo.getCode() +"</b>), vận tải: " + vendorName;
    String subject = lolo.getCode() + " : Thanh toán bảng kê ngày: " + DateUtil.asCompactDate(lolo.getPaymentRequestDate()) + " / thầu phụ: " + lolo.getVendorName();
    message.setSubject(subject);
    message.setContent(content);
    message.setSenderLoginId(client.getRemoteUser());
    return message;
  }

  public boolean updateInvoiceReconcileMessageId(ClientInfo clientInfo, Company company, Long loloId, Long messageId) {
    repo.updateLOLOMessageId(company.getId(), loloId, messageId);
    return true;
  }

  
}