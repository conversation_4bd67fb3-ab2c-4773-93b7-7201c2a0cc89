package net.datatp.module.company.tmpl.repository;

import java.io.Serializable;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import net.datatp.module.company.tmpl.entity.TmplBlock;

@Repository
public interface TmplBlockRepository extends JpaRepository<TmplBlock, Serializable> {
  @Query("SELECT h from TmplBlock h WHERE h.companyId = 0 AND h.path = :path")
  public TmplBlock getByPath(@Param("path") String path);
  
  @Query("SELECT h from TmplBlock h WHERE h.companyId = :companyId AND h.id = :id")
  public TmplBlock getById(@Param("companyId") Long companyId, @Param("id") Long id);
  
  @Query("SELECT h from TmplBlock h WHERE h.companyId = :companyId AND h.path = :path")
  public TmplBlock getByPath(@Param("companyId") Long companyId, @Param("path") String path);
}