package net.datatp.module.company.tmpl;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import jakarta.servlet.http.HttpServletRequest;

import net.datatp.module.company.tmpl.entity.TmplBlock;
import net.datatp.module.data.db.query.SqlQueryParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.github.jknack.handlebars.Template;

import net.datatp.module.app.AppEnv;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.company.CompanyLogic;
import net.datatp.module.company.entity.Company;
import net.datatp.module.core.print.PDFPrintable;
import net.datatp.module.core.template.HBSTemplateCache;
import net.datatp.module.core.template.HBSTemplateEngine;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.util.error.ErrorType;

import org.springframework.transaction.annotation.Transactional;

import net.datatp.util.error.RuntimeError;
import net.datatp.util.text.StringUtil;

@Service("TmplService")
public class TmplService {
  @Autowired
  private AppEnv appEnv;
  
  @Autowired
  private CompanyLogic    companyLogic;
  
  @Autowired
  private TmplBlockLogic tmplBlockLogic ;
  
  private Map<String, HBSTemplateEngine> engineMap = new HashMap<>();
  
  private HBSTemplateCache templateCache = new HBSTemplateCache();
  
  public Company getCompany(HttpServletRequest req) {
    Company company = companyLogic.getCompany(ClientInfo.DEFAULT, "test");
    return company;
  }
  
  public Template compile(ICompany company, String hbsLocation) {
    HBSTemplateEngine engine = engineMap.get(company.getCode()) ;
    if(engine == null) {
      HBSDBTemplateLoader tmplLoader = new HBSDBTemplateLoader(company, appEnv, tmplBlockLogic);
      engine = new HBSTemplateEngine(tmplLoader).with(templateCache);
      engineMap.put(company.getCode(), engine) ;
    }
    return engine.compile(hbsLocation);
  }

  public String render(Company company, String hbsLocation, Object model) {
    Template template = compile(company, hbsLocation);
    try {
      return template.apply(model);
    } catch (IOException e) {
      throw RuntimeError.UnknownError("Cannot render template {0}", hbsLocation);
    }
  }

  @Transactional(readOnly = true)
  public List<TmplBlock> searchTmplBlock(ClientInfo clientInfo, Company company, SqlQueryParams params) {
    List<TmplBlock> tmplBlockList = tmplBlockLogic.searchTmplBlock(clientInfo, company, params);
    return tmplBlockList;
  }

  @Transactional
  public Boolean deleteTmplBlockById(ClientInfo clientInfo, Company company, List<Long> ids) {
    return tmplBlockLogic.deleteTmplBlockById(clientInfo, company, ids);
  }

  @Transactional(readOnly = true)
  public TmplBlock getTmplBlockById(ClientInfo clientInfo, Company company, Long id) {
    return tmplBlockLogic.getTmplBlockById(clientInfo, company, id);
  }

  @Transactional
  public TmplBlock saveTmplBlock(ClientInfo clientInfo, Company company, TmplBlock tmplBlock) {
    return tmplBlockLogic.saveTmplBlock(clientInfo, company, tmplBlock);
  }

  public byte[] render(Company company, String hbsLocation, String type, Object model) {
    Template template = compile(company, hbsLocation);
    if("pdf".equals(type)) return getPDFAsBytes(template, model);
    return getTextAsBytes(template, model);
  }

  private byte[] getTextAsBytes(Template template, Object scopes) {
    return apply(template, scopes).getBytes(StringUtil.UTF8);
  }

  private byte[] getPDFAsBytes(Template template, Object context) {
    PDFPrintable printable = new PDFPrintable("print/fonts");
    return printable.createPDF(apply(template, context));
  }

  private String apply(Template template, Object context) {
    try {
      return template.apply(context);
    } catch (IOException e) {
      throw new RuntimeError(ErrorType.Unknown, e);
    }
  }
}