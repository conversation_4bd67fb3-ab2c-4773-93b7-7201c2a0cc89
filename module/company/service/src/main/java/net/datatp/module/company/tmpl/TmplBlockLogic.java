package net.datatp.module.company.tmpl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import net.datatp.module.common.ClientInfo;
import net.datatp.module.company.entity.Company;
import net.datatp.module.company.tmpl.entity.TmplBlock;
import net.datatp.module.company.tmpl.repository.TmplBlockRepository;
import net.datatp.module.data.db.DAOService;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.ClauseFilter;
import net.datatp.module.data.db.query.EntityTable;
import net.datatp.module.data.db.query.OptionFilter;
import net.datatp.module.data.db.query.RangeFilter;
import net.datatp.module.data.db.query.SearchFilter;
import net.datatp.module.data.db.query.SqlQuery;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.util.ds.Collections;

@Component
public class TmplBlockLogic extends DAOService {
  @Autowired
  private TmplBlockRepository repo;
  
  public TmplBlock getTmplBlock(String name) {
    return repo.getByPath(0l, name);
  }
  
  public TmplBlock getTmplBlock(ICompany company, String name) {
    return repo.getByPath(company.getId(), name);
  }
  
  public TmplBlock saveTmplBlock(ClientInfo client, TmplBlock block) {
    block.set(client);
    block = repo.save(block);
    return block;
  }
  
  public TmplBlock getTmplBlock(ClientInfo client, Company company, String name) {
    return repo.getByPath(company.getId(), name);
  }
  
  public TmplBlock saveTmplBlock(ClientInfo client, Company company, TmplBlock block) {
    block.set(client, company.getId());
    block = repo.save(block);
    return block;
  }

  public List<TmplBlock> searchTmplBlock(ClientInfo client, Company company, SqlQueryParams params) {
    params.addParam("companyId", company.getId());
    SqlQuery query =
      new SqlQuery()
        .ADD_TABLE(new EntityTable(TmplBlock.class)
          .selectAllFields()
          .rmSelectField("content"))
          .FILTER(new ClauseFilter(TmplBlock.class, "companyId", "=", ":companyId"))
          .FILTER(
          SearchFilter.isearch(TmplBlock.class, "path"))
          .FILTER(
          OptionFilter.storageState(TmplBlock.class),
          RangeFilter.modifiedTime(TmplBlock.class))
          .ORDERBY(new String[] {"path", "modifiedTime"}, "modifiedTime", "DESC");
    return query(client, query, params, TmplBlock.class);
  }

  public boolean deleteTmplBlockById (ClientInfo clientInfo, Company company, List<Long> ids) {
    if(Collections.isNotEmpty(ids)) {
      repo.deleteAllById(ids);
    }
    return true;
  }

  public TmplBlock getTmplBlockById (ClientInfo clientInfo, Company company, Long id) {
    return repo.getById(company.getId(), id);
  }
}
