.ui-calendar-manager {
  border: 1px solid lightgray;

  .ui-calendar-toolbar {
    margin: 1px 0px;
    padding: 1px;
    border: 1px solid lightgray;

    .btn,
    .form-control,
    .dropdown-toggle {
      padding: 0.25em 0.5em;
    }
  }

  .ui-month-calendar {
    padding: 0.15em;
    background-color: var(--phoenix-emphasis-bg);

    .cell {
      flex: 1;
      min-width: 100px;
      min-height: 50px;
      border: 1px solid var(--phoenix-calendar-border-color);
      font-size: 0.9em;
      cursor: pointer;
      background-color: var(--phoenix-calendar-bg);

      &:hover {
        background-color: var(--phoenix-body-highlight-bg);
      }

      .date-label {
        width: 1.875rem;
        height: 1.875rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: transparent;
        color: var(--phoenix-tertiary-color);
        transition: background-color 200ms ease-in-out;
      }

      &.cell-selected {
        .date-label {
          background-color: var(--phoenix-primary);
          color: #fff;
        }
      }

      &.cell-preview {
        background-color: var(--phoenix-body-highlight-bg);
        opacity: 0.7;

        .date-label {
          opacity: 1;
        }
      }

      .cell-content {
        padding: 2px;
      }
    }

    .cell-header {
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      background-color: var(--phoenix-body-highlight-bg);
      min-height: 30px;
      color: var(--phoenix-tertiary-color);
      border: 1px solid var(--phoenix-calendar-border-color);
    }
  }

  .ui-week-calendar {
    padding: 2px;

    .cell {
      display: flex;
      flex-direction: column;
      margin: 1px 0px;
      padding: 2px;
      width: 12.5%;
      max-width: 12.5%;
      min-height: 50px;
      border: 1px solid #d9d9d9;
    }

    .cell-header {
      max-height: 40px;
      min-height: 40px;
      background-color: lightgray;
      align-items: center;
      justify-content: center;
      overflow: hidden;
    }

    .cell-time {
      min-width: 60px;
      max-width: 60px;
      align-items: center;
      justify-content: center;
      background-color: var(--phoenix-body-highlight-bg, #f8f9fa);
    }

    .cell-day-column {
      display: flex;
      flex-direction: column;
      flex: 1;
      border: 1px solid #d9d9d9;
      margin: 0 1px;

      .cell {
        width: 100%;
        max-width: 100%;
        height: 100%;
        margin: 0;
        border: none;
        overflow-y: auto;
      }
    }

    .cell-selected {
      background-color: var(--phoenix-body-highlight-bg, lightgray);
    }

    .cell-highlight {
      border: 2px solid var(--phoenix-primary, #0d6efd);
      background-color: var(--phoenix-primary-bg-subtle, rgba(13, 110, 253, 0.1));
    }
  }

  .ui-day-calendar {
    .highlight {
      border: 2px dashed slateblue;
    }

    .cell {
      display: flex;
      flex-direction: column;
      margin: 1px;
      padding: 2px;
      min-height: 50px;
      border: 1px solid #d9d9d9;
    }

    .cell-time {
      width: 70px;
      align-items: center;
      justify-content: center;
    }

    .cell-content {
      flex-grow: 1;
    }
  }
}

.ui-mini-month-calendar {
  margin: 10px;

  .control {
    padding: 2px;

    .dropdown-toggle {
      background-color: transparent;
      color: black;
      border: none;
      outline: none;
    }
  }

  .cell {
    display: flex;
    justify-content: center;
    width: 13% !important;
    padding: 5px 2px;
    min-width: 35px;
    margin: 1px;
    color: black;
    background-color: transparent;
    border: 1px solid lightgray;
    border-radius: 5px;
    font-size: 0.9em;

    .btn-link {
      padding: 0px;
      margin: 0px;
    }
  }

  .cell-header {
    color: blue;
  }

  .cell-selected {
    background: lightgray;
  }

  .cell-preview {
    color: gray;
  }
}