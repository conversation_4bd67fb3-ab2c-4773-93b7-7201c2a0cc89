import React, { CSSProperties } from "react"
import moment from 'moment';

import { Button, AvailableSize, BaseComponentProps, GreedyScrollable } from 'wbootstrap/core';

import { CalendarContext, CalendarConfig, HourCellMode } from "./ICalendar";
interface CellProps {
  context: CalendarContext;
  config: CalendarConfig;
  date: Date;
  selected?: boolean
  style?: CSSProperties;
  highlight?: boolean;
}
class Cell extends React.Component<CellProps> {
  render() {
    let { context, config, style, date, selected, highlight } = this.props
    let className = "cell";
    if (selected) className += ' cell-selected';
    if (highlight) className += ' cell-highlight';
    let content = null;
    if (config.week && config.week.renderCell) {
      content = config.week.renderCell(context, config, date);
    }
    return (<div className={className} style={style}>{content}</div>)
  }
}

function DayHeaders(uiCal: UIWeekCalendar, width: number,) {
  let { date } = uiCal.props
  let cells = [];
  cells.push(<div className='cell cell-header cell-time' style={{ minWidth: 60 }}></div>);
  for (let i = 0; i < 7; i++) {
    let dateInWeek = new Date(date);
    dateInWeek.setDate(date.getDate() + (i - date.getDay()));
    let mDate = moment(dateInWeek);
    let label = mDate.format('ll');
    cells.push(
      <div className='cell cell-header'>
        <Button laf='link' onClick={() => uiCal.onSelectDay(dateInWeek)}>{label}</Button>
      </div>
    );
  }
  return (
    <div className="flex-vbox-grow-0" style={{ width: width }}>
      <div className="flex-hbox justify-content-between">{cells}</div>
    </div>
  );
}

function DayColumns(uiCal: UIWeekCalendar, width: number, height: number) {
  let { context, config, date } = uiCal.props

  let columns = [];

  // Add time column header (empty for alignment)
  columns.push(
    <div className='cell cell-time' style={{ minWidth: 60, height: height }}>
      {/* Empty time column for day view */}
    </div>
  );

  // Create 7 day columns
  for (let j = 0; j < 7; j++) {
    let dayInWeek = new Date(date);
    dayInWeek.setDate(date.getDate() + (j - date.getDay()));

    let selected = j == date.getDay();
    let highlight = false;

    // Check if this day is today
    let today = new Date();
    if (dayInWeek.toDateString() === today.toDateString()) {
      highlight = true;
    }

    columns.push(
      <div className='cell cell-day-column' style={{ height: height, flex: 1 }}>
        <Cell
          context={context}
          config={config}
          selected={selected}
          date={dayInWeek}
          highlight={highlight}
          style={{ height: '100%', display: 'flex', flexDirection: 'column' }}
        />
      </div>
    );
  }

  return (
    <div className="flex-hbox justify-content-between" style={{ width: width, height: height }}>
      {columns}
    </div>
  );
}

interface UIWeekCalendarProps extends BaseComponentProps {
  context: CalendarContext;
  config: CalendarConfig, date: Date
}
export class UIWeekCalendar extends AvailableSize<UIWeekCalendarProps> {
  onSelectDay(date: Date) {
    let { context, config } = this.props;
    config.selectedDate = date
    context.getCalendarManager().forceUpdateView(true);
  }

  onChangeHourCellMode = (cellMode: HourCellMode) => {
    let { config } = this.props;
    config.cellMode = cellMode;
    this.forceUpdate();
  }

  renderContent(width: number, height: number) {
    if (!width) width = 60
    return (
      <div className="ui-week-calendar flex-vbox" style={{ width: width, height: height }}>
        {DayHeaders(this, width)}
        {DayColumns(this, width, height - 40)}
      </div>
    );
  }
}