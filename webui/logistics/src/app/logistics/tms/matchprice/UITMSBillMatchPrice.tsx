import React from 'react';
import * as FeatherIcon from 'react-feather'
import { util, app, bs, input, entity } from "@datatp-ui/lib";

import { T } from '../backend';

import { UITMSBillMatchPriceList } from './UITMSBillMatchPriceList';
import { TMSBillTransportationModeTools } from '../utils';
import { TransportationMode } from 'app/logistics/forwarder';
// import { PricePlugin } from 'app/logistics/forwarder/price/common';

export type TMSBillMatchPrice = {
  customerId: number;
  customerFullName: string;
  priceField: string;
  fromLocationId: number;
  fromLocationAddress: string;
  fromProvinceId: number;
  fromProvinceAddress: string;
  toLocationId: number;
  toLocationAddress: string;
  toProvinceId: number;
  toProvinceAddress: string;
}

interface UITMSBillMatchPriceFormProps extends app.AppComponentProps {
  bill: any;
  onSelectPrice: (params: any, price: any) => void;
}
export class UITMSBillMatchPriceForm extends app.AppComponent<UITMSBillMatchPriceFormProps> {
  matchPriceObserver: entity.BeanObserver;
  pricePlugin: any //PricePlugin;

  regularPrices = [
    'truck1Ton25Price', 'truck1Ton5Price', 'truck2Ton5Price', 'truck3Ton5Price', 'truck5TonPrice',
    'truck7TonPrice', 'truck8TonPrice', 'truck10TonPrice', 'truck15TonPrice',
  ];
  regularPriceLabels = ['1.25T', '1.5T', '2.5T', '3.5T', '5T', '7T', '8T', '10T', '15T',];
  containerPrices = [
    "contDry20Lt17TonPrice", "contDry20Geq17TonPrice",
    "contHighCube20Lt17TonPrice", "contHighCube20Geq17TonPrice",
    "contReefer20Lt17TonPrice", "contReefer20Geq17TonPrice",
    "contOpenTop20Lt17TonPrice", "contOpenTop20Geq17TonPrice",
    "contFlatRack20Lt17TonPrice", "contFlatRack20Geq17TonPrice",
    "contTank20Lt17TonPrice", "contTank20Geq17TonPrice",
    "contDry40Lt17TonPrice", "contDry40Geq17TonPrice",
    "contHighCube40Lt17TonPrice", "contHighCube40Geq17TonPrice",
    "contReefer40Lt17TonPrice", "contReefer40Geq17TonPrice",
    "contOpenTop40Lt17TonPrice", "contOpenTop40Geq17TonPrice",
    "contFlatRack40Lt17TonPrice", "contFlatRack40Geq17TonPrice",
    "contTank40Lt17TonPrice", "contTank40Geq17TonPrice"
  ];
  containerPriceLabels = [
    "20DC < 17T", "20DC >= 17T",
    "20HC < 17T", "20HC >= 17T",
    "20RF < 17T", "20RF >= 17T",
    "20OT < 17T", "20OT >= 17T",
    "20FR < 17T", "20FR >= 17T",
    "20Tank < 17T", "20Tank >= 17T",
    "40DC < 17T", "40DC >= 17T",
    "40HC < 17T", "40HC >= 17T",
    "40RF < 17T", "40RF >= 17T",
    "40OT < 17T", "40OT >= 17T",
    "40FR < 17T", "40FR >= 17T",
    "40Tank < 17T", "40Tank >= 17T"
  ];
  constructor(props: UITMSBillMatchPriceFormProps) {
    super(props);
    let { bill } = this.props;

    let model = {
      customerPartnerId: bill.customerPartnerId,
      customerFullName: bill.customerFullName,
      mode: 'LCL',
      weight: bill.weight,
      priceField: '',
      truckType: bill.truckType,
      fromAddress: bill.senderAddress,
      fromLocationCode: bill.senderLocationCode,
      fromLocationAddress: bill.senderLocationAddress,
      fromProvinceId: bill.senderStateId,
      fromProvinceAddress: bill.senderStateLabel,
      toAddress: bill.receiverAddress,
      toLocationCode: bill.receiverLocationCode,
      toLocationAddress: bill.receiverLocationAddress,
      toProvinceId: bill.receiverStateId,
      toProvinceAddress: bill.receiverStateLabel,
    }
    if (TMSBillTransportationModeTools.isFCL(bill.mode)) {
      model.mode = 'FCL';
      model.priceField = getFieldPrice('FCL', bill.truckType, bill.weight) || this.containerPrices[0];
    } else {
      model.priceField = getFieldPrice('LCL', bill.truckType, bill.weight) || this.regularPrices[0];
    }
    this.matchPriceObserver = new entity.BeanObserver(model);
    this.onSetPricePlugin();
  }

  onSetPricePlugin = () => {
    let model = this.matchPriceObserver.getMutableBean();
    // this.pricePlugin = new PricePlugin(TransportationMode.TRUCK_CONTAINER)
    // if (model.mode === 'FCL') {
    //   this.pricePlugin = new TruckContainerTransportChargeListPlugin()
    //     .withTargetReference('Partner', model.customerPartnerId, model.customerFullName)
    // } else {
    // }
  }

  onMatchPrice = () => {
    this.onSetPricePlugin();
    this.forceUpdateUI(true);
  }

  render() {
    let { appContext, pageContext, onSelectPrice } = this.props;
    const matchPrice = this.matchPriceObserver.getMutableBean();
    return (
      <bs.VSplit>
        <bs.VSplitPane width={400}>
          <div className={'flex-vbox'}>
            <bs.GreedyScrollable className='flex-vbox p-1'>
              <div className='bb-field'>
                <bs.FormLabel>Customer</bs.FormLabel>
                {/* <BBRefTMSPartner type='Customer' minWidth={400}
                  appContext={appContext} pageContext={pageContext} placeholder={'Customer'}
                  bean={matchPrice} beanIdField={'customerId'} beanLabelField={'customerFullName'}
                /> */}
              </div>
              <input.BBStringField label='Truck Type' field={'truckType'} bean={matchPrice} disable />
              <input.BBRadioInputField label='Mode' field='mode' bean={matchPrice}
                options={['FCL', 'LCL']} onInputChange={(bean, field, oldVal, newVal) => {
                  matchPrice.priceField = newVal === 'FCL' ? this.containerPrices[0] : this.regularPrices[0];
                  this.forceUpdate()
                }} />

              <div className='bb-field'>
                <bs.FormLabel>Pickup Location</bs.FormLabel>
                {/* <entity.BBModelAutoComplete
                  appContext={appContext} pageContext={pageContext} allowUserInput
                  query={FleetEntityQuery.location} bean={matchPrice} field={'fromLocationCode'} labelField={'fromLocationAddress'}
                  onPostSelect={(option: any, val: any) => {
                    matchPrice.fromLocationId = val ? val.id : null;
                    matchPrice.fromLocationCode = val ? val.code : null;
                    matchPrice.fromLocationAddress = val ? val.address : null;
                    matchPrice.fromProvinceId = val ? val.stateId : null;
                    matchPrice.fromProvinceAddress = val ? val.stateLabel : null;
                    this.forceUpdate();
                  }} /> */}
              </div>
              <input.BBTextField label='Pickup Address' field='fromAddress' bean={matchPrice}
                style={{ height: '5em' }} />

              <div className='bb-field'>
                <bs.FormLabel>Delivery Location</bs.FormLabel>
                {/* <entity.BBModelAutoComplete
                  appContext={appContext} pageContext={pageContext} allowUserInput
                  query={FleetEntityQuery.location} bean={matchPrice} field={'toLocationCode'} labelField={'toLocationAddress'}
                  onPostSelect={(option: any, val: any) => {
                    matchPrice.toLocationId = val ? val.id : null;
                    matchPrice.toLocationCode = val ? val.code : null;
                    matchPrice.toLocationAddress = val ? val.address : null;
                    matchPrice.toProvinceId = val ? val.stateId : null;
                    matchPrice.toProvinceAddress = val ? val.stateLabel : null;
                    this.forceUpdate();
                  }} /> */}
              </div>
              <input.BBTextField label='Delivery Address' field='toAddress' bean={matchPrice}
                style={{ height: '5em' }} />

              <input.BBSelectField label='Price' field='priceField' bean={matchPrice}
                options={matchPrice.mode === 'FCL' ? this.containerPrices : this.regularPrices}
                optionLabels={matchPrice.mode === 'FCL' ? this.containerPriceLabels : this.regularPriceLabels} />
            </bs.GreedyScrollable>

            <bs.Toolbar>
              <entity.WButtonEntityWrite
                appContext={appContext} pageContext={pageContext} color='secondary'
                label={T('Find Match Price')} icon={FeatherIcon.Book} onClick={this.onMatchPrice} />
            </bs.Toolbar>
          </div>
        </bs.VSplitPane>
        <bs.VSplitPane>
          <UITMSBillMatchPriceList key={this.componentId}
            appContext={appContext} pageContext={pageContext} type={'selector'}
            params={matchPrice} onSelect={(_appContext, _pageCtx, price) => onSelectPrice(matchPrice, price)}
            plugin={this.pricePlugin} />
        </bs.VSplitPane>
      </bs.VSplit>
    );
  }
}

const getFieldPrice = (mode: 'FCL' | 'LCL', truckType: any, weight?: number) => {
  let mapFieldPrice = new Map([
    ['1.25T', 'truck1Ton25Price'],
    ['1.5T', 'truck1Ton5Price'],
    ['2.5T', 'truck2Ton5Price'],
    ['3.5T', 'truck3Ton5Price'],
    ['5T', 'truck5TonPrice'],
    ['7T', 'truck7TonPrice'],
    ['8T', 'truck8TonPrice'],
    ['10T', 'truck10TonPrice'],
    ['15T', 'truck15TonPrice'],
    ['20DC', 'contDry20'],
    ['20ISOTANK', 'contTank20'],
    ['20OT', 'contOpenTop20'],
    ['20RF', 'contReefer20'],
    ['40DC', 'contDry40'],
    ['40FR', 'contFlatRack40'],
    ['40HC', 'contHighCube40'],
    ['40OT', 'contOpenTop40'],
    ['40RF', 'contReefer40'],
  ]);
  let fieldPrice = mapFieldPrice.get(truckType);
  if (mode === 'FCL') {
    if (weight && weight >= 17000) fieldPrice = fieldPrice + "Geq17TonPrice";
    else fieldPrice = fieldPrice + "Lt17TonPrice";
  }
  return fieldPrice;
}