import { entity } from "@datatp-ui/lib";
import { T } from "../../backend";

interface BBRefTMSContainerTypeProps extends entity.BBRefEntityProps {
  beanIdField?: string;
  beanLabelField: string;
}
export class BBRefTMSContainerType extends entity.BBRefEntity<BBRefTMSContainerTypeProps> {
  createPlugin() {
    let { beanIdField, beanLabelField } = this.props;
    let config: entity.BBRefEntityPluginConfig = {
      backend: {
        context: 'company',
        service: 'TMSRoundUsedService',
        searchMethod: 'searchTMSContainerTypes',
        loadMethod: beanIdField ? 'getTMSContainerType' : 'getTMSContainerTypeByType',
        createLoadParams(idValue) {
          let params = { 'id': idValue };
          return params;
        },
      },
      bean: {
        idField: beanIdField ? beanIdField : beanLabelField,
        labelField: beanLabelField,
      },
      refEntity: {
        idField: 'id',
        labelField: 'type',
        labelFunc: (opt: any) => {
          return opt['type'];
        },

        vgridRecordConfig: {
          fields: [
            entity.DbEntityListConfigTool.FIELD_INDEX(),
            entity.DbEntityListConfigTool.FIELD_ON_SELECT('type', T('Type'), 120),
            { name: 'description', label: T('Description'), width: 150 },
          ]
        }
      },
    }
    return new entity.BBRefEntityPlugin(config);
  }
}