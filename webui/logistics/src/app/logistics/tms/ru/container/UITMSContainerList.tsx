import React, { Component } from 'react';
import * as FeatherIcon from 'react-feather'
import { server, util, bs, grid, input, sql, app, entity } from '@datatp-ui/lib';
import { module } from "@datatp-ui/erp";

import { T } from '../../backend';
import { BBRefTMSCarrier } from '../../partner/BBRefTMSCarrier';
import { BBRefTMSContainerType } from './BBRefTMSContainerType';

const { CONTAINER_NUMBER_VALIDATOR } = util.validator;

export class UITMSContainerListPageControl extends Component<grid.VGridContextProps> {
  saving = false;

  onSave = () => {
    let { context } = this.props;
    let uiRoot = context.uiRoot as entity.DbEntityList;
    let { appContext, plugin } = uiRoot.props;
    let modifiedRecords = context.model.getMarkModifiedRecords();
    let deleteRecords = context.model.getMarkDeletedRecords();
    let records = [...modifiedRecords, ...deleteRecords];
    if (records.length === 0) {
      let selectedRecords = plugin.getListModel().getSelectedRecords();
      selectedRecords.forEach(sel => {
        let state = grid.getRecordState(sel);
        sel['_state'] = new grid.RecordState(state.row);
      })
      appContext.addOSNotification("success", T("No Rows is Edited"));
      uiRoot.forceUpdate();
    } else {
      context.getVGrid().forceUpdateView();
      appContext.createHttpBackendCall('TMSRoundUsedService', 'saveTMSContainers', { records })
        .withSuccessData((_data: any) => {
          appContext.addOSNotification("success", T("Save Container Success"));
          uiRoot.reloadData();
          context.getVGrid().forceUpdateView();
        })
        .withFail((response: server.BackendResponse) => {
          server.rest.handleResponseError('', response);
          this.saving = false;
          this.forceUpdate();
        })
        .call();

      this.saving = true;
      this.forceUpdate();
    }
  }

  render() {
    let { context } = this.props;
    let uiRoot = context.uiRoot as app.AppComponent;
    let { pageContext } = uiRoot.props;
    let writeCap = pageContext.hasUserWriteCapability();

    return (
      <bs.Toolbar className='border' hide={!pageContext.hasUserReadCapability()}>
        {writeCap ?
          <bs.Button laf='primary' disabled={this.saving || !writeCap} onClick={this.onSave}>
            {this.saving ?
              <FeatherIcon.Loader size={12} style={{ animation: '0.75s linear infinite spinner-border' }} />
              :
              <FeatherIcon.Save size={12} />
            }
            <span style={{ marginLeft: 4 }}>{T('Save Changes')}</span>
          </bs.Button>
          :
          null
        }
      </bs.Toolbar>
    )
  }
}

export class UITMSContainerListPlugin extends entity.DbEntityListPlugin {
  constructor() {
    super([]);

    this.backend = {
      context: 'company',
      service: 'TMSRoundUsedService',
      searchMethod: 'searchTMSContainers',
    }

    this.searchParams = {
      "params": {
        purpose: null,
        type: null,
        ids: null,
      },
      "filters": [
        ...sql.createSearchFilter(),
      ],
      "optionFilters": [
        sql.createStorageStateFilter([entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED])
      ],
      maxReturn: 5000,
    }
  }

  loadData(uiList: entity.DbEntityList<any>) {
    let params = { 'params': this.searchParams }
    this.createBackendSearch(uiList, params).call();
  }

  withMaxReturn(maxReturn: number) {
    if (this.searchParams) this.searchParams.maxReturn = maxReturn;
    return this;
  }


  withIds(ids: Array<any>) {
    this.addSearchParam("ids", ids);
    return this;
  }
}

export class UITMSContainerList extends entity.DbEntityList {
  createVGridConfig() {
    let { appContext, pageContext, type } = this.props;
    const writeCap = pageContext.hasUserWriteCapability();

    let uiRoot = this;
    let onInputChange = (fieldCtx: grid.FieldContext, oldVal: any, newVal: any) => {
      let dRecord = fieldCtx.displayRecord;
      let field = fieldCtx.fieldConfig;
      let ctx = fieldCtx.gridContext;
      let event: grid.VGridCellEvent = {
        row: dRecord.row, field: field, event: 'Modified', data: dRecord
      }
      ctx.broadcastCellEvent(event);
    };

    let configId = `tms-container-list`;
    let config: grid.VGridConfig = {
      id: configId,
      title: `TMS Container`,
      record: {
        editor: {
          supportViewMode: ['aggregation', 'table'],
          enable: true
        },
        computeDataRowHeight: (_ctx: grid.VGridContext, dRec: grid.DisplayRecord) => {
          if (dRec.isDataRecord()) return 35;
          return 20;
        },
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          {
            name: 'containerNo', label: T('Container No'), width: 150,
            customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let containerNo = record[field.name];
              let width = field.width ? field.width : 120;
              let cssClass = field.cssClass;
              if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(ctx, dRecord)}`;
              return (
                <div className={`flex-hbox`}>
                  <bs.Tooltip className={`flex-grow-1 text-truncate ${cssClass}`} style={{ width: width - 25 }} tooltip={containerNo}>
                    {containerNo}
                  </bs.Tooltip>
                  {uiRoot.containerValidate(containerNo)}
                </div>
              )
            },
            editor: {
              type: 'string', onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let bill = dRecord.record;
                let containerNo = bill[field.name];
                let cssClass = field.cssClass;
                if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiRoot.getVGridContext(), dRecord)}`;
                return (
                  <div className={`flex-hbox`}>
                    <input.BBStringField className={cssClass}
                      bean={bill} field={field.name} tabIndex={tabIndex} focus={focus} onInputChange={onInputChange} />
                    {uiRoot.containerValidate(containerNo)}
                  </div>
                )
              },
            },
            onClick: (ctx: grid.VGridContext, record: grid.DisplayRecord) => {
              uiRoot.onSelect(record);
            },
          },
          {
            name: 'ownerLabel', label: T('Owner'), width: 150,
            editor: {
              type: 'string',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let bill = dRecord.record;
                let cssClass = field.cssClass;
                if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiRoot.getVGridContext(), dRecord)}`;
                return (
                  <BBRefTMSCarrier minWidth={400} disable={!writeCap}
                    className={cssClass} appContext={appContext} pageContext={pageContext} tabIndex={tabIndex} autofocus={focus}
                    bean={bill} beanIdField={'ownerId'} beanLabelField={'ownerLabel'} placeholder={'Carrier'}
                    onPostUpdate={(_inputUI: React.Component, bean: any, _selectOpt: any, _userInput: string) => {
                      onInputChange(bean, field.name, null, bean[field.name])
                    }}
                  />
                )
              },
            }
          },
          {
            name: 'type', label: T('Type'), width: 150,
            editor: {
              type: 'string',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let roundUsed = dRecord.record;
                let cssClass = field.cssClass;
                if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiRoot.getVGridContext(), dRecord)}`;
                return (
                  <BBRefTMSContainerType minWidth={400}
                    className={cssClass} appContext={appContext} pageContext={pageContext} tabIndex={tabIndex} autofocus={focus}
                    bean={roundUsed} beanIdField={'containerTypeId'} beanLabelField={'type'} placeholder={'Container Type'}
                    onPostUpdate={(_inputUI: React.Component, bean: any, selectOpt: any, _userInput: string) => {
                      onInputChange(bean, '', null, selectOpt['type']);
                    }}
                  />
                )
              },
            }
          },
        ]
      },
      toolbar: {
        actions: [
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_ADD(!writeCap || type !== 'page'),
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_DELETE(!writeCap, T('Del')),
          module.settings.createVGridFavButton(configId),
        ],
        filters: entity.DbEntityListConfigTool.TOOLBAR_FILTERS(true, 'filter'),
        filterActions: [
          ...entity.DbEntityListConfigTool.TOOLBAR_AUTO_REFRESH('auto-refresh-container', T('Refresh')),
        ],
      },
      footer: {
        page: {
          hide: type !== 'page',
          render: (ctx: grid.VGridContext) => {
            return <UITMSContainerListPageControl context={ctx} />
          }
        },
      },
      view: {
        currentViewName: 'aggregation',
        availables: {
          table: {
            viewMode: 'table',
          },
          aggregation: {
            viewMode: 'aggregation',
            treeWidth: 150,
            createAggregationModel(_ctx: grid.VGridContext) {
              let model = new grid.AggregationDisplayModel(T('All'), false);
              model.addAggregation(new grid.ValueAggregation(T("Owner"), "ownerLabel", true));
              model.addAggregation(new grid.ValueAggregation(T("Type"), "type", true));
              return model;
            },
          }
        }
      }
    }

    let fields = config.record.fields;
    let computeCssClassesModifyBean = (_ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
      if (dRecord.isDataRecord()) {
        let state = dRecord.getRecordState();
        if (state.isMarkModified()) return 'fw-bolder fst-italic';
      }
      return '';
    }
    for (let sel of fields) {
      if (sel.name.startsWith("_")) continue;
      if (sel.listener) {
        if (!sel.listener.onDataCellEvent) {
          sel.listener.onDataCellEvent = (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
            if (event.row == cell.getRow()) {
              cell.forceUpdate()
            }
          }
        }
      } else {
        sel.listener = {
          onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
            if (event.row == cell.getRow()) {
              cell.forceUpdate()
            }
          },
        }
      }

      if (sel.computeCssClasses) {
        let cssClass = sel.computeCssClasses;
        sel.computeCssClasses = (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
          return `${cssClass(ctx, dRecord)} ${computeCssClassesModifyBean(ctx, dRecord)}`;
        }
      } else {
        sel.computeCssClasses = (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
          return computeCssClassesModifyBean(ctx, dRecord);
        }
      }
    }
    return config;
  }

  containerValidate = (val: any) => {
    try {
      CONTAINER_NUMBER_VALIDATOR.validate(val);
      return null;
    } catch (error) {
      return (
        <div className='flex-grow-0' >
          <FeatherIcon.XCircle size={12} color='red' />
        </div>
      );
    }
  }

  onNewAction(): void {
    let { plugin } = this.props
    let newRecord = {};
    plugin.getRecords().unshift(newRecord);
    plugin.getListModel().filter();
    let state = grid.getRecordState(newRecord);
    state.markModified();
    this.getVGridContext().getVGrid().forceUpdateView();
  }
}