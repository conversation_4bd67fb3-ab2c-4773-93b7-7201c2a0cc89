import React, { Component } from 'react';
import * as FeatherIcon from 'react-feather';
import { grid, sql, bs, entity, input, util, server, app } from '@datatp-ui/lib';

import { T } from '../backend';
import { TMSVGridConfigTool } from '../utils';
import { UITMSBillList, UITMSBillListPlugin } from '../bill/UITMSBillList';
import { TMSHubStatus, TMSHubStatusTools } from './models';
import { BBRefTMSCarrier } from '../partner/BBRefTMSCarrier';
import { BBRefTMSContainerType } from './container/BBRefTMSContainerType';

const { CONTAINER_NUMBER_VALIDATOR } = util.validator;

export class UITMSHubListPageControl extends Component<grid.VGridContextProps> {
  saving = false;

  onCreateHubFromBill = () => {
    let { context } = this.props;
    let uiRoot = context.uiRoot as entity.DbEntityList;
    let { appContext, pageContext, plugin } = uiRoot.props;

    let onSelectBills = (_appCtx: app.AppContext, _pageCtx: app.PageContext, billModels: any[]) => {
      appContext.createHttpBackendCall('TMSRoundUsedService', 'createTMSHubByTMSBills', { billModels: billModels })
        .withSuccessData((_data: any) => {
          uiRoot.reloadData();
          appContext.addOSNotification("success", T("Create Success"));
          _pageCtx.back();
        })
        .call();
    }
    let onSelectBill = (_appCtx: app.AppContext, _pageCtx: app.PageContext, billModel: any) => {
      appContext.createHttpBackendCall('TMSRoundUsedService', 'createTMSHubByTMSBills', { billModels: [billModel] })
        .withSuccessData((_data: any) => {
          uiRoot.reloadData();
          appContext.addOSNotification("success", T("Create Success"));
          _pageCtx.back();
        })
        .call();
    }

    let createAppPage = (_appCtx: app.AppContext, _pageCtx: app.PageContext) => {
      let dateRange = new util.TimeRange();
      dateRange.fromSetDate(new Date());
      dateRange.toAdd(2, 'month');
      return (
        <div className='flex-vbox'>
          <UITMSBillList type={'selector'} readOnly
            appContext={_appCtx} pageContext={_pageCtx} plugin={new UITMSBillListPlugin(dateRange)}
            onMultiSelect={onSelectBills} onSelect={onSelectBill} />
        </div>
      )
    }
    pageContext.createPopupPage('bill-selector', T('TMS Bills'), createAppPage, { size: 'xl' })
  }

  updateTMSHubData = (updatedHubs: Array<any>) => {
    let { context } = this.props;
    let uiRoot = context.uiRoot as entity.DbEntityList;
    let { appContext } = uiRoot.props;
    let pluginImpl = new UITMSHubListPlugin();
    let ids: Array<any> = [];
    updatedHubs.forEach(sel => ids.push(sel.id));

    let searchParams = pluginImpl.searchParams;
    if (searchParams) {
      searchParams.params = {
        ...searchParams.params,
        ids: ids.length > 0 ? ids : null
      }
    }
    appContext.createHttpBackendCall('TMSRoundUsedService', 'searchTMSHubs', { params: searchParams })
      .withSuccessData((data: any) => {
        for (let hub of updatedHubs) {
          for (let result of data) {
            if (hub['id'] == result['id']) {
              for (const propertyName in result) {
                hub[propertyName] = result[propertyName];
              }
            }
          }
          let state = grid.getRecordState(hub);
          hub['_state'] = new grid.RecordState(state.row);
        }
        appContext.addOSNotification("success", T("Save Hub Success"));
        context.getVGrid().forceUpdateView();
        this.saving = false;
        this.forceUpdate();
      })
      .call();
  }

  onSaveEditedHubs = () => {
    let { context } = this.props;
    let uiRoot = context.uiRoot as entity.DbEntityList;
    let { appContext, plugin } = uiRoot.props;
    let modifiedRecords = context.model.getMarkModifiedRecords();
    let deleteRecords = context.model.getMarkDeletedRecords();
    let records = [...modifiedRecords, ...deleteRecords];
    if (records.length === 0) {
      let selectedRecords = plugin.getListModel().getSelectedRecords();
      selectedRecords.forEach(sel => {
        let state = grid.getRecordState(sel);
        sel['_state'] = new grid.RecordState(state.row);
      })
      appContext.addOSNotification("success", T("No Rows is Edited"));
      uiRoot.forceUpdate();
    } else {
      context.getVGrid().forceUpdateView();
      appContext.createHttpBackendCall('TMSRoundUsedService', 'saveTMSHubModels', { models: records })
        .withSuccessData((data: any) => {
          let results: Array<any> = data;
          for (let modifiedRecord of modifiedRecords) {
            for (let result of results) {
              if (modifiedRecord['uikey'] && modifiedRecord['uikey'] == result['uikey']) {
                modifiedRecord['id'] = result['id'];
              }
            }
          }
          for (let sel of deleteRecords) {
            plugin.getListModel().removeRecord(sel);
          }
          this.updateTMSHubData(modifiedRecords);
        })
        .withFail((response: server.BackendResponse) => {
          server.rest.handleResponseError('', response);
          this.saving = false;
          this.forceUpdate();
        })
        .call();
      this.saving = true;
      this.forceUpdate();
    }
  }

  render() {
    let { context } = this.props;
    let uiRoot = context.uiRoot as app.AppComponent;
    let { pageContext } = uiRoot.props;
    let writeCap = pageContext.hasUserWriteCapability();
    let readCap = pageContext.hasUserReadCapability();

    return (
      <bs.Toolbar className='border' hide={!readCap}>
        {writeCap ?
          <>
            <bs.Button laf='primary' disabled={this.saving || !writeCap} onClick={this.onCreateHubFromBill}>
              <FeatherIcon.List size={12} />
              <span style={{ marginLeft: 4 }}>{T('Collect From Bill')}</span>
            </bs.Button>
            <bs.Button laf='primary' disabled={this.saving || !writeCap} onClick={this.onSaveEditedHubs}>
              {this.saving ?
                <FeatherIcon.Loader size={12} style={{ animation: '0.75s linear infinite spinner-border' }} />
                :
                <FeatherIcon.Save size={12} />
              }
              <span style={{ marginLeft: 4 }}>{T('Save Changes')}</span>
            </bs.Button>
          </>
          :
          null
        }
      </bs.Toolbar>
    )
  }
}

export class UITMSHubListPlugin extends entity.DbEntityListPlugin {
  constructor() {
    super();
    this.backend = {
      context: 'company',
      service: 'TMSRoundUsedService',
      searchMethod: 'searchTMSHubs',
      deleteMethod: 'deleteHubs',
      changeStorageStateMethod: 'updateHubStorageState'
    }

    this.searchParams = {
      params: {
        ids: null
      },
      filters: [
        ...sql.createSearchFilter()
      ],
      optionFilters: [
        {
          name: 'status', label: 'Status', type: 'STRING', required: true, multiple: true,
          options: [TMSHubStatus.INVENTORY, TMSHubStatus.WAITING, TMSHubStatus.DONE],
          optionLabels: ['Inventory', 'Waiting', 'Done'],
          selectOptions: [TMSHubStatus.INVENTORY, TMSHubStatus.WAITING, TMSHubStatus.DONE],
        },
        sql.createStorageStateFilter([entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED])
      ],
      rangeFilters: [
        {
          name: "validDate", label: T("Ngày Hạ"), type: "DATE",
          required: true, fromValue: null, toValue: null
        },
        {
          name: "expiryDate", label: T("Hạn Vỏ"), type: "DATE",
          required: true, fromValue: null, toValue: null
        },
      ],
      maxReturn: 1000,
    }
  }

  loadData(uiList: entity.DbEntityList<any>) {
    let params = { 'params': this.searchParams }
    this.createBackendSearch(uiList, params).call();
  }

  withValidDate(day: number) {
    let dateNow = new Date();
    if (day) {
      dateNow.setDate(day);
    }
    let dateRange = new util.TimeRange();
    dateRange.fromSetDate(dateNow);
    dateRange.fromSubtract(1, 'month');
    if (this.searchParams && this.searchParams.rangeFilters) {
      let findDate = this.searchParams.rangeFilters.find(sel => sel.name === 'validDate');
      if (findDate) findDate.fromValue = dateRange.fromFormat()
    }
    return this;
  }
}

export class UITMSHubList extends entity.DbEntityList {
  createVGridConfig() {
    let { appContext, pageContext, type } = this.props;
    let thisUI = this;
    const writeCap = pageContext.hasUserWriteCapability();
    const modCap = pageContext.hasUserModeratorCapability();
    let onInputChange = (fieldCtx: grid.FieldContext, oldVal: any, newVal: any) => {
      let dRecord = fieldCtx.displayRecord;
      let field = fieldCtx.fieldConfig;
      let ctx = fieldCtx.gridContext;
      let record = dRecord.record;
      let event: grid.VGridCellEvent = {
        row: dRecord.row, field: field, event: 'Modified', data: dRecord
      }
      if (field.name === 'validDate' || field.name === 'demDes') {
        if (record['validDate']) {
          let timeRange: util.TimeRange = new util.TimeRange();
          let demDes = record['demDes']
          if (demDes < 1) {
            record['demDes'] = oldVal;
          } else {
            let validDate = util.TimeUtil.parseCompactDateTimeFormat(record['validDate']);
            timeRange.toSetDate(validDate);
            timeRange.toAdd(demDes - 1, 'days');
            record['expiryDate'] = util.TimeUtil.toCompactDateTimeFormat(timeRange.toAsDate());
          }
        }
      }
      if (field.name === 'expiryDate') {
        if (record['validDate'] && record['expiryDate']) {
          let validDate = util.TimeUtil.parseCompactDateTimeFormat(record['validDate']);
          let expiryDate = util.TimeUtil.parseCompactDateTimeFormat(record['expiryDate']);
          let timeRange: util.TimeRange = new util.TimeRange();
          timeRange.fromSetDate(validDate);
          timeRange.toSetDate(expiryDate);
          let demDes = timeRange.diff("days") + 1;
          if (demDes < 0) {
            record[field.name] = oldVal;
            throw new Error("Expiry Date must be greater than or equal Valid Date!!!")
          } else {
            record['demDes'] = demDes;
          }
        }
      }
      ctx.broadcastCellEvent(event);
    };
    let containerValidate = (val: any) => {
      try {
        CONTAINER_NUMBER_VALIDATOR.validate(val);
        return null;
      } catch (error) {
        return (
          <div className='flex-grow-0' >
            <FeatherIcon.XCircle size={12} color='red' />
          </div>
        );
      }
    }
    let config: grid.VGridConfig = {
      record: {
        editor: {
          supportViewMode: ['aggregation', 'table'],
          enable: true
        },
        control: {
          width: 50,
          items: [
            {
              name: 'copy', hint: 'Copy', icon: FeatherIcon.Copy,
              onClick: (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
                let hub = dRecord.record;
                hub = { ...hub, id: null };
                ctx.model.insertDisplayRecordAt(dRecord.row, hub);
                grid.getRecordState(hub).markModified(true);
                ctx.getVGrid().forceUpdateView();
              },
            },
            {
              name: 'del', hint: 'Delete', icon: FeatherIcon.Trash2,
              onClick: (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
                let hub = dRecord.record;
                ctx.model.getDisplayRecordList().toggleDeletedDisplayRecord(dRecord.row);
                if (dRecord.getRecordState().isMarkDeleted()) {
                  ctx.model.getDisplayRecordList().markSelectDisplayRecord(dRecord.row);
                  hub['editState'] = 'DELETED';
                } else {
                  dRecord.getRecordState().selected = false;
                  delete hub['editState'];
                }
                ctx.getVGrid().forceUpdateView();
              },
            },
          ],
        },
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          {
            name: 'fileNo', label: T('File No.'), width: 120, filterableType: 'string', filterable: true,
            state: { showRecordState: true }, container: 'fixed-left',
            editor: { enable: true, type: 'string', onInputChange: onInputChange }
          },
          {
            name: 'customerImportLabel', label: T('Khách Nhập'), width: 120,
            filterableType: 'options', filterable: true,
            editor: {
              type: 'string', onInputChange: onInputChange,
            }
          },
          {
            name: 'customerImportNote', label: T('Ghi Chú'),
            filterableType: 'string', filterable: true,
            editor:
            {
              type: 'string',
              onInputChange: onInputChange,
            }
          },
          {
            name: 'containerNo', label: T('Container No'),
            filterableType: 'string', filterable: true,
            customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let containerNo = record[field.name];
              let width = field.width ? field.width : 120;
              let cssClass = field.cssClass;
              if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(ctx, dRecord)}`;
              return (
                <div className={`flex-hbox`}>
                  <bs.Tooltip className={`flex-grow-1 text-truncate ${cssClass}`} style={{ width: width - 25 }} tooltip={containerNo}>
                    {containerNo}
                  </bs.Tooltip>
                  {containerValidate(containerNo)}
                </div>
              )
            },
            editor: {
              type: 'string', onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let hub = dRecord.record;
                let containerNo = hub[field.name];
                let cssClass = field.cssClass;
                if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(thisUI.getVGridContext(), dRecord)}`;
                return (
                  <div className={`flex-hbox`}>
                    <input.BBStringField className={cssClass}
                      bean={hub} field={field.name} tabIndex={tabIndex} focus={focus} onInputChange={onInputChange} />
                    {containerValidate(containerNo)}
                  </div>
                )
              },
            }
          },
          {
            name: 'carrierFullName', label: T('Line'),
            filterableType: 'options', filterable: true,
            editor: {
              type: 'string',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let hub = dRecord.record;
                let cssClass = field.cssClass;
                if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(thisUI.getVGridContext(), dRecord)}`;
                return (
                  <BBRefTMSCarrier minWidth={400}
                    className={cssClass} appContext={appContext} pageContext={pageContext} tabIndex={tabIndex} autofocus={focus}
                    bean={hub} beanIdField={'carrierId'} beanLabelField={'carrierFullName'} placeholder={'Carrier'}
                    onPostUpdate={(_inputUI: React.Component, bean: any, _selectOpt: any, _userInput: string) => {
                      onInputChange(bean, field.name, null, bean[field.name])
                    }}
                  />
                )
              },
            }
          },
          {
            name: 'containerType', label: `Loại`, width: 100, cssClass: 'px-1', filterableType: 'options', filterable: true,
            editor: {
              type: 'string',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let hub = dRecord.record;
                if (hub.tmsBillId) {
                  return (
                    <div>{hub[field.name]}</div>
                  );
                }
                let cssClass = field.cssClass;
                if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(thisUI.getVGridContext(), dRecord)}`;
                return (
                  <BBRefTMSContainerType minWidth={400}
                    className={cssClass} appContext={appContext} pageContext={pageContext} tabIndex={tabIndex} autofocus={focus}
                    bean={hub} beanLabelField={'containerType'} placeholder={'Container Type'}
                    onPostUpdate={(_inputUI: React.Component, bean: any, selectOpt: any, _userInput: string) => {
                      onInputChange(bean, field.name, null, selectOpt['type'])
                    }}
                  />
                )
              },
            }
          },
          {
            name: 'validDate', label: T('Ngày Kéo'), format: util.text.formater.compactDate,
            filterable: true, filterableType: 'date',
            editor: {
              type: 'date',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let roundUsed = dRecord.record;
                let cssClass = field.cssClass;
                if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(thisUI.getVGridContext(), dRecord)}`;
                return (
                  <input.BBDateInputMask className={cssClass}
                    bean={roundUsed} field={field.name} tabIndex={tabIndex} focus={focus} format='DD/MM/YYYY'
                    onInputChange={onInputChange} />
                );
              },
            },
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row == cell.getRow()) {
                  cell.forceUpdate()
                }
              },
            },
          },
          {
            name: 'demDes', label: T('Dem/Des'), filterableType: 'string', filterable: true,
            editor: {
              type: 'number',
              onInputChange: onInputChange
            },
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row === cell.getRow() && (event.field.name === 'validDate' || event.field.name === 'expiryDate')) {
                  cell.forceUpdate()
                }
              },
            },
          },
          {
            name: 'expiryDate', label: T('Hạn Vỏ'), format: util.text.formater.compactDate,
            filterable: true, filterableType: 'date',
            editor: {
              type: 'date',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let hub = dRecord.record;
                let cssClass = field.cssClass;
                if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(thisUI.getVGridContext(), dRecord)}`;
                return (
                  <input.BBDateInputMask className={cssClass} disabled
                    bean={hub} field={field.name} tabIndex={tabIndex} focus={focus} format='DD/MM/YYYY'
                    onInputChange={onInputChange} />
                );
              },
            },
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row === cell.getRow() && (event.field.name === 'validDate' || event.field.name === 'demDes')) {
                  cell.forceUpdate()
                }
              },
            },
          },
          {
            name: 'liftOffDate', label: T('Ngày Hạ'), filterable: true, filterableType: 'date',
            cssClass: 'flex-grow-1 text-end',
            computeCssClasses(_ctx, dRecord) {
              if (!dRecord.isDataRecord()) {
                if (dRecord.indentLevel > 1) return 'text-warning fw-bold';
                return 'text-info fw-bold';
              }
              return '';
            },
            editor: {
              type: 'date',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let hub = dRecord.record;
                let cssClass = field.cssClass;
                if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(thisUI.getVGridContext(), dRecord)}`;
                return (
                  <input.BBDateInputMask className={cssClass}
                    bean={hub} field={field.name} tabIndex={tabIndex} focus={focus} format='DD/MM/YYYY'
                    onInputChange={onInputChange} />
                );
              },
            },
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row == cell.getRow()) {
                  cell.forceUpdate()
                }
              },
            },
          },
          {
            name: 'liftOnDate', label: T('Ngày Nâng'), format: util.text.formater.compactDate,
            filterable: true, filterableType: 'date',
            editor: {
              type: 'date',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let hub = dRecord.record;
                let cssClass = field.cssClass;
                if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(thisUI.getVGridContext(), dRecord)}`;
                return (
                  <input.BBDateInputMask className={cssClass}
                    bean={hub} field={field.name} tabIndex={tabIndex} focus={focus} format='DD/MM/YYYY'
                    onInputChange={onInputChange} />
                );
              },
            },
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row == cell.getRow()) {
                  cell.forceUpdate()
                }
              },
            },
          },
          { name: 'customerExportLabel', label: T('Khách Xuất'), width: 135, editor: { type: 'string', onInputChange: onInputChange, } },
          { name: 'customerExportNote', label: T('Ghi Chú'), editor: { type: 'string', onInputChange: onInputChange, } },
          { name: 'bookingNo', label: T('Số Booking'), width: 130, editor: { type: 'string', onInputChange: onInputChange, } },
          { name: 'sealNo', label: T('Lấy Chì'), width: 130, editor: { type: 'string', onInputChange: onInputChange, } },
          {
            name: 'status', label: T('Status'), width: 130, container: 'fixed-right', filterable: true, filterableType: 'options',
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row == cell.getRow() && (event.field.name == 'liftOnDate' || event.field.name == 'customerExportLabel')) {
                  let hub = cell.getDisplayRecord().record;
                  hub['status'] = TMSHubStatus.INVENTORY;
                  if (hub['liftOnDate'] || hub['customerExportLabel']) hub['status'] = TMSHubStatus.WAITING;
                  if (hub['liftOnDate'] && hub['customerExportLabel']) hub['status'] = TMSHubStatus.DONE;
                  cell.forceUpdate();
                }
              },
            },
            editor: {
              type: 'string',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let hub = dRecord.record;

                return (
                  <input.BBSelectField
                    className={TMSHubStatusTools.getColor(hub['status'])}
                    bean={hub} field={field.name} tabIndex={tabIndex} focus={focus}
                    options={[TMSHubStatus.INVENTORY, TMSHubStatus.WAITING, TMSHubStatus.DONE]}
                    onInputChange={onInputChange}
                  />
                );
              },
            }
          },
          ...TMSVGridConfigTool.ENTITY_COLUMNS
        ],
      },
      toolbar: {
        actions: [
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_ADD(!writeCap, T("Add")),
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_DELETE(!modCap, T('Del')),
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_STORAGE_STATES([entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED], !writeCap)
        ],
        filters: entity.DbEntityListConfigTool.TOOLBAR_FILTERS(true, 'filter'),
        filterActions: [
          ...entity.DbEntityListConfigTool.TOOLBAR_AUTO_REFRESH('auto-refresh', T('Refresh')),
        ],
      },
      footer: {
        page: {
          render: (ctx: grid.VGridContext) => {
            return <UITMSHubListPageControl context={ctx} />
          }
        },
      },
      view: {
        currentViewName: 'aggregation',
        availables: {
          table: {
            viewMode: 'table'
          },
          aggregation: {
            viewMode: 'aggregation',
            treeWidth: 135,
            createAggregationModel(_ctx: grid.VGridContext) {
              let model = new grid.AggregationDisplayModel(T('All'), false);
              model.addAggregation(
                new grid.DateValueAggregation(T("Lift Off Date"), "liftOffDate", "YYYY/MM/DD", true)
                  .withAggFunction(new CountAggregationFunction(T('Total'), 'liftOffDate', 'Cont', true))
                  .withSortBucket('desc'));
              model.addAggregation(
                new grid.ValueAggregation(T("Line"), "carrierFullName", false)
                  .withAggFunction(new CountAggregationFunction(T('Total By Line'), 'liftOffDate', 'Cont', true))
                  .withSortBucket('desc'));
              // model.addAggregation(
              //   new grid.DateValueAggregation(T("Valid Date"), "validDate", "YYYY/MM/DD", false)
              //     .withSortBucket('desc'));
              // model.addAggregation(
              //   new grid.DateValueAggregation(T("Expiry Date"), "expiryDate", "YYYY/MM/DD", false)
              //     .withSortBucket('desc'));
              return model;
            }
          }
        }
      }
    }

    let fields = config.record.fields;
    let computeCssClassesModifyBean = (_ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
      if (dRecord.isDataRecord()) {
        let state = dRecord.getRecordState();
        if (state.isMarkModified()) return 'fw-bolder fst-italic';
      }
      return '';
    }
    for (let sel of fields) {
      if (sel.name.startsWith("_")) continue;
      if (sel.listener) {
        if (!sel.listener.onDataCellEvent) {
          sel.listener.onDataCellEvent = (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
            if (event.row == cell.getRow()) {
              cell.forceUpdate()
            }
          }
        }
      } else {
        sel.listener = {
          onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
            if (event.row == cell.getRow()) {
              cell.forceUpdate()
            }
          },
        }
      }

      if (sel.computeCssClasses) {
        let cssClass = sel.computeCssClasses;
        sel.computeCssClasses = (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
          return `${cssClass(ctx, dRecord)} ${computeCssClassesModifyBean(ctx, dRecord)}`;
        }
      } else {
        sel.computeCssClasses = (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
          return computeCssClassesModifyBean(ctx, dRecord);
        }
      }
    }
    return config;
  }

  onNewAction() {
    let { plugin } = this.props;
    let newDate = new Date();
    newDate.setHours(0, 0, 0, 0);

    let validDate = util.TimeUtil.javaCompactDateTimeFormat(newDate);
    let expiryDate = validDate;
    let newHub = { validDate: validDate, demDes: 0, expiryDate: expiryDate };
    plugin.getListModel().addRecord(newHub);
    grid.getRecordState(newHub).markModified();
    this.getVGridContext().getVGrid().forceUpdateView();
  }
}

class CountAggregationFunction implements grid.IAggregationFunction {
  label: string;
  name: string;
  enable: boolean;
  fieldName: string

  constructor(name: string, fieldName: string, label: string, enable: boolean = true) {
    this.name = name;
    this.enable = enable;
    this.fieldName = fieldName;
    this.label = label;
  }

  invoke(records: Array<any>) {
    let aggRec: any = { aggLabel: this.name };
    aggRec[this.fieldName] = `${records.length} ${this.label}`;
    return aggRec;
  }
}

