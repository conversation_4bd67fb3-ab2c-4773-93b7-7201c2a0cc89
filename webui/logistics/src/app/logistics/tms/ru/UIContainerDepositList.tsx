import React from 'react';
import * as FeatherIcon from 'react-feather'
import { app, grid, sql, bs, util, input, entity } from '@datatp-ui/lib';
import { module } from '@datatp-ui/erp';

import { T } from '../backend';
import { ContainerDepositStatus, PaymentMethod } from '../models';
import { BBRefTMSCustomer } from '../partner/BBRefTMSCustomer';
import { BBRefTMSCarrier } from '../partner/BBRefTMSCarrier';
import { BBRefVehicleFleet } from '../vehicle/BBRefVehicleFleet';

export class UIContainerDepositListPlugin extends entity.DbEntityListPlugin {
  constructor() {
    super([]);

    this.backend = {
      context: 'company',
      service: 'TMSRoundUsedService',
      searchMethod: 'searchContainerDeposit'
    }

    this.searchParams = {
      filters: [...sql.createSearchFilter()],
      optionFilters: [sql.createStorageStateFilter([entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED])],
      maxReturn: 1000,
    };

  }

  loadData(uiList: entity.DbEntityList<any>): void {
    this.createBackendSearch(uiList, { sqlParams: this.searchParams }).call();
  }
}

export class UIContainerDepositList extends entity.DbEntityList {

  parseToString(value: any) {
    if (value === null || value === undefined) {
      return '';
    }
    return String(value);
  }

  processContainerDetails(containerDetails: Array<any>) {
    let value = "";
    for (let containerDetail of containerDetails) {
      value += 'Quantity: ' + this.parseToString(containerDetail.quantity);
      value += ' Container Type: ' + this.parseToString(containerDetail.containerType);
      value += ' Price: ' + this.parseToString(containerDetail.price);
      value += ' Container No: ' + this.parseToString(containerDetail.containerNo);
      value += ' Vendor: ' + this.parseToString(containerDetail.vendorName);
      if (containerDetails.indexOf(containerDetail) != containerDetails.length - 1) {
        value += ' , '
      }
    }
    return value;
  }

  createVGridConfig() {
    let { appContext, pageContext } = this.props;
    const writeCap = pageContext.hasUserWriteCapability();
    const modCap = pageContext.hasUserModeratorCapability();
    let uiRoot = this;
    let onInputChange = (fieldCtx: grid.FieldContext, oldVal: any, newVal: any) => {
      let field = fieldCtx.fieldConfig;
      let dRecord = fieldCtx.displayRecord;
      let ctx = fieldCtx.gridContext;
      let event: grid.VGridCellEvent = {
        row: dRecord.row,
        field: field,
        event: 'Modified'
      }
      ctx.broadcastCellEvent(event);
    }
    let config: grid.VGridConfig = {
      title: 'Container Deposit',
      record: {
        computeDataRowHeight: (_ctx: grid.VGridContext, dRec: grid.DisplayRecord) => {
          return 35;
        },
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          {
            name: 'fileNo', label: T('File No'), width: 150, editor: {
              type: 'string',
              onInputChange: onInputChange
            },
            state: { showRecordState: true },
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row === cell.getRow()) {
                  cell.forceUpdate()
                }
              },
            },
          },
          { name: 'code', label: T('Code'), state: { visible: false }, width: 150 },
          {
            name: 'requestDate', label: T('Request Date'), state: { visible: true }, width: 150, format: util.text.formater.compactDate,
            editor: {
              type: 'date',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let containerDeposit = dRecord.record;
                let cssClass = field.cssClass;
                return (
                  <input.BBDateInputMask className={cssClass}
                    bean={containerDeposit} field={field.name} tabIndex={tabIndex} focus={focus} format='DD/MM/YYYY'
                    onInputChange={onInputChange} />
                );
              },
            }
          },
          {
            name: 'vendorName', label: T('Vendor'), width: 200, cssClass: 'px-1', filterableType: 'options', filterable: true,
            editor: {
              type: 'string',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let containerDeposit = dRecord.record;
                let cssClass = field.cssClass;
                const oldVal = containerDeposit[field.name];
                return (
                  <BBRefVehicleFleet minWidth={400}
                    className={cssClass} appContext={appContext} pageContext={pageContext} tabIndex={tabIndex} autofocus={focus}
                    bean={containerDeposit} beanIdField={'vendorId'} beanLabelField={'vendorName'} placeholder={'Vendor'}
                    onPostUpdate={(_inputUI: React.Component, bean: any, _selectOpt: any, _userInput: string) => {
                      onInputChange(bean, field.name, oldVal, bean[field.name])
                    }}
                  />
                )
              },
            }
          },
          {
            name: 'customerName', label: T('Customer'), width: 170, cssClass: 'px-1', sortable: true,
            dataTooltip: true, filterableType: 'Options', filterable: true,
            editor: {
              type: 'string',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let containerDeposit = dRecord.record;
                const oldVal = containerDeposit[field.name];
                let cssClass = field.cssClass;
                if (field.computeCssClasses) cssClass = field.computeCssClasses(uiRoot.getVGridContext(), dRecord);
                return (
                  <BBRefTMSCustomer minWidth={400} allowUserInput
                    className={cssClass} appContext={appContext} pageContext={pageContext} tabIndex={tabIndex} autofocus={focus}
                    bean={containerDeposit} beanIdField={'customerId'} beanLabelField={'customerName'} placeholder={'Customer'}
                    onPostUpdate={(_inputUI: React.Component, bean: any, _selectOpt: any, _userInput: string) => {
                      onInputChange(bean, field.name, oldVal, bean[field.name])
                    }}
                  />
                )
              },
            }
          },
          {
            name: 'containerDetail', label: T('Container'), width: 170,
            customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let containerDeposit = dRecord.record;
              let containerDetail: string = "";
              let containerDetails: Array<any> = containerDeposit['containerDetails'];
              containerDetail = this.processContainerDetails(containerDetails);
              return <bs.Tooltip tooltip={containerDetail}> {containerDetail} </bs.Tooltip>
            },
            editor: {
              type: 'string',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let containerDeposit = dRecord.record;
                let containerDetail = "";
                let containerDetails: Array<any> = containerDeposit['containerDetails'];
                containerDetail = uiRoot.processContainerDetails(containerDetails);
                return (
                  <div className={`flex-hbox`} style={{ marginLeft: 5, marginRight: 5 }} onClick={() => { uiRoot.onShowContainerDetail(containerDeposit) }}>
                    {containerDetail ?
                      <bs.Tooltip tooltip={containerDetail}> {containerDetail} </bs.Tooltip> :
                      <bs.Button className="px-1 py-0" laf="secondary" outline>
                        <FeatherIcon.Plus size={12} />
                      </bs.Button>
                    }
                  </div>
                )
              },

            }
          },
          {
            name: 'totalRequestFee', label: T('Total Request Fee'), state: { visible: true }, width: 150, format: util.text.formater.currency,
            editor: {
              type: 'currency',
              onInputChange: onInputChange
            }
          },
          { name: 'requestNote', label: T('Request Note'), state: { visible: true }, width: 150, editor: { type: 'text', onInputChange: onInputChange } },
          {
            name: 'carrierFullName', label: T('Carrier'), width: 200,
            editor: {
              type: 'string',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let containerDeposit = dRecord.record;
                const oldVal = containerDeposit[field.name];
                let cssClass = field.cssClass;
                if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiRoot.getVGridContext(), dRecord)}`;
                return (
                  <BBRefTMSCarrier minWidth={400}
                    className={cssClass} appContext={appContext} pageContext={pageContext} tabIndex={tabIndex} autofocus={focus}
                    bean={containerDeposit} beanIdField={'carrierId'} beanLabelField={'carrierName'} placeholder={'Carrier'}
                    onPostUpdate={(_inputUI: React.Component, bean: any, _selectOpt: any, _userInput: string) => {
                      onInputChange(bean, field.name, oldVal, bean[field.name])
                    }}
                  />
                )
              },
            }
          },
          {
            name: 'bookingCode', label: T('Booking Code'), state: { visible: true }, width: 150,
            editor: { type: 'string', onInputChange: onInputChange }
          },
          {
            name: 'courier', label: T('Courier'), state: { visible: true }, width: 150,
            editor: { type: 'string', onInputChange: onInputChange }
          },
          {
            name: 'paymentMethod', label: T('Payment Method'), state: { visible: true }, width: 150,
            editor: {
              type: 'string',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let record = dRecord.record;
                let cssClass = field.cssClass;
                if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiRoot.getVGridContext(), dRecord)}`;
                return (
                  <input.BBSelectField className={cssClass}
                    bean={record}
                    field={field.name}
                    tabIndex={tabIndex} focus={focus}
                    options={[PaymentMethod.Cash, PaymentMethod.Wired]}
                    onInputChange={onInputChange} />
                )
              },
            }
          },
          {
            name: 'eirDate', label: T('Eir Date'), state: { visible: true }, width: 150, format: util.text.formater.compactDate,
            editor: {
              type: 'date',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let containerDeposit = dRecord.record;
                let cssClass = field.cssClass;
                return (
                  <input.BBDateInputMask className={cssClass}
                    bean={containerDeposit} field={field.name} tabIndex={tabIndex} focus={focus} format='DD/MM/YYYY'
                    onInputChange={onInputChange} />
                );
              },
            }
          },
          { name: 'receiver', label: T('Receiver'), state: { visible: true }, width: 150, editor: { type: 'string', onInputChange: onInputChange } },
          {
            name: 'depositDate', label: T('Deposit Date'), state: { visible: true }, width: 150, format: util.text.formater.compactDate,
            editor: {
              type: 'date',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let containerDeposit = dRecord.record;
                let cssClass = field.cssClass;
                return (
                  <input.BBDateInputMask className={cssClass}
                    bean={containerDeposit} field={field.name} tabIndex={tabIndex} focus={focus} format='DD/MM/YYYY'
                    onInputChange={onInputChange} />
                );
              },
            }
          },
          {
            name: 'totalDeposit', label: T('Total Deposit'), state: { visible: true }, width: 150, format: util.text.formater.currency,
            editor: {
              type: 'currency',
              onInputChange: onInputChange
            }
          },
          {
            name: 'refundDate', label: T('Refund Date'), state: { visible: true }, width: 150, format: util.text.formater.compactDate,
            editor: {
              type: 'date',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let containerDeposit = dRecord.record;
                let cssClass = field.cssClass;
                return (
                  <input.BBDateInputMask className={cssClass}
                    bean={containerDeposit} field={field.name} tabIndex={tabIndex} focus={focus} format='DD/MM/YYYY'
                    onInputChange={onInputChange} />
                );
              },
            }
          },
          {
            name: 'totalRefund', label: T('Total Refund'), state: { visible: true }, width: 150, format: util.text.formater.currency,
            editor: {
              type: 'currency',
              onInputChange: onInputChange
            }
          },
          {
            name: 'refundPaymentMethod', label: T('Refund Payment Method'), hint: T('Refund Payment Method'), state: { visible: true }, width: 150,
            editor: {
              type: 'string',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let record = dRecord.record;
                let cssClass = field.cssClass;
                if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiRoot.getVGridContext(), dRecord)}`;
                return (
                  <input.BBSelectField className={cssClass}
                    bean={record}
                    field={field.name}
                    tabIndex={tabIndex} focus={focus}
                    options={[PaymentMethod.Cash, PaymentMethod.Wired]}
                    onInputChange={onInputChange} />
                )
              },
            }
          },
          { name: 'note', label: T('Note'), state: { visible: true }, width: 150, editor: { type: 'text', onInputChange: onInputChange } },
          {
            name: 'status', label: T('Status'), state: { visible: true }, width: 150,
            editor: {
              type: 'string',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let record = dRecord.record;
                let cssClass = field.cssClass;
                return (
                  <input.BBSelectField className={cssClass}
                    bean={record}
                    field={field.name}
                    tabIndex={tabIndex} focus={focus}
                    options={[ContainerDepositStatus.NEW_REQUEST, ContainerDepositStatus.PROCESSING_REQUEST,
                    ContainerDepositStatus.REQUESTED, ContainerDepositStatus.DEPOSITING,
                    ContainerDepositStatus.DEPOSITED, ContainerDepositStatus.REFUNDED]}
                    onInputChange={onInputChange} />
                )
              },
            }
          },
          ...entity.DbEntityListConfigTool.FIELD_ENTITY,
        ],
        editor: {
          enable: true,
          supportViewMode: ['table']
        }
      },
      toolbar: {
        actions: [
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_ADD(!writeCap, T('Add')),
        ],
        filters: entity.DbEntityListConfigTool.TOOLBAR_FILTERS(true),
      },

      footer: {
        page: {
          render: (ctx: grid.VGridContext) => {
            return <bs.Toolbar className='border' hide={!writeCap}>
              <entity.WButtonEntityWrite appContext={appContext} pageContext={pageContext} icon={FeatherIcon.Save}
                label={T('Save')} onClick={this.onSave} />
            </bs.Toolbar >
          }
        },
      },

      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table',
          },
        },
      },
    };
    return config;
  }

  onShowContainerDetail(containerDeposit: any) {
    let { appContext, pageContext } = this.props

    appContext.createHttpBackendCall('TMSRoundUsedService', 'getContainerDeposit', { id: containerDeposit.id })
      .withSuccessData((data: any) => {
        let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          let observer = new entity.ComplexBeanObserver(data);
          let onSave = () => {
            observer.commitAndGet()
            let containerDeposit = observer.getMutableBean();
            appCtx.createHttpBackendCall('TMSRoundUsedService', 'saveContainerDeposit', { containerDeposit: containerDeposit })
              .withSuccessData((_data: any) => {
                appCtx.addOSNotification("success", T("Save Success"));
                pageCtx.back();
                this.reloadData();
              })
              .call();
          }
          return <div className='flex-vbox'>
            <UIContainerDetailList appContext={appCtx} pageContext={pageCtx} style={{ height: 350 }}
              plugin={observer.createVGridEntityListEditorPlugin('containers', [])}
              dialogEditor={true} editorTitle={T("Container Detail")}
            />
            <bs.Toolbar className='border'>
              <entity.WButtonEntityWrite
                appContext={appCtx} pageContext={pageCtx} icon={FeatherIcon.Save}
                label={T('Save')} onClick={onSave} />
            </bs.Toolbar>
          </div>
        }
        pageContext.createPopupPage('container-detail', T('Container Detail'), createAppPage)
      })
      .call();
  }

  onNewAction() {
    let { plugin } = this.props;
    let newRecord = {};
    plugin.getListModel().addRecord(newRecord);
    let state = grid.getRecordState(newRecord);
    state.markModified();
    this.getVGridContext().getVGrid().forceUpdateView();
  }

  onSave = () => {
    let { appContext, pageContext, plugin } = this.props;
    let modifiedRecords = plugin.getListModel().getMarkModifiedRecords();
    appContext.createHttpBackendCall('TMSRoundUsedService', 'saveContainerDeposits', { containerDeposits: modifiedRecords })
      .withSuccessData((_data: any) => {
        appContext.addOSNotification("success", T('Success'));
        this.reloadData();
      })
      .call();
  }
}

export class UIContainerDetailList extends entity.VGridEntityListEditor {
  createVGridConfig() {
    let { appContext, pageContext } = this.props;
    let onInputChange = (fieldCtx: grid.FieldContext, oldVal: any, newVal: any) => {
      let dRecord = fieldCtx.displayRecord;
      let field = fieldCtx.fieldConfig;
      let ctx = fieldCtx.gridContext;
      let event: grid.VGridCellEvent = {
        row: dRecord.row, field: field, event: 'Modified', data: dRecord
      }
      ctx.broadcastCellEvent(event);
    };


    let writeCap = pageContext.hasUserWriteCapability();
    let config: grid.VGridConfig = {
      record: {
        editor: {
          supportViewMode: ['table'],
          enable: true
        },
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          {
            name: 'quantity', label: T('Quantity'), width: 150,
            state: { showRecordState: true },
            editor: { type: 'double', onInputChange: onInputChange, },
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row === cell.getRow()) {
                  cell.forceUpdate()
                }
              },
            },
          },
          {
            name: 'containerType', label: T('Container Type'), width: 150,
            editor: {
              type: 'string',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let containerDetail = dRecord.record;
                const oldVal = containerDetail[field.name];
                let cssClass = field.cssClass;
                return (
                  <module.settings.BBRefUnit
                    className={cssClass}
                    appContext={appContext} pageContext={pageContext}
                    minWidth={300}
                    placement="left"
                    placeholder="Enter Unit"
                    groupNames={['truck']}
                    bean={containerDetail} beanIdField={field.name}
                    tabIndex={tabIndex} autofocus={focus}
                    onPostUpdate={(inputUI, bean, selectOpt, userInput) => {
                      onInputChange(bean, field.name, oldVal, bean[field.name])
                    }} />
                )
              },
            }
          },
          {
            name: 'price', label: T('Price'), width: 150, format: util.text.formater.currency, editor: { type: 'currency', onInputChange: onInputChange }
          },
          { name: 'containerNo', label: T('Container No'), width: 200, editor: { type: 'text', onInputChange: onInputChange } },
          {
            name: 'vendorName', label: T('Vendor'), width: 200, cssClass: 'px-1', filterableType: 'options', filterable: true,
            state: { visible: false },
            editor: {
              type: 'string',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let containerDeposit = dRecord.record;
                let cssClass = field.cssClass;
                const oldVal = containerDeposit[field.name];
                return (
                  <BBRefVehicleFleet minWidth={400}
                    className={cssClass} appContext={appContext} pageContext={pageContext} tabIndex={tabIndex} autofocus={focus}
                    bean={containerDeposit} beanIdField={'vendorId'} beanLabelField={'vendorName'} placeholder={'Vendor'}
                    onPostUpdate={(_inputUI: React.Component, bean: any, _selectOpt: any, _userInput: string) => {
                      onInputChange(bean, field.name, oldVal, bean[field.name])
                    }}
                  />
                )
              },
            }
          },
        ],
        computeDataRowHeight: (_ctx: grid.VGridContext, dRec: grid.DisplayRecord) => {
          return 35;
        },
        control: {
          width: 25,
          items: [
            {
              name: 'copy', hint: 'Copy', icon: FeatherIcon.Copy,
              onClick: (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
                let containerDetail = dRecord.record;
                containerDetail = { ...containerDetail, id: null };
                ctx.model.insertDisplayRecordAt(dRecord.row, containerDetail);
                grid.getRecordState(containerDetail).markNew(true);
                ctx.getVGrid().forceUpdateView();
              },
            },
          ]
        }
      },
      toolbar: {
        actions: [
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_DELETE(!writeCap, T('Delete')),
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_ADD(!writeCap, T('Add')),
        ],
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          }
        },
      },

    }
    return config;
  }

  onNewAction() {
    let { plugin } = this.props;
    let newRecord = {};
    let records = plugin.getModel().getRecords();
    records.unshift(newRecord);
    plugin.replaceBeans(records);
    grid.getRecordState(newRecord).markNew();
    this.vgridContext.getVGrid().forceUpdateView();
  }
}
