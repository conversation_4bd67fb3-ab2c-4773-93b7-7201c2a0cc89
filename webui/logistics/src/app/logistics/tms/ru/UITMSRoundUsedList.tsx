import React, { Component } from 'react';
import * as FeatherIcon from 'react-feather'
import { server, util, bs, grid, input, app, entity } from '@datatp-ui/lib';
import { module } from "@datatp-ui/erp";

import { T } from '../backend';
import { UITMSRoundUsedListPlugin } from './UITMSRoundUsedListPlugin';
import { UITMSBillUtils } from '../bill/UITMSBillUtils';
import { UITMSBillList, UITMSBillListPlugin } from '../bill/UITMSBillList';
import { TMSRoundUsedStatus, TMSRoundUsedType } from '../models';
import { TMSRoundUsedStatusTools, TMSUtils } from '../utils';
import { UITMSReceiptOfDeliveryPrint } from '../bill/UITMSBillPrint';
import { WButtonPopupUIFindTMSBillList } from '../bill/UIFindTMSBillList';
import { BBRefTransporter } from '../transport/BBRefTransporter';
import { JobTrackingListConfig, UIJobTrackingDetailForm } from 'app/logistics/jobtracking';

import { BBRefTMSCustomer } from '../partner/BBRefTMSCustomer';
import { BBRefTMSCarrier } from '../partner/BBRefTMSCarrier';
import { XLSXCustomButton } from '../XLSXButton';

import BBRefState = module.settings.BBRefState;
import BBRefLocation = module.settings.BBRefLocation;
import { BBRefVehicleFleet } from '../vehicle/BBRefVehicleFleet';
import { BBRefVehicle } from '../vehicle/BBRefVehicle';
import { BBRefTMSContainerType } from './container/BBRefTMSContainerType';
const { CONTAINER_NUMBER_VALIDATOR } = util.validator;

export class UITMSRoundUsedListPageControl extends Component<grid.VGridContextProps> {
  saving = false;

  onNewRoundUsedFromBill = () => {
    let { context } = this.props;
    let uiRoot = context.uiRoot as entity.DbEntityList;
    let { appContext, pageContext, plugin } = uiRoot.props;

    let onSelectBills = (_appCtx: app.AppContext, _pageCtx: app.PageContext, billModels: any[]) => {
      appContext.createHttpBackendCall('TMSRoundUsedService', 'createTMSRoundUsedByTMSBills', { billModels: billModels })
        .withSuccessData((_data: any) => {
          _appCtx.addOSNotification("success", T("Create Round Used Success"));
          uiRoot.reloadData();
          _pageCtx.back();
        })
        .call();
    }

    let onSelectBill = (_appCtx: app.AppContext, _pageCtx: app.PageContext, billModel: any) => {
      appContext.createHttpBackendCall('TMSRoundUsedService', 'createTMSRoundUsedByTMSBills', { billModels: [billModel] })
        .withSuccessData((data: any) => {
          let models: any[] = data;
          _appCtx.addOSNotification("success", T("Create Round Used Success"));
          context.model.getRecordFilter().withPattern(models[0].fileTruck);
          uiRoot.reloadData();
          _pageCtx.back();
        })
        .call();
    }

    let createAppPage = (_appCtx: app.AppContext, _pageCtx: app.PageContext) => {
      let dateRange = new util.TimeRange();
      dateRange.fromSetDate(new Date());
      dateRange.toAdd(2, 'month');
      return (
        <div className='flex-vbox'>
          <UITMSBillList type={'selector'} readOnly
            appContext={_appCtx} pageContext={_pageCtx} plugin={new UITMSBillListPlugin(dateRange).withExcludeRoundUsed()}
            onSelect={onSelectBill}
            onMultiSelect={onSelectBills} />
        </div>
      )
    }
    pageContext.createPopupPage('bill-selector', T('TMS Bills'), createAppPage, { size: 'xl' })
  }

  updateTMSRoundUsedData = (updatedRoundUsed: Array<any>) => {
    let { context } = this.props;
    let uiRoot = context.uiRoot as entity.DbEntityList;
    let { appContext } = uiRoot.props;
    let pluginImpl = new UITMSRoundUsedListPlugin();
    let ids: Array<any> = [];
    updatedRoundUsed.forEach(sel => ids.push(sel.id));

    let searchParams = pluginImpl.searchParams;
    if (searchParams) {
      searchParams.params = {
        ...searchParams.params,
        ids: ids.length > 0 ? ids : null
      }
    }

    appContext.createHttpBackendCall('TMSRoundUsedService', 'searchTMSRoundUsed', { params: searchParams })
      .withSuccessData((data: any) => {
        for (let roundUsed of updatedRoundUsed) {
          for (let result of data) {
            if (roundUsed['id'] == result['id']) {
              for (const propertyName in result) {
                roundUsed[propertyName] = result[propertyName];
              }
            }
          }
          let state = grid.getRecordState(roundUsed);
          roundUsed['_state'] = new grid.RecordState(state.row);
        }
        appContext.addOSNotification("success", T("Save Round Used Success"));
        context.getVGrid().forceUpdateView();
        this.saving = false;
        this.forceUpdate();
      })
      .call();
  }

  onSaveEditedRoundUsed = () => {
    let { context } = this.props;
    let uiRoot = context.uiRoot as entity.DbEntityList;
    let { appContext, plugin } = uiRoot.props;
    let modifiedRecords = context.model.getMarkModifiedRecords();
    let deleteRecords = context.model.getMarkDeletedRecords();
    let records = [...modifiedRecords, ...deleteRecords];
    if (records.length === 0) {
      let selectedRecords = plugin.getListModel().getSelectedRecords();
      selectedRecords.forEach(sel => {
        let state = grid.getRecordState(sel);
        sel['_state'] = new grid.RecordState(state.row);
      })
      appContext.addOSNotification("success", T("No Rows is Edited"));
      uiRoot.forceUpdate();
    } else {

      for (let rec of modifiedRecords) {
        if (!rec['id']) rec['uikey'] = `new-${util.IDTracker.next()}`;
      }
      context.getVGrid().forceUpdateView();
      appContext.createHttpBackendCall('TMSRoundUsedService', 'saveTMSRoundUsedModels', { models: records })
        .withSuccessData((data: any) => {
          let results: Array<any> = data;
          for (let modifiedRecord of modifiedRecords) {
            for (let result of results) {
              if (modifiedRecord['uikey'] && modifiedRecord['uikey'] === result['uikey']) {
                modifiedRecord['id'] = result['id'];
              }
            }
          }
          for (let sel of deleteRecords) {
            plugin.getListModel().removeRecord(sel);
          }
          this.updateTMSRoundUsedData(modifiedRecords);
        })
        .withFail((response: server.BackendResponse) => {
          server.rest.handleResponseError('', response);
          this.saving = false;
          this.forceUpdate();
        })
        .call();
      this.saving = true;
      this.forceUpdate();
    }
  }

  render() {
    let { context } = this.props;
    let uiRoot = context.uiRoot as app.AppComponent;
    let { appContext, pageContext } = uiRoot.props;
    let writeCap = pageContext.hasUserWriteCapability();

    return (
      <bs.Toolbar className='border' hide={!pageContext.hasUserReadCapability()}>
        {writeCap ?
          <>
            <XLSXCustomButton tableName="tms-bill" context={context}
              options={{ fileName: `RU ${util.TimeUtil.toCompactDateFormat(new Date())}.xlsx`, modelName: 'tms-ru' }}
              appContext={appContext} pageContext={pageContext} />
            <bs.Button laf='primary' disabled={this.saving || !writeCap} onClick={this.onNewRoundUsedFromBill}>
              <FeatherIcon.List size={12} />
              <span style={{ marginLeft: 4 }}>{T('Collect From Bill')}</span>
            </bs.Button>
            <bs.Button laf='primary' disabled={this.saving || !writeCap} onClick={this.onSaveEditedRoundUsed}>
              {this.saving ?
                <FeatherIcon.Loader size={12} style={{ animation: '0.75s linear infinite spinner-border' }} />
                :
                <FeatherIcon.Save size={12} />
              }
              <span style={{ marginLeft: 4 }}>{T('Save Changes')}</span>
            </bs.Button>
          </>
          :
          null
        }
      </bs.Toolbar>
    )
  }
}

interface UITMSRoundUsedListProps extends entity.DbEntityListProps {
  jobTrackingListConfig?: JobTrackingListConfig
}
export class UITMSRoundUsedList extends entity.DbEntityList<UITMSRoundUsedListProps> {

  onShowJobTrackingStep = (jobTrackingListConfig: any, roundUsed: any,) => {
    let { appContext, pageContext } = this.props;
    const writeCap = pageContext.hasUserWriteCapability();
    let jobProject = jobTrackingListConfig.project;

    let params = {
      jobProjectId: jobProject['id'],
      jobTrackingId: roundUsed['jobTrackingId'] ? roundUsed['jobTrackingId'] : null
    }

    appContext.createHttpBackendCall('JobTrackingService', 'createOrGetJobTrackingMapRecordById', params)
      .withSuccessData((data: any) => {
        let jobTrackingId = data['jobTrackingId'];
        if (!roundUsed['jobTrackingId']) {
          let params = {
            'roundUsedId': roundUsed['id'],
            'jobTrackingId': jobTrackingId
          }

          appContext.createHttpBackendCall('TMSRoundUsedService', 'updateTMSRoundUsedJobTrackingId', params)
            .withSuccessData((data: any) => {
              let roundUsedInDB = data;
              roundUsed['jobTrackingId'] = roundUsedInDB['jobTrackingId'];
              appContext.addOSNotification("success", T('Update TMSRoundUsed Job Tracking Success'))
            })
            .call();
        }

        let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return (
            <UIJobTrackingDetailForm
              appContext={appCtx} pageContext={pageCtx} readOnly={!writeCap}
              listConfig={jobTrackingListConfig}
              observer={new entity.BeanObserver(data)} onPostCommit={(bean) => {
                roundUsed['stepDoneCount'] = bean['stepDoneCount'];
                roundUsed['currentStep'] = bean['lastStepName'];
                this.getVGridContext().vgrid?.forceUpdateView();
              }} />
          )
        }
        pageContext.createPopupPage('working-step', T('Working Step'), createAppPage, { size: 'xl' });
      })
      .call();
  }

  renderWorkingStepButton = (roundUsed: any) => {
    let { jobTrackingListConfig } = this.props;
    if (!jobTrackingListConfig) return;
    let currentStep = roundUsed['currentStep'];
    let label = '[...]';
    if (!roundUsed.id) return null;
    if (currentStep) {
      label = `[${roundUsed['currentStep']}]`;
    } else {
      currentStep = label;
    }
    let verifyJobTrackingError = roundUsed['verifyJobTrackingError'];
    return (
      <bs.Button className={`flex-grow-0 align-items-left ${verifyJobTrackingError ? 'text-danger' : 'text-warning'}`}
        style={{ width: 25, fontSize: 12 }}
        onClick={() => this.onShowJobTrackingStep(jobTrackingListConfig, roundUsed)} laf='link'>
        {label}
      </bs.Button>
    )
  }

  renderRoundUsedStatus = (roundUsed: any) => {
    let status: TMSRoundUsedStatus = roundUsed.status;
    if (!status) return;
    let title = `${status}`;
    let onUpdateStatus = (newStatus: TMSRoundUsedStatus) => {
      let { appContext } = this.props;
      appContext.createHttpBackendCall('TMSRoundUsedService', 'updateTMSRoundUsedStatus', { id: roundUsed.id, status: newStatus })
        .withSuccessData((_data: any) => {
          roundUsed.status = newStatus;
          appContext.addOSNotification("success", T(`${newStatus} Round Used Success`));
          this.getVGridContext().getVGrid().forceUpdateView();
        })
        .call();
    }

    let createStatusOptionButton = (label: string, status: TMSRoundUsedStatus) => {
      return (
        <bs.Button laf={TMSRoundUsedStatusTools.getColor(status)} style={{ fontSize: 12, borderRadius: 5 }} className='p-2 mx-1'
          onClick={() => onUpdateStatus(status)}>
          {label}
        </bs.Button>
      )
    }

    let textCssClass = TMSRoundUsedStatusTools.getTextColor(status);
    let statusOptions = [];
    if (status === TMSRoundUsedStatus.SendingFromBill) {
      title = 'Request From Bill';
      statusOptions.push(
        createStatusOptionButton('Confirm', TMSRoundUsedStatus.Processing),
        createStatusOptionButton('Reject', TMSRoundUsedStatus.Cancel),
      );
    } else if (status === TMSRoundUsedStatus.Processing) {
      statusOptions.push(
        createStatusOptionButton('Done', TMSRoundUsedStatus.Done),
        createStatusOptionButton('Cancel', TMSRoundUsedStatus.Cancel),
      );
    } else if (status === TMSRoundUsedStatus.Done) {
      statusOptions.push(
        createStatusOptionButton('Processing', TMSRoundUsedStatus.Processing),
        createStatusOptionButton('Cancel', TMSRoundUsedStatus.Cancel),
      );
    } else if (status === TMSRoundUsedStatus.Cancel) {
      statusOptions.push(
        createStatusOptionButton('Processing', TMSRoundUsedStatus.Processing),
        createStatusOptionButton('Done', TMSRoundUsedStatus.Done),
      );
    }

    if (statusOptions && statusOptions.length > 0) {
      return (
        <bs.Popover flex-hbox-grow-0 closeOnTrigger=".btn">
          <bs.PopoverToggle laf='secondary' className={`border-0 bg-body px-1 w-100`}>
            <div title={`Round Used: ${title}`} className={`${textCssClass} text-start fw-bold`}>
              {title}
            </div>
          </bs.PopoverToggle>
          <bs.PopoverContent>
            <div className='flex-hbox align-items-center'>
              {statusOptions}
            </div>
          </bs.PopoverContent>
        </bs.Popover >
      )
    }
    return (
      <div title={`Sending Request To TMS Bill`} className={`${textCssClass} px-1 fw-bold`}>
        {'Sending'}
      </div>
    );
  }

  createVGridConfig() {
    let { type, appContext, pageContext, jobTrackingListConfig } = this.props;
    const writeCap = pageContext.hasUserWriteCapability();
    const modCap = pageContext.hasUserModeratorCapability();
    if (jobTrackingListConfig) jobTrackingListConfig.buildFields(this);

    let uiRoot = this;
    let onInputChange = (fieldCtx: grid.FieldContext, oldVal: any, newVal: any) => {
      let ctx = fieldCtx.gridContext;
      let dRecord = fieldCtx.displayRecord;
      let field = fieldCtx.fieldConfig;
      let event: grid.VGridCellEvent = {
        row: dRecord.row, field: field, event: 'Modified', data: dRecord.record
      }
      if (field.name === 'vehicleLabel' || field.name === 'vehicleDriverFullName') {
        dRecord.record['vehicleInfoModified'] = true;
      }
      ctx.broadcastCellEvent(event);
    };

    let controlWidth = bs.ScreenUtil.isSmallScreen() ? 25 : 45;
    let configId = `tms-round-used-list`;
    let config: grid.VGridConfig = {
      id: configId,
      title: `TMS Round Used`,
      record: {
        editor: {
          supportViewMode: ['aggregation', 'table'],
          enable: true
        },
        computeDataRowHeight: (_ctx: grid.VGridContext, dRec: grid.DisplayRecord) => {
          if (dRec.isDataRecord()) return 35;
          return 20;
        },
        control: {
          width: controlWidth,
          items: [
            {
              name: 'copy', hint: 'Copy', icon: FeatherIcon.Copy,
              onClick: (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
                let roundUsed = dRecord.record;
                roundUsed = {
                  ...roundUsed,
                  id: null,
                  tmsBillId: null,
                  jobTrackingId: null,
                  vendorId: null,
                  vendorFullName: null,
                  vehicleId: null,
                  vehicleLabel: null,
                  vehicleDriverId: null,
                  vehicleDriverFullName: null,
                  vehicleDriverIdentificationNo: null,
                  vehicleDriverMobile: null
                };
                ctx.model.insertDisplayRecordAt(dRecord.row, roundUsed);
                grid.getRecordState(roundUsed).markModified(true);
                ctx.getVGrid().forceUpdateView();
              },
            },
            {
              name: 'del', hint: 'Delete', icon: FeatherIcon.Trash2,
              onClick: (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
                let roundUsed = dRecord.record;
                ctx.model.getDisplayRecordList().toggleDeletedDisplayRecord(dRecord.row);
                if (dRecord.getRecordState().isMarkDeleted()) {
                  ctx.model.getDisplayRecordList().markSelectDisplayRecord(dRecord.row);
                  roundUsed['editState'] = 'DELETED';
                } else {
                  dRecord.getRecordState().selected = false;
                  delete roundUsed['editState'];
                }
                ctx.getVGrid().forceUpdateView();
              },
            },
          ],
        },
        fields: [
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),
          {
            name: 'fileRu', label: T('File Ru'), width: 150, filterableType: 'string', filterable: true,
            state: { showRecordState: true }, container: 'fixed-left',
            onClick: (ctx: grid.VGridContext, record: grid.DisplayRecord) => {
              uiRoot.onSelect(record);
            },
            editor: {
              type: 'string', onInputChange: onInputChange
            },
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row == cell.getRow()) {
                  cell.forceUpdate()
                }
              },
            },
            customRender(ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) {
              let roundUsed = dRecord.record;
              let cssClass = field.cssClass;
              return <div className={cssClass}>{roundUsed[field.name]}</div>
            },
          },
          {
            name: 'fileTruck', label: T('File Truck'), width: 170, filterableType: 'string', filterable: true,
            state: { showRecordState: true }, container: 'fixed-left',
            onClick: (ctx: grid.VGridContext, record: grid.DisplayRecord) => {
              uiRoot.onSelect(record);
            },
            editor: {
              type: 'string', onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let field = fieldCtx.fieldConfig;
                let dRecord = fieldCtx.displayRecord;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let roundUsed = dRecord.record;
                let disable = !roundUsed['billOwner'] && roundUsed['id'];;
                return (
                  <div className='flex-hbox'>
                    <input.BBStringField disable={disable} className={field.cssClass} tabIndex={tabIndex} focus={focus}
                      field={field.name} bean={roundUsed} onInputChange={onInputChange} />
                    {roundUsed['isModifiedFileTruck'] ?
                      <div title='File Truck is Modified'>
                        <bs.Button laf='link' className='text-warning'>
                          <FeatherIcon.AlertTriangle size={12} />
                        </bs.Button>
                      </div>
                      : null
                    }
                  </div>
                );
              },
            },
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row == cell.getRow()) {
                  cell.forceUpdate()
                }
              },
            },
            customRender(ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) {
              let roundUsed = dRecord.record;
              let cssClass = field.cssClass;
              return <div className={cssClass}>{roundUsed[field.name]}</div>
            },
          },
          {
            name: 'customerFullName', label: 'Customer', width: 170, cssClass: 'px-1', sortable: true,
            dataTooltip: true, filterableType: 'Options', filterable: true, container: 'fixed-left',
            computeCssClasses: (_ctx, dRecord) => {
              return dRecord.record['customerId'] == null ? 'text-warning' : '';
            },
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row == cell.getRow() && event.field.name === 'customerFullName') {
                  cell.forceUpdate()
                }
              },
            },
            editor: {
              type: 'string',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let field = fieldCtx.fieldConfig;
                let dRecord = fieldCtx.displayRecord;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let roundUsed = dRecord.record;
                let readOnly = !roundUsed['billOwner'] && roundUsed['id'];
                let cssClass = field.cssClass;
                if (field.computeCssClasses) cssClass = field.computeCssClasses(uiRoot.getVGridContext(), dRecord);
                return (
                  <BBRefTMSCustomer minWidth={400} disable={readOnly || !writeCap}
                    className={cssClass} appContext={appContext} pageContext={pageContext} tabIndex={tabIndex} autofocus={focus}
                    bean={roundUsed} beanIdField={'customerId'} beanLabelField={'customerFullName'} placeholder={'Customer'}
                    onPostUpdate={(_inputUI: React.Component, bean: any, _selectOpt: any, _userInput: string) => {
                      onInputChange(bean, field.name, null, bean[field.name])
                    }}
                  />
                )
              },
            },
            customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, focus: boolean) => {
              let roundUsed = dRecord.record;
              let cssClass = field.cssClass;
              if (field.computeCssClasses) cssClass = field.computeCssClasses(uiRoot.getVGridContext(), dRecord);
              return (
                <div className={`flex-hbox ${cssClass}`}>
                  <div className='flex-grow-1'>
                    {roundUsed[field.name]}
                  </div>
                  {!roundUsed['customerId'] && roundUsed['customerFullName'] ?
                    <bs.Button laf='link' className='text-warning'>
                      <FeatherIcon.AlertTriangle size={12} />
                    </bs.Button>
                    : null
                  }
                </div>
              )
            }
          },
          {
            name: 'date', label: 'Date', cssClass: 'flex-grow-1 text-end', container: 'fixed-left', width: 150,
            sortable: true, filterableType: 'date', filterable: true, format: util.text.formater.compactDate,
            editor: {
              type: 'date',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let field = fieldCtx.fieldConfig;
                let dRecord = fieldCtx.displayRecord;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let roundUsed = dRecord.record;
                let disable = !roundUsed['billOwner'] && roundUsed['id'];;
                let cssClass = field.cssClass;
                if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiRoot.getVGridContext(), dRecord)}`;
                return (
                  <input.BBDateInputMask className={cssClass} disabled={disable}
                    bean={roundUsed} field={field.name} tabIndex={tabIndex} focus={focus} format='DD/MM/YYYY'
                    onInputChange={onInputChange} />
                );
              },
            },
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row == cell.getRow()) {
                  cell.forceUpdate()
                }
              },
            },
          },
          {
            name: 'time', label: T('Time'), width: 60, container: 'fixed-left',
            filterableType: 'options', filterable: true,
            editor: {
              type: 'string',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let field = fieldCtx.fieldConfig;
                let dRecord = fieldCtx.displayRecord;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let roundUsed = dRecord.record;
                let disable = !roundUsed['billOwner'] && roundUsed['id'];
                let cssClass = field.cssClass;
                if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiRoot.getVGridContext(), dRecord)}`;
                return (
                  <input.BBTimeInputMask
                    className={cssClass} bean={roundUsed} field={field.name} tabIndex={tabIndex} focus={focus}
                    onInputChange={(bean: any, field: string, oldVal: any, newVal: any) => {
                      if (!newVal) newVal = '00:00';
                      let date = util.TimeUtil.parseCompactDateTimeFormat(bean['date']);
                      let sDate = util.TimeUtil.toCompactDateTimeFormat(date);
                      let ddmmyyyy = sDate.substring(0, sDate.indexOf('@') + 1);
                      let timeZone = sDate.substring(sDate.indexOf('+'), sDate.length);
                      bean['date'] = `${ddmmyyyy}${newVal}:00${timeZone}`;
                      onInputChange(bean, field, oldVal, newVal);
                    }}
                    disabled={disable} />
                );
              },
            },
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row == cell.getRow()) {
                  cell.forceUpdate()
                }
              },
            },
          },
          {
            name: 'containerType', label: `20'/40'`, width: 100, cssClass: 'px-1', filterableType: 'options', filterable: true,
            editor: {
              type: 'string',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let field = fieldCtx.fieldConfig;
                let dRecord = fieldCtx.displayRecord;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let roundUsed = dRecord.record;
                let disable = !roundUsed['billOwner'] && roundUsed['id'];;
                let cssClass = field.cssClass;
                if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiRoot.getVGridContext(), dRecord)}`;
                return (
                  <BBRefTMSContainerType minWidth={400}
                    className={cssClass} appContext={appContext} pageContext={pageContext} tabIndex={tabIndex} autofocus={focus}
                    bean={roundUsed} beanLabelField={'containerType'} placeholder={'Container Type'}
                    onPostUpdate={(_inputUI: React.Component, bean: any, selectOpt: any, _userInput: string) => {
                      onInputChange(bean, field.name, null, selectOpt['type'])
                    }}
                  />
                )
              },
            }
          },
          {
            name: 'containerNo', label: T('Container No'), width: 150,
            customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let containerNo = record[field.name];
              let width = field.width ? field.width : 120;
              let cssClass = field.cssClass;
              if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(ctx, dRecord)}`;
              return (
                <div className={`flex-hbox`}>
                  <bs.Tooltip className={`flex-grow-1 text-truncate ${cssClass}`} style={{ width: width - 25 }} tooltip={containerNo}>
                    {containerNo}
                  </bs.Tooltip>
                  {this.containerValidate(containerNo)}
                </div>
              )
            },
            editor: {
              type: 'string', onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let field = fieldCtx.fieldConfig;
                let dRecord = fieldCtx.displayRecord;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let roundUsed = dRecord.record;
                let disable = !roundUsed['billOwner'] && roundUsed['purpose'] === 'I' && roundUsed['id'];
                let containerNo = roundUsed[field.name];
                let tmsContainerNo = roundUsed["tmsContainerNo"];
                let cssClass = field.cssClass;
                if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiRoot.getVGridContext(), dRecord)}`;
                if (!containerNo && tmsContainerNo) containerNo = tmsContainerNo;
                return (
                  <div className={`flex-hbox`}>
                    <input.BBStringField className={cssClass} disable={disable}
                      bean={roundUsed} field={field.name} tabIndex={tabIndex} focus={focus} onInputChange={onInputChange} />
                    {uiRoot.containerValidate(containerNo)}
                  </div>
                )
              },
            }
          },
          {
            name: 'sealNo', label: T('Seal No'), width: 130,
            editor: {
              type: 'string', onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let field = fieldCtx.fieldConfig;
                let dRecord = fieldCtx.displayRecord;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let roundUsed = dRecord.record;
                let disable = !roundUsed['billOwner'] && roundUsed['purpose'] === 'I' && roundUsed['id'];
                let cssClass = field.cssClass;
                if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiRoot.getVGridContext(), dRecord)}`;
                let sealNo = roundUsed[field.name];
                let tmsSealNo = roundUsed["tmsSealNo"];
                if (!sealNo && tmsSealNo) sealNo = tmsSealNo;
                return (
                  <input.BBStringField tabIndex={tabIndex} focus={focus}
                    disable={disable} className={cssClass} field={field.name} bean={roundUsed} onInputChange={onInputChange} />
                );
              },
            },
          },
          {
            name: 'purpose', label: `IMP/EXP`,
            filterableType: 'Options', filterable: true,
            computeCssClasses: (ctx, dRecord) => {
              let roundUsed = dRecord.record;
              if (roundUsed['purpose'] === 'E') return 'px-2 text-warning';
              return 'px-2 text-success';
            },
            editor: {
              type: 'string', onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let field = fieldCtx.fieldConfig;
                let dRecord = fieldCtx.displayRecord;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let roundUsed = dRecord.record;
                let cssClass = field.cssClass;
                if (field.computeCssClasses) cssClass = field.computeCssClasses(uiRoot.getVGridContext(), dRecord);
                return (
                  <input.BBSelectField tabIndex={tabIndex} focus={focus} className={cssClass}
                    bean={roundUsed} field={field.name} options={['I', 'E']}
                    onInputChange={onInputChange} />
                )
              },
            }
          },
          {
            name: 'type', label: 'Loại Hình',
            filterableType: 'Options', filterable: true,
            editor: {
              type: 'string', onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let field = fieldCtx.fieldConfig;
                let dRecord = fieldCtx.displayRecord;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let forwarder = dRecord.record;
                let cssClass = field.cssClass;
                if (field.computeCssClasses) cssClass = field.computeCssClasses(uiRoot.getVGridContext(), dRecord);
                return (
                  <input.BBSelectField tabIndex={tabIndex} focus={focus} className={cssClass}
                    bean={forwarder} field={field.name} options={[TMSRoundUsedType.CK, TMSRoundUsedType.D, TMSRoundUsedType.KEP, TMSRoundUsedType.RU]}
                    onInputChange={onInputChange} />
                )
              },
            }
          },
          {
            name: 'carrierFullName', label: T('Carrier'), width: 200,
            editor: {
              type: 'string',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let field = fieldCtx.fieldConfig;
                let dRecord = fieldCtx.displayRecord;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let roundUsed = dRecord.record;
                let cssClass = field.cssClass;
                if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiRoot.getVGridContext(), dRecord)}`;
                return (
                  <BBRefTMSCarrier minWidth={400} disable={!writeCap} allowUserInput
                    className={cssClass} appContext={appContext} pageContext={pageContext} tabIndex={tabIndex} autofocus={focus}
                    bean={roundUsed} beanIdField={'carrierId'} beanLabelField={'carrierFullName'} placeholder={'Carrier'}
                    onPostUpdate={(_inputUI: React.Component, bean: any, _selectOpt: any, _userInput: string) => {
                      onInputChange(bean, field.name, null, bean[field.name])
                    }}
                  />
                )
              },
            }
          },
          {
            name: 'grossWeight', label: 'GW (T)', width: 80,
            editor: {
              type: 'double', onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let field = fieldCtx.fieldConfig;
                let dRecord = fieldCtx.displayRecord;
                let roundUsed = dRecord.record;
                let disable = !roundUsed['billOwner'] && roundUsed['id'];;
                return (
                  <input.BBNumberField disabled={disable} className={field.cssClass} field={field.name} bean={roundUsed} onInputChange={onInputChange} />
                );
              },
            },
          },
          {
            name: 'deliveryContact', label: T('Contact'), hint: T('Contact'), cssClass: 'px-1', width: 170, dataTooltip: true,
            sortable: true,
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row == cell.getRow() && event.field.name === 'mode') {
                  cell.forceUpdate()
                }
              },
            },
            customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, focus: boolean) => {
              let roundUsed = dRecord.record;
              let cssClass = field.cssClass;
              if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(ctx, dRecord)}`;
              let html = <bs.Tooltip className={cssClass} tooltip={roundUsed[field.name]}>{roundUsed[field.name]}</bs.Tooltip>
              return html;
            },
            editor: {
              type: 'string',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let field = fieldCtx.fieldConfig;
                let dRecord = fieldCtx.displayRecord;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let roundUsed = dRecord.record;
                let cssClass = field.cssClass;
                let disable = !roundUsed['billOwner'] && roundUsed['id'];;
                if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiRoot.getVGridContext(), dRecord)}`;
                let html = (
                  disable ?
                    <div>{roundUsed[field.name]}</div> :
                    <input.BBStringField tabIndex={tabIndex} focus={focus}
                      className={cssClass} field={field.name} bean={roundUsed}
                      onInputChange={onInputChange} />
                )
                return TMSUtils.renderTMSGridTooltip(html, roundUsed[field.name], field.width, dRecord.row);
              },
            },
          },
          {
            name: 'deliveryAddress', label: 'Địa Điểm', width: 300, cssClass: 'justify-content-end px-2',
            editor: {
              type: 'string', onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let field = fieldCtx.fieldConfig;
                let dRecord = fieldCtx.displayRecord;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let roundUsed = dRecord.record;
                let disable = !roundUsed['billOwner'] && roundUsed['id'];
                let html = (disable ?
                  <div className={`flex-hbox justify-content-end px-2`}>{roundUsed[field.name]}</div> :
                  <input.BBStringField tabIndex={tabIndex} focus={focus}
                    className={'flex-hbox justify-content-end'} field={field.name} bean={roundUsed}
                    onInputChange={onInputChange} />
                )
                return TMSUtils.renderTMSGridTooltip(html, roundUsed[field.name], field.width, dRecord.row);
              },
            },
          },
          {
            name: 'deliveryStateLabel', label: 'Tỉnh Thành', width: 150, cssClass: 'px-1 text-success',
            editor: {
              type: 'string',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let field = fieldCtx.fieldConfig;
                let dRecord = fieldCtx.displayRecord;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let roundUsed = dRecord.record;
                let cssClass = field.cssClass;
                if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiRoot.getVGridContext(), dRecord)}`;
                if (!roundUsed['deliveryStateLabel']) roundUsed['deliveryStateLabel'] = roundUsed['locDeliveryStateLabel'];
                if (!roundUsed['deliveryStateId']) roundUsed['deliveryStateId'] = roundUsed['locDeliveryStateId'];
                return (
                  <BBRefState
                    countryId={244}
                    appContext={appContext} pageContext={pageContext} bean={roundUsed} tabIndex={tabIndex} autofocus={focus}
                    beanIdField='deliveryStateId' beanLabelField='deliveryStateLabel' placeholder='Tỉnh Thành' minWidth={400}
                    onPostUpdate={(_inputUI: React.Component, bean: any, _selectOpt: any, _userInput: string) => {
                      onInputChange(roundUsed, field.name, null, bean[field.name])
                    }}
                  />
                )
              },
            }
          },
          {
            name: 'bookingBill', label: 'Booking/Bill', width: 150,
            editor: {
              type: 'string', onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let field = fieldCtx.fieldConfig;
                let dRecord = fieldCtx.displayRecord;
                let roundUsed = dRecord.record;
                let disable = !roundUsed['billOwner'] && roundUsed['id'];;
                let cssClass = field.cssClass;
                if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiRoot.getVGridContext(), dRecord)}`;
                return (
                  <input.BBStringField disable={disable} className={cssClass} field={field.name} bean={roundUsed} onInputChange={onInputChange} />
                );
              },
            },
          },
          // {
          //   name: 'warehouseLocationId', label: 'CY', width: 150,
          //   editor: {
          //     type: 'string', onInputChange: onInputChange,
          //     renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
          //       let field = fieldCtx.fieldConfig;
          //       let dRecord = fieldCtx.displayRecord;
          //       let roundUsed = dRecord.record;
          //       let disable = !roundUsed['billOwner'] && roundUsed['id'];;
          //       let cssClass = field.cssClass;
          //       if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiRoot.getVGridContext(), dRecord)}`;
          //       let warehouseLocationId = roundUsed['warehouseLocationId'];
          //       return (
          //         <BBRefLocation
          //           className={`flex-grow-1 ${cssClass}`} autofocus={fieldCtx.focus} tabIndex={fieldCtx.tabIndex} locationTags={['app:tms']} minWidth={500}
          //           appContext={appContext} pageContext={pageContext} bean={roundUsed} disable={disable} refLocationBy='id'
          //           beanIdField={'warehouseLocationId'} beanLabelField={'warehouseLocationLabel'} placeholder='Full Return WH'
          //           onPostUpdate={(_inputUI: React.Component, bean: any, _selectOpt: any, _userInput: string) => {
          //             onInputChange(bean, field.name, warehouseLocationId, bean['warehouseLocationId']);
          //           }} />
          //       );
          //     },
          //   },
          // },
          // {
          //   name: 'etaCutOffTime', label: T('ETA/Cut Of Time'), width: 150,
          //   editor: {
          //     type: 'string', onInputChange: onInputChange,
          //     renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
          //       let field = fieldCtx.fieldConfig;
          //       let dRecord = fieldCtx.displayRecord;
          //       let roundUsed = dRecord.record;
          //       let disable = !roundUsed['billOwner'] && roundUsed['id'];;
          //       let cssClass = field.cssClass;
          //       if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiRoot.getVGridContext(), dRecord)}`;
          //       return (
          //         <input.BBStringField disable={disable} className={cssClass} field={field.name} bean={roundUsed} onInputChange={onInputChange} />
          //       );
          //     },
          //   },
          // },
          {
            name: 'note', label: 'Notes', width: 200, dataTooltip: true, cssClass: 'px-1 text-danger',
            editor: {
              type: 'string', onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let field = fieldCtx.fieldConfig;
                let dRecord = fieldCtx.displayRecord;
                let tabIndex = fieldCtx.tabIndex;
                let roundUsed = dRecord.record;
                let cssClass = field.cssClass;
                if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiRoot.getVGridContext(), dRecord)}`;
                return (
                  <div className='flex-hbox'>
                    <input.BBStringField className={cssClass} tabIndex={tabIndex} bean={roundUsed} field={field.name} onInputChange={onInputChange} />
                    {UITMSBillUtils.renderButtonInfo('Note', roundUsed[field.name])}
                  </div>
                );
              },
            }
          },
          {
            name: 'office', label: T('Office'), width: 80, hint: 'Office', filterableType: 'Options', filterable: true,
            editor: {
              type: 'string', onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let field = fieldCtx.fieldConfig;
                let dRecord = fieldCtx.displayRecord;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let roundUsed = dRecord.record;
                let disable = !roundUsed['billOwner'] && roundUsed['id'];;
                let cssClass = field.cssClass;
                if (field.computeCssClasses) cssClass = field.computeCssClasses(uiRoot.getVGridContext(), dRecord);
                return (
                  <input.BBOptionAutoComplete disable={disable} tabIndex={tabIndex} autofocus={focus} className={cssClass} allowUserInput
                    bean={roundUsed} field={field.name} options={['BEEHPH', 'BEEHAN', 'BEEHCM', 'BEEDAD', 'BEELS', 'BEEND', 'BEETH', 'BEEHN']}
                    onInputChange={onInputChange} />
                )
              },
            }
          },
          {
            name: 'vendorFullName', label: 'Vendor', width: 200, cssClass: 'px-1', filterableType: 'options', filterable: true,
            computeCssClasses(ctx, dRecord) {
              let vendorId = dRecord.getValue('vendorId');
              if (!vendorId) return 'text-warning'
              return '';
            },
            editor: {
              type: 'string',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let field = fieldCtx.fieldConfig;
                let dRecord = fieldCtx.displayRecord;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let roundUsed = dRecord.record;
                let cssClass = field.cssClass;
                if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiRoot.getVGridContext(), dRecord)}`;
                return (
                  <BBRefVehicleFleet minWidth={400} disable={!writeCap} allowUserInput
                    className={cssClass} appContext={appContext} pageContext={pageContext} tabIndex={tabIndex} autofocus={focus}
                    bean={roundUsed} beanIdField={'vendorId'} beanLabelField={'vendorFullName'} placeholder={'Vendor'}
                    onPostUpdate={(_inputUI: React.Component, bean: any, _selectOpt: any, _userInput: string) => {
                      onInputChange(bean, field.name, null, bean[field.name])
                    }}
                  />
                )
              },
            }
          },
          {
            name: 'vehicleLabel', label: T('License plate'), hint: 'Biển Số', filterableType: 'Options', filterable: true,
            editor: {
              type: 'string',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let field = fieldCtx.fieldConfig;
                let dRecord = fieldCtx.displayRecord;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let roundUsed = dRecord.record;
                const oldVal = roundUsed[field.name];
                return (
                  <BBRefVehicle appContext={appContext} pageContext={pageContext} placeholder='Vehicle'
                    tabIndex={tabIndex} autofocus={focus} allowUserInput minWidth={500}
                    placement="bottom-start" disable={!writeCap}
                    bean={roundUsed} beanIdField={'vehicleId'} beanLabelField={'vehicleLabel'}
                    onPostUpdate={(inputUI: React.Component, bean: any, selectOpt: any, userInput: string) => {
                      onInputChange(bean, field.name, oldVal, bean[field.name])
                    }}
                  />
                )
              }
            }
          },
          {
            name: 'vehicleDriverFullName', label: T('Driver'), hint: 'Lái Xe', width: 175,
            editor: {
              type: 'string',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let field = fieldCtx.fieldConfig;
                let dRecord = fieldCtx.displayRecord;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let roundUsed = dRecord.record;
                const oldDriver = roundUsed[field.name];
                return (
                  <BBRefTransporter
                    appContext={appContext} pageContext={pageContext}
                    minWidth={300}
                    placement="left"
                    placeholder="Enter Transporter"
                    bean={roundUsed} beanIdField={'vehicleDriverId'}
                    tabIndex={tabIndex} autofocus={focus}
                    beanLabelField={field.name} refTransporterBy={'id'} onPostUpdate={(inputUI, bean, selectOp, userInput) => {
                      if (selectOp) {
                        roundUsed['vehicleDriverMobile'] = selectOp['mobile'];
                        roundUsed['vehicleDriverIdentificationNo'] = selectOp['idCard'];
                      }
                      onInputChange(bean, field.name, oldDriver, bean[field.name])
                    }} />
                )
              }
            }
          },
          { name: 'vehicleDriverMobile', label: T('Mobile'), width: 150, editor: { type: 'string', onInputChange: onInputChange } },
          { name: 'vehicleDriverIdentificationNo', label: T('ID'), width: 150, editor: { type: 'string', onInputChange: onInputChange } },
          {
            name: 'costTruckingTruck', label: 'Truck',
            editor: { type: 'currency', onInputChange: onInputChange }
          },
          {
            name: 'costTruckingLiftOn', label: 'Nâng',
            editor: { type: 'currency', onInputChange: onInputChange }
          },
          {
            name: 'costTruckingLiftOff', label: 'Hạ',
            editor: { type: 'currency', onInputChange: onInputChange }
          },
          {
            name: 'costTruckingExtra', label: 'Phát Sinh Khác',
            editor: { type: 'currency', onInputChange: onInputChange }
          },
          {
            name: 'costRuTruck', label: 'Truck',
            editor: { type: 'currency', onInputChange: onInputChange }
          },
          {
            name: 'costRuCombine', label: 'Kết Hợp',
            editor: { type: 'currency', onInputChange: onInputChange }
          },
          {
            name: 'costRuLiftOn', label: 'Nâng',
            editor: { type: 'currency', onInputChange: onInputChange }
          },
          {
            name: 'costRuLiftOff', label: 'Hạ',
            editor: { type: 'currency', onInputChange: onInputChange }
          },
          {
            name: 'costRuProfit', label: 'Lợi Nhuận',
            editor: { type: 'currency', onInputChange: onInputChange }
          },
          {
            name: 'find-tms-bill', label: '', width: 50, container: 'fixed-right',
            customRender: (_ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              if (record['purpose'] == 'I') return;
              return (
                <div className={'flex-hbox justify-content-center'}>
                  <WButtonPopupUIFindTMSBillList context={_ctx} dRecord={dRecord} />
                  {dRecord.getValue('ruTmsBillId') ?
                    <bs.Button className='mx-1' laf='link' onClick={() => {
                      bs.dialogConfirmMessage('Unlink RU TMSBill', T('You want unlink TMS Bill!!!'),
                        () => {
                          appContext
                            .createHttpBackendCall('TMSRoundUsedService', 'ruUnlinkTMSBill', { ruId: record['id'] })
                            .withSuccessData(data => {
                              record['ruTmsBillId'] = null;
                              record['containerNo'] = null;
                              record['sealNo'] = null;
                              appContext.addOSNotification('success', 'Unlink Success!!!')
                              _ctx.getVGrid().forceUpdateView();
                            }).call();
                        }
                      )
                    }}>
                      <FeatherIcon.Link size={12} />
                    </bs.Button>
                    : null
                  }
                </div>
              )
            }
          },
          {
            name: 'status', label: T('Status'), container: 'fixed-right', width: 170,
            customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let status = record.status;
              let val = status;
              if (status === TMSRoundUsedStatus.SendingFromRu) val = 'Sending'
              if (status === TMSRoundUsedStatus.SendingFromBill) val = 'Sending From Bill'
              let cssTextColor = TMSRoundUsedStatusTools.getTextColor(record.status);
              return (
                <div className={'flex-hbox'}>
                  <div style={{ width: 135 }} className={`fw-bold ${cssTextColor}`}>
                    {val}
                  </div>
                  <div className='flex-hbox-grow-0 align-items-center'>
                    {this.renderWorkingStepButton(record)}
                  </div>
                </div>
              )
            },
            editor: {
              type: 'string',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let field = fieldCtx.fieldConfig;
                let dRecord = fieldCtx.displayRecord;
                let record = dRecord.record;
                let cssClass = '';
                let cssTextColor = TMSRoundUsedStatusTools.getTextColor(record.status);
                if (field.computeCssClasses) cssClass = `fw-bold ${cssClass} ${field.computeCssClasses(uiRoot.getVGridContext(), dRecord)} ${cssTextColor}`;
                return (
                  <div className={'flex-hbox'}>
                    <div style={{ width: 135 }}>
                      {uiRoot.renderRoundUsedStatus(record)}
                    </div>
                    <div className='flex-hbox-grow-0 align-items-center'>
                      {uiRoot.renderWorkingStepButton(record)}
                    </div>
                  </div>
                )
              },
            }
          },
        ],
        fieldGroups: {
          "reused": {
            label: T('Reused'),
            visible: true,
            fields: [
              'fileRu', 'fileTruck'
            ]
          },
          "time": {
            label: T('Time'),
            visible: true,
            fields: [
              'date', 'time'
            ]
          },
          "container": {
            label: T('Container'),
            visible: true,
            fields: [
              'containerType', 'purpose', 'type', 'carrierFullName', 'grossWeight'
            ]
          },
          "warehouse": {
            label: T('Loading/Unloading Warehouse'),
            visible: true,
            fields: [
              'deliveryAddress', 'deliveryStateLabel'
            ]
          },
          "cosTruck": {
            label: T('COS TRUCKING'),
            visible: true,
            fields: [
              'costTruckingTruck', 'costTruckingLiftOn', 'costTruckingLiftOff', 'costTruckingExtra'
            ]
          },
          "cosRu": {
            label: T('COS RU'),
            visible: true,
            fields: [
              'costRuTruck', 'costRuCombine', 'costRuLiftOn', 'costRuLiftOff', 'costRuProfit'
            ]
          },
        }
      },
      toolbar: {
        actions: [
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_ADD(!writeCap, T('Add')),
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION(!writeCap, {
            name: 'print-receipt-of-delivery', label: T('Print POD'), hint: T('Print Receipt Of Delivery'),
            icon: FeatherIcon.Printer,
            onClick: (context: grid.VGridContext) => { this.onPrintReceiptOfDelivery(context) }
          }),
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_DELETE(!modCap, T('Del')),
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_STORAGE_STATES([entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED], !writeCap),
          module.settings.createVGridFavButton(configId),
        ],
        filters: entity.DbEntityListConfigTool.TOOLBAR_FILTERS(true, 'filter'),
        filterActions: [
          ...entity.DbEntityListConfigTool.TOOLBAR_AUTO_REFRESH('auto-refresh-round-used', T('Refresh')),
        ],
      },
      footer: {
        page: {
          hide: type !== 'page',
          render: (ctx: grid.VGridContext) => {
            return <UITMSRoundUsedListPageControl context={ctx} />
          }
        },
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table',
          },
          aggregation: {
            viewMode: 'aggregation',
            treeWidth: 150,
            createAggregationModel(_ctx: grid.VGridContext) {
              let sumAggregation = (label: string, enable: boolean = false) => {
                return new grid.SumAggregationFunction(
                  label,
                  ['costTruckingTruck', 'costTruckingLiftOn', 'costTruckingLiftOff', 'costTruckingExtra',
                    'costRuTruck', 'costRuCombine', 'costRuLiftOn', 'costRuLiftOff', 'costRuProfit'],
                  enable
                );
              }
              let model = new grid.AggregationDisplayModel(T('All'), false);
              model.addAggregation(
                new grid.DateValueAggregation(T("Date"), "date", "YYYY/MM/DD", true)
                  .withSortBucket('desc')
                  .withAggFunction(sumAggregation('Total', true)));
              model.addAggregation(
                new grid.ValueAggregation(T("Customer"), "customerFullName", false)
                  .withSortBucket('asc')
                  .withAggFunction(sumAggregation('Total')));
              return model;
            },
          }
        }
      }
    }

    let fields = config.record.fields;
    let computeCssClassesModifyBean = (_ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
      if (dRecord.isDataRecord()) {
        let state = dRecord.getRecordState();
        if (state.isMarkModified()) return 'fw-bolder fst-italic';
      }
      return '';
    }
    for (let sel of fields) {
      if (sel.name.startsWith("_")) continue;
      if (sel.listener) {
        if (!sel.listener.onDataCellEvent) {
          sel.listener.onDataCellEvent = (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
            if (event.row == cell.getRow()) {
              cell.forceUpdate()
            }
          }
        }
      } else {
        sel.listener = {
          onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
            if (event.row == cell.getRow()) {
              cell.forceUpdate()
            }
          },
        }
      }

      if (sel.computeCssClasses) {
        let cssClass = sel.computeCssClasses;
        sel.computeCssClasses = (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
          return `${cssClass(ctx, dRecord)} ${computeCssClassesModifyBean(ctx, dRecord)}`;
        }
      } else {
        sel.computeCssClasses = (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
          return computeCssClassesModifyBean(ctx, dRecord);
        }
      }
    }
    return config;
  }

  containerValidate = (val: any) => {
    try {
      CONTAINER_NUMBER_VALIDATOR.validate(val);
      return null;
    } catch (error) {
      return (
        <div className='flex-grow-0' >
          <FeatherIcon.XCircle size={12} color='red' />
        </div>
      );
    }
  }

  onPrintReceiptOfDelivery(ctx: grid.VGridContext) {
    let uiRoot = ctx.uiRoot as UITMSRoundUsedList;
    let { pageContext, plugin } = uiRoot.props;
    let records = plugin.getListModel().getSelectedRecords();
    let tmsBillIds: Array<any> = [];
    for (let record of records) {
      tmsBillIds.push(record.tmsBillId)
    }
    let newRecords = plugin.getListModel().getRecords().find((sel: any) => grid.getRecordState(sel).isMarkNew());
    if (newRecords) {
      bs.notificationShow("danger", "You need to save changes");
      return;
    }
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <UITMSReceiptOfDeliveryPrint key={`print-${util.IDTracker.next()}`}
          appContext={appCtx} pageContext={pageCtx} billIds={tmsBillIds} />
      )
    }
    pageContext.createPopupPage('receipt-of-delivery', T('Receipt Of Delivery'), createAppPage, { size: 'xl' });
  }

  onNewAction(): void {
    let { appContext, plugin } = this.props

    appContext.createHttpBackendCall('TMSRoundUsedService', 'newTMSRoundUsedModel', { roundUsedModel: {} })
      .withSuccessData((data: any) => {
        let newRecord = data;
        plugin.getRecords().unshift(newRecord);
        grid.initRecordStates([newRecord]);
        plugin.getListModel().filter();

        let state = grid.getRecordState(newRecord);
        state.markModified();
        this.getVGridContext().getVGrid().forceUpdateView();
      })
      .call();
  }
}

export class UITMSRoundUsedJobTrackingList extends app.AppComponent {
  listConfig: JobTrackingListConfig;
  constructor(props: entity.DbEntityListProps) {
    super(props);
    let { appContext } = this.props;
    this.markLoading(true);
    appContext.createHttpBackendCall('JobTrackingService', 'getJobTrackingProjectByCode', { code: 'tms-round-used' })
      .withSuccessData((data: any) => {
        let jobTrackingProject = data;
        if (jobTrackingProject) {
          this.listConfig = new JobTrackingListConfig(jobTrackingProject, app.host.DATATP_SESSION.getAccountId());
        }
        this.markLoading(false);
        this.forceUpdate();
      })
      .call();
  }

  render(): React.ReactNode {
    if (this.isLoading()) return;
    let { appContext, pageContext } = this.props;
    if (this.listConfig) {
      return (
        <UITMSRoundUsedList type='page' jobTrackingListConfig={this.listConfig}
          plugin={new UITMSRoundUsedListPlugin().withDeliveryPlan(26)}
          appContext={appContext} pageContext={pageContext} />
      )
    }
    return (
      <UITMSRoundUsedList type='page'
        plugin={new UITMSRoundUsedListPlugin().withDeliveryPlan(26)}
        appContext={appContext} pageContext={pageContext} />
    )
  }
}
