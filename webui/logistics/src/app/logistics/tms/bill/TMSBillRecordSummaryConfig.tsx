import React from 'react';
import * as FeatherIcon from 'react-feather'
import { util, bs, grid, entity, input } from '@datatp-ui/lib';
import { T } from '../backend';
import {
  TMSBillTransportationModeTools,
} from '../utils';
import { UITMSBillUtils } from './UITMSBillUtils';
import { WIconPopupTMSJobTrackingIssue } from '../issue/UITMSJobTrackingIssue';
import { renderOpsStatus, renderRoundUsedStatus, renderTrackingButton } from './TMSBillRecordConfig';
import { renderBtnTrackingAPI } from 'app/logistics/vendor/UITMSVendorBillList';

const compactDate = (val: string) => {
  if (!val) return;
  return util.text.formater.compactDate(val);
}

export class WTMSGridInsertRow extends grid.WGridInsertRow {
  override onInsert = () => {
    let { context, row, createInsertRecord, allowInsert } = this.props;
    let dRecord = context.model.getDisplayRecordList().getDisplayRecordAt(row);

    let insert = true;
    if (allowInsert) insert = allowInsert(context, dRecord);

    if (insert) {
      let newRecord = createInsertRecord(context, dRecord)
      context.model.insertDisplayRecordAt(row, newRecord);
      let state = grid.getRecordState(newRecord);
      state.markModified();
      context.getVGrid().forceUpdateView(true);
    }
  }

  render() {
    let { context, row, className, style, allowInsert } = this.props;
    let disabled = false;
    if (allowInsert) {
      let dRecord = context.model.getDisplayRecordList().getDisplayRecordAt(row);
      disabled = !allowInsert(context, dRecord);
    }
    return (
      <bs.Button laf='info' className={`p-1 m-1 ${className}`} disabled={disabled} style={style} onClick={this.onInsert}>
        <FeatherIcon.Plus size={16} />
      </bs.Button>
    );
  }
}

function bntCloneBill(ctx: grid.VGridContext, dRecord: grid.DisplayRecord) {
  let create = (_ctx: grid.VGridContext, atRecord: grid.DisplayRecord) => {
    return UITMSBillUtils.cloneBill(atRecord.record);
  }
  let allowInsert = (_ctx: grid.VGridContext, atRecord: grid.DisplayRecord) => {
    let recState = atRecord.getRecordState(false);
    if (!recState) return false;
    if (recState.isMarkDeleted()) {
      return false;
    }
    return true;
  }
  return (<WTMSGridInsertRow key={'add'} color='link' context={ctx} row={dRecord.row}
    createInsertRecord={create} allowInsert={allowInsert} />);
}

function renderActions(ctx: grid.VGridContext, dRecord: grid.DisplayRecord) {
  let uiRoot = ctx.uiRoot as entity.DbEntityList;
  let { appContext, pageContext } = uiRoot.props;
  let record = dRecord.record;
  let issueTemplate = {
    'refEntityId': record['id'],
    'refEntity': 'lgc_tms_bill',
    'label': record['label'],
    'label2': record['customerFullName'],
    'resolverAccountId': record['responsibleAccountId'],
    'resolverAccountFullName': record['responsibleFullName'],
    'status': 'CONFIRMED',
  }
  if (record['jobTrackingId']) {
    issueTemplate['refEntity'] = 'lgc_job_tracking';
    issueTemplate['refEntityId'] = record['jobTrackingId'];
  }
  let billTemplate = {
    tmsBillId: record['id'],
    billIssueTotal: record['billIssueTotal'],
    jobTrackingIssueTotal: record['jobTrackingIssueTotal']
  }
  return (
    <div className={'flex-hbox'}>
      <WIconPopupTMSJobTrackingIssue
        appContext={appContext} pageContext={pageContext}
        template={issueTemplate} billTemplate={billTemplate}
        className='mx-2'
        btnSize={
          {
            fontSize: 15,
            width: 30,
            height: 25,
          }
        }
        onUpdateIssueTotal={(val) => {
          record['billIssueTotal'] = record['billIssueTotal'] + val;
          ctx.getVGrid().forceUpdateView();
        }}
      />
      {renderRoundUsedStatus(ctx, record, { iconSize: 20, fontSize: 15, cssClass: 'mx-2' })}
      {renderOpsStatus(record, { iconSize: 25, fontSize: 15, cssClass: 'mx-2' })}
      {renderTrackingButton(ctx, record, { btnSize: 30, iconSize: 20, cssClass: 'mx-2' })}
      {renderBtnTrackingAPI(ctx, record, { iconSize: 16, btnShowBorder: true, cssClass: 'm-1 p-1' })}
    </div>
  )
}

function deleteBill(ctx: grid.VGridContext, dRecord: grid.DisplayRecord) {
  let bill = dRecord.record;
  if (dRecord.getRecordState().isMarkDeleted()) {
    ctx.model.getDisplayRecordList().toggleDeletedDisplayRecord(dRecord.row);
    delete bill['editState'];
    ctx.getVGrid().forceUpdateView();
    return;
  }
  const onConfirm = () => {
    ctx.model.getDisplayRecordList().toggleDeletedDisplayRecord(dRecord.row);
    if (dRecord.getRecordState().isMarkDeleted()) {
      bill['editState'] = 'DELETED';
    } else {
      delete bill['editState'];
    }
    ctx.getVGrid().forceUpdateView();
  }
  bs.dialogConfirmMessage('Delete Confirm', 'Are you sure delete???', onConfirm);
}

export function renderSummary(ctx: grid.VGridContext, dRecord: grid.DisplayRecord) {
  if (!dRecord.isDataRecord()) return;
  let _state = dRecord.getRecordState();
  let uiRoot = ctx.uiRoot as entity.DbEntityList;
  let tmsBill = dRecord.record;
  let modifiedCssColor = dRecord.getRecordState().isMarkModified() ? 'fw-bolder fst-italic' : '';
  let mode = tmsBill['mode'];
  let truckNo = tmsBill.truckNo ? tmsBill.truckNo : '' + tmsBill.tmsTrackingTruckNo ? tmsBill.tmsTrackingTruckNo : ''
  let htmlContact = (<>
    <div className='flex-hbox'>
      <label className="fw-lighter pe-1">{T('Pickup')}: </label>
      <span className="text-info" onClick={() => {
        bs.dialogShow('Pickup', (<div>{tmsBill.senderAddress}</div>),)
      }}
      >{tmsBill.senderAddress}</span>
    </div>
    <div className='flex-hbox'>
      <label className="fw-lighter pe-1">{T('Delivery')}: </label>
      <span className="text-info" onClick={() => {
        bs.dialogShow('Delivery', (<div>{tmsBill.receiverAddress}</div>),)
      }}
      >{tmsBill.receiverAddress}</span>
    </div>
  </>)

  let style;
  let stateContent = null;
  if (_state.isMarkNew()) {
    style = { backgroundColor: 'rgb(217, 251, 208)' };
    stateContent = (
      <div style={{
        position: 'absolute', marginTop: 50, marginLeft: 100,
        fontSize: 30, color: 'orange', opacity: 0.2, fontWeight: 900, transform: 'rotate(25deg)'
      }}>
        NEW
      </div>
    )
  }
  if (_state.isSelected()) {
    stateContent = (
      <div style={{
        position: 'absolute', marginTop: 50, marginLeft: 100,
        fontSize: 30, color: 'blue', opacity: 0.2, fontWeight: 900, transform: 'rotate(25deg)'
      }}>
        SELECTED
      </div>
    )
  }
  if (_state.isMarkDeleted()) {
    style = { backgroundColor: '#FFCCCC' };
    stateContent = (
      <div style={{
        position: 'absolute', marginTop: 50, marginLeft: 100,
        fontSize: 30, color: 'red', opacity: 0.2, fontWeight: 900, transform: 'rotate(25deg)'
      }}>
        DELETE
      </div>
    )
  }

  let html = (
    <div style={style} className={`flex-vbox ${_state.isSelected() ? 'cell-selected' : ''}`}>
      {stateContent}
      <grid.SummaryCell className='flex-hbox' context={ctx} record={dRecord}>
        <div className={'flex-vbox'}>
          <div className='flex-hbox align-items-center'>
            <label className="fw-lighter pe-1">{T('File')}: </label>
            <span className={`px-1 text-info ${modifiedCssColor}`}>{tmsBill.label}</span>
            |
            <span className={`px-1 ${TMSBillTransportationModeTools.getColor(mode)}`}>
              {TMSBillTransportationModeTools.getLabel(mode)}
            </span>
          </div>
          <div className='flex-hbox'>
            <label className="fw-lighter pe-1">{T('Customer')}: </label>
            <span className="text-info">{tmsBill.customerFullName}</span>
          </div>
          <div className='flex-hbox'>
            <div style={{ width: 130 }} className='flex-hbox flex-grow-0 align-items-center'>
              <label className="fw-lighter pe-1">{T('Date')}: </label>
              <span className="text-info">{compactDate(tmsBill.dateTime)}</span>
            </div>
            <div style={{ width: 90 }} className='flex-hbox flex-grow-0 align-items-center'>
              <label className="fw-lighter pe-1">{T('Time')}: </label>
              <span className="text-info">
                <input.BBTimeInputMask
                  bean={tmsBill} field={'time'} disabled />
              </span>
            </div>
            <div style={{ width: 90 }} className='flex-hbox align-items-center'>
              <label className="fw-lighter pe-1">{T('E.Time')}: </label>
              <span className="text-info">
                <input.BBTimeInputMask bean={tmsBill} field={'estimateTime'} disabled />
              </span>
            </div>
          </div>
          <div className='flex-hbox'>
            <label className="fw-lighter pe-1">{T('Vendor')}: </label>
            <span className="text-info">{tmsBill.vendorFullName}</span>
          </div>
          <div className='flex-hbox'>
            <label className="fw-lighter pe-1">{T('Truck No')}: </label>
            <span className="text-info" onClick={() => {
              bs.dialogShow('Truck No', (<div>{truckNo}</div>),)
            }}>{truckNo}</span>
          </div>

          {htmlContact}
        </div>
      </grid.SummaryCell >
      <div className='flex-hbox'>
        <input.BBCheckboxField className='mx-1' style={{ width: 25, height: 25, marginTop: 5 }} disable={!tmsBill['id']}
          bean={_state} field={'selected'} value={false} onInputChange={() => uiRoot.forceUpdate()} />
        {bntCloneBill(ctx, dRecord)}
        <bs.Button className='p-1 m-1' laf='warning' onClick={() => uiRoot.onDefaultSelect(dRecord)} disabled={!tmsBill['id']}>
          <FeatherIcon.Edit size={16} />
        </bs.Button>
        <bs.Button laf='danger' className='p-1 m-1' onClick={() => deleteBill(ctx, dRecord)}>
          <FeatherIcon.Trash size={16} />
        </bs.Button>
        <hr className='m-1' style={{ height: 25, width: 0, borderLeft: '2px solid #000' }} />
        {renderActions(ctx, dRecord)}
      </div>
    </div>
  );
  return html;
}
