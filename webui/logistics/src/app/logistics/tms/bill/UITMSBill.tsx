import React from "react";
import * as FeatherIcon from 'react-feather';
import { bs, input, app, entity } from "@datatp-ui/lib";
import { module } from "@datatp-ui/erp";

import { T } from "../backend";

import {
  GoodsType,
  ResponsibilityType,
  TMSBillState,
  TMSBillTransportationMode,
  TMSBillType,
  TMSTransportType
} from "../models";
import { ManagementRestURL } from "../RestURL";
import { UITMSBillActivityList, UITMSBillActivityListPlugin, } from "./UITMSBillActivityList";
import { UITMSBillUtils } from "./UITMSBillUtils";
import { UITMSBillResponsibilityForm } from "./UITMSBillResponsibility";
import { UITMSBillResponsibilityList, UITMSBillResponsibilityListPlugin, } from "./UITMSBillResponsibilityList";
import { UITMSGoodItemListEditor } from "./UITMSGoodItemList";

import { UITMSBillFeeEditor } from "./UITMSBillFee";
import { TMSBillTransportationModeTools } from "../utils";

import { BBRefAccount } from "@datatp-ui/erp/dist/@types/module/account";
/*TODO: Chien clean */

// class UITMSBillTransportForm extends entity.AppDbEntityEditor {
//   render() {
//     let { observer, appContext, pageContext, onModify } = this.props;
//     const writeCap = pageContext.hasUserWriteCapability();
//     let bill = observer.getMutableBean();

//     return (
//       <div>
//         <bs.Row>
//           <bs.Col span={3}>
//             <input.BBStringField bean={bill} field="label" label={T("Label")} disable={!writeCap} />
//           </bs.Col>
//           <bs.Col span={3}>
//             <input.BBSelectField bean={bill} field="state" label={T("TMS Bill State")} disable={!writeCap}
//               options={[TMSBillState.NewBill, TMSBillState.Canceled, TMSBillState.CollectingGoods,
//               TMSBillState.CollectedGoods, TMSBillState.CollectGoodsFail, TMSBillState.TransportHub,
//               TMSBillState.Transport, TMSBillState.Delivering, TMSBillState.Delivered, TMSBillState.DeliverFail,
//               TMSBillState.ReturningGoods, TMSBillState.ReturnedGoods, TMSBillState.ReturnGoodsFail]} />
//           </bs.Col>
//           <bs.Col span={6}>
//             <bs.FormLabel className="form-label">{T('Type')}</bs.FormLabel>
//             <input.BBSelectField bean={bill} field={'type'}
//               options={[TMSBillType.IMS, TMSBillType.FORWARDER]} onInputChange={onModify} disable={!writeCap} />
//           </bs.Col>
//         </bs.Row>
//         <bs.Row>
//           <bs.Col span={6}>
//             <input.BBDateTimeField bean={bill} field="openedDate" label={T("Opened Date")} timeFormat={false} disable={!writeCap} />
//           </bs.Col>
//           <bs.Col span={6}>
//             <input.BBDateTimeField bean={bill} field="closedDate" label={T("Closed Date")} timeFormat={false} disable={!writeCap} />
//           </bs.Col>
//         </bs.Row>
//         <bs.Row>
//           <bs.Col span={3}>
//             <input.BBDateTimeField bean={bill} field="pickupSuccessDate" label={T("Pickup Success Date")} timeFormat={false} disable={!writeCap} />
//           </bs.Col>
//           <bs.Col span={3}>
//             <input.BBDateTimeField bean={bill} field="deliverSuccessDate" label={T("Deliver Success Date")} timeFormat={false} disable={!writeCap} />
//           </bs.Col>
//           <bs.Col span={6}>
//             <input.BBDateTimeField bean={bill} field={"accountingReportDate"} label={T("Accounting Report Date")} timeFormat={false} disable={!writeCap} />
//           </bs.Col>
//         </bs.Row>
//         <bs.Row>
//           <bs.Col span={6}>
//             <label className={'form-label'}>{T("Pickup By")}</label>
//             {/* <BBTransporterAutoComplete
//               appContext={appContext} pageContext={pageContext} bean={bill}
//               field={"pickupByTransporterId"} labelField={"pickupByTransporter"}
//               codeField={"pickupByTransporterCode"} disable={!writeCap} /> */}
//           </bs.Col>
//           <bs.Col span={6}>
//             <label className={'form-label'}>{T("Delivery By")}</label>
//             {/* <BBTransporterAutoComplete appContext={appContext} pageContext={pageContext} bean={bill}
//               field={"deliveryByTransporterId"} labelField={"deliveryByTransporter"}
//               codeField={"deliveryByTransporterCode"} disable={!writeCap} /> */}
//           </bs.Col>
//         </bs.Row>
//         <input.BBTextField bean={bill} field={"description"} label={T("Description")} style={{ height: "4em" }} disable={!writeCap} />
//       </div >
//     )
//   }
// }

// class UITMSBillForwarderTransportForm extends entity.AppDbEntityEditor {
//   render() {
//     let { appContext, pageContext, observer, onModify } = this.props;
//     const writeCap = pageContext.hasUserWriteCapability();
//     let bill = observer.getMutableBean();
//     let forwarder = observer.getBeanProperty('tmsBillForwarderTransport', {});
//     let fleetEntityQuery = new FleetEntityQuery;

//     return (
//       <div>
//         <bs.Col span={12}>
//           <input.BBStringField bean={bill} field="storageState" label={T("StorageState")} disable={!writeCap} />
//         </bs.Col>
//         <bs.Row>
//           <bs.Col span={3}>
//             <input.BBStringField bean={bill} field="label" label={T("Label")} disable={!writeCap} />
//           </bs.Col>
//           <bs.Col span={3}>
//             <bs.FormLabel className="form-label">{T('Mode')}</bs.FormLabel>
//             <input.BBSelectField bean={forwarder} field={'mode'}
//               options={[
//                 TMSBillTransportationMode.ExportFcl, TMSBillTransportationMode.ExportLcl, TMSBillTransportationMode.ExportAir,
//                 TMSBillTransportationMode.ImportFcl, TMSBillTransportationMode.ImportLcl, TMSBillTransportationMode.ImportAir,
//                 TMSBillTransportationMode.Domestic
//               ]}
//               optionLabels={[
//                 'Export Fcl', 'Export Lcl', 'Export Air',
//                 'Import Fcl', 'Import Lcl', 'Import Air',
//                 'Domestic'
//               ]} disable={!writeCap} onInputChange={onModify} />
//           </bs.Col>
//           <bs.Col span={3}>
//             <bs.FormLabel className="form-label">{T('Type')}</bs.FormLabel>
//             <input.BBSelectField bean={bill} field={'type'}
//               options={[TMSBillType.IMS, TMSBillType.FORWARDER]} onInputChange={onModify} disable={!writeCap} />
//           </bs.Col>
//           <bs.Col span={3}>
//             <input.BBStringField bean={bill} field={"collect"} label={"Collect"} disable={!writeCap} />
//           </bs.Col>
//         </bs.Row>
//         <bs.Row>
//           <bs.Col span={6}>
//             <input.BBDateInputMask
//               bean={bill} field="openedDate" label={T("Opened Date")} format="DD/MM/YYYY" timeFormat />
//           </bs.Col>
//           <bs.Col span={3}>
//             <input.BBDateInputMask
//               bean={bill} field="closedDate" label={T("Closed Date")} format="DD/MM/YYYY" timeFormat />
//           </bs.Col>
//           <bs.Col span={3}>
//             <input.BBDateInputMask bean={bill} field={"accountingReportDate"} label={T("Accounting Report Date")} format="DD/MM/YYYY" timeFormat />
//           </bs.Col>
//         </bs.Row>

//         <bs.Row>
//           {TMSBillTransportationModeTools.isExport(forwarder.mode) ?
//             <>
//               <bs.Col span={6}>
//                 <bs.FormLabel className="form-label">{T('Warehouse Name')}</bs.FormLabel>
//                 {/* <BBWarehouseLocationAutoComplete appContext={appContext} pageContext={pageContext} bean={forwarder} inputObserver={observer}
//                   labelField={'warehouseLabel'} field={'warehouseId'} disable={!writeCap} /> */}
//               </bs.Col>
//               <bs.Col span={3}>
//                 <input.BBDateInputMask
//                   bean={bill} field="planPickupSuccessTime" label={T("Plan Pickup Time")} format="DD/MM/YYYY" timeFormat />
//               </bs.Col>
//               <bs.Col span={3}>
//                 <input.BBDateInputMask bean={forwarder} field={'cutOffTime'} label={T("Cut Off Time")} format={"DD/MM/YYYY"} timeFormat />
//               </bs.Col>
//             </>
//             : TMSBillTransportationModeTools.isImport(forwarder.mode) ?
//               <>
//                 <bs.Col span={6}>
//                   <bs.FormLabel className="form-label">{T('Warehouse Name')}</bs.FormLabel>
//                   {/* <BBWarehouseLocationAutoComplete appContext={appContext} pageContext={pageContext} bean={forwarder} inputObserver={observer}
//                     labelField={'warehouseLabel'} field={'warehouseId'} disable={!writeCap} /> */}
//                 </bs.Col>
//                 <bs.Col span={3}>
//                   <input.BBDateInputMask disabled={!writeCap}
//                     bean={forwarder} field={'eta'} label={T("ETA")} format={"DD/MM/YYYY"} timeFormat />
//                 </bs.Col>
//                 <bs.Col span={3}>
//                   <input.BBDateInputMask disabled={!writeCap}
//                     bean={bill} field="planDeliverySuccessTime" format="DD/MM/YYYY" label={T("Plan Delivery Time")} timeFormat />
//                 </bs.Col>
//               </>
//               :
//               <>
//                 <bs.Col span={6}>
//                   <input.BBDateInputMask disabled={!writeCap}
//                     bean={bill} field="planPickupSuccessTime" label={T("Plan Pickup Time")} format="DD/MM/YYYY" timeFormat />
//                 </bs.Col>
//                 <bs.Col span={6}>
//                   <input.BBDateInputMask disabled={!writeCap}
//                     bean={bill} field="planDeliverySuccessTime" format="DD/MM/YYYY" label={T("Plan Delivery Time")} timeFormat />
//                 </bs.Col>
//               </>
//           }
//         </bs.Row>
//         <bs.Row>
//           <bs.Col span={3}>
//             <input.BBStringField bean={bill} field="bookingCode" label={T("Booking Code")} disable={!writeCap} />
//           </bs.Col>
//           <bs.Col span={3}>
//             <input.BBStringField bean={bill} field="declarationNumber" label={T("Declaration Number")} disable={!writeCap} />
//           </bs.Col>
//           <bs.Col span={3}>
//             <label className="form-label">{T('Vendor')}</label>
//             <entity.BBModelAutoComplete
//               appContext={appContext} pageContext={pageContext} disable={!writeCap}
//               query={fleetEntityQuery.partner()} bean={bill} field={'vendorId'} labelField={'vendorFullName'} />
//           </bs.Col>
//           <bs.Col span={3}>
//             <label className="form-label">{T('Carrier Partner')}</label>
//             <entity.BBModelAutoComplete
//               appContext={appContext} pageContext={pageContext} disable={!writeCap}
//               query={fleetEntityQuery.partner()} bean={bill} field={'carrierId'} labelField={'carrierFullName'} />
//           </bs.Col>
//         </bs.Row>

//         <input.BBTextField bean={bill} field={"description"} label={T("Description")} style={{ height: "4em" }} disable={!writeCap} />
//       </div >
//     )
//   }
// }

// class UITMSBillInfoCards extends entity.AppDbComplexEntityEditor {
//   onModify = (bean: any, field: string, oldVal: any, newVal: any) => {
//     const { onModify } = this.props;
//     if (onModify) onModify(bean, field, oldVal, newVal);
//     this.forceUpdate()
//   }

//   render() {
//     const { appContext, pageContext, observer } = this.props;
//     const bill = observer.getMutableBean();
//     const writeCap = pageContext.hasUserWriteCapability();
//     const moderatorCap = pageContext.hasUserModeratorCapability();
//     let type = bill['type'];
//     let forwarder = observer.getBeanProperty('tmsBillForwarderTransport', {});
//     let mode = forwarder.mode;

//     return (
//       <bs.ScrollableCards className="flex-vbox">
//         <bs.Card header={T("Bill Info")}>
//           {type === TMSBillType.IMS ?
//             < UITMSBillTransportForm appContext={appContext} pageContext={pageContext} observer={observer} onModify={this.onModify} />
//             : <UITMSBillForwarderTransportForm appContext={appContext} pageContext={pageContext} observer={observer} onModify={this.onModify} />
//           }
//         </bs.Card>
//         {TMSBillTransportationModeTools.isDomestic(mode) || TMSBillTransportationModeTools.isExport(mode) ?
//           <bs.Card header={T('Sender')}>
//             <bs.FormLabel className={'form-label'}>{T("Customer")}</bs.FormLabel>
//             {/* <BBRefTMSPartner type='Customer' minWidth={400} refBy="code"
//               appContext={appContext} pageContext={pageContext} placeholder={'Customer'} disable={!writeCap}
//               bean={observer.createObserver('customer', {}).getMutableBean()} beanIdField={'customerCode'} beanLabelField={'customerFullName'} /> */}
//             <UISenderForm appContext={appContext} pageContext={pageContext}
//               observer={observer.createObserver('sender', {})} readOnly={!writeCap} />
//           </bs.Card> : null
//         }
//         {TMSBillTransportationModeTools.isImport(mode) ?
//           <bs.Card header={T('Receiver')}>
//             <bs.FormLabel className={'form-label'}>{T("Customer")}</bs.FormLabel>
//             {/* <BBRefTMSPartner type='Customer' refBy="code" minWidth={400} disable={!writeCap}
//               appContext={appContext} pageContext={pageContext} placeholder={'Customer'}
//               bean={observer.createObserver('customer', {}).getMutableBean()} beanIdField={'customerCode'} beanLabelField={'customerFullName'} /> */}
//             <UIReceiverContact
//               appContext={appContext} pageContext={pageContext} observer={observer.createObserver('receiver', {})} readOnly={!writeCap} />
//           </bs.Card> : null
//         }
//         {TMSBillTransportationModeTools.isDomestic(mode) ?
//           <bs.Card header={T('Receiver')}>
//             <UIReceiverContact
//               appContext={appContext} pageContext={pageContext} observer={observer.createObserver('receiver', {})} readOnly={!writeCap} />
//           </bs.Card>
//           : null
//         }
//         <bs.Card header={T("Current State")}>
//           <UITMSBillResponsibilityForm
//             appContext={appContext} pageContext={pageContext}
//             observer={observer.createObserver('currentResponsibility', {})} readOnly={!writeCap} />
//         </bs.Card>
//         <bs.Card header={T("Technical")}>
//           <bs.Row>
//             <bs.Col span={12}>
//               <input.BBStringField bean={bill} field={"code"} label={T("Code")} disable={!writeCap} />
//             </bs.Col>
//           </bs.Row>
//           <bs.Row>
//             <bs.Col span={6}>
//               <input.BBStringField bean={bill} field={"refSource"} label={T("Referent Source")} disable />
//             </bs.Col>
//             <bs.Col span={3}>
//               <input.BBStringField bean={bill} field={"refCode"} label={T("Referent Code")} disable={!moderatorCap} />
//             </bs.Col>
//             <bs.Col span={3}>
//               <input.BBStringField bean={bill} field={"refId"} label={T("Referent Id")} disable={!moderatorCap} />
//             </bs.Col>
//           </bs.Row>
//           <bs.Row>
//             <bs.Col span={6}>
//               <input.BBStringField bean={bill} field={"regionOfResponsibilityCode"} label={T("Region Of Responsibility Code")} disable={!moderatorCap} />
//             </bs.Col>
//             <bs.Col span={6}>
//               <input.BBStringField bean={bill} field={"originLogisticsHubCode"} label={T("Origin Logistics Hub Code")} disable={!moderatorCap} />
//             </bs.Col>
//           </bs.Row>
//         </bs.Card>
//       </bs.ScrollableCards>
//     );
//   }
// }

// interface UITMSBillCollectedFeeFormProps extends entity.AppDbEntityProps {
//   type: 'Pickup' | 'Deliver';
// }
// class UITMSBillCollectedFeeForm extends entity.AppDbEntity<UITMSBillCollectedFeeFormProps> {
//   render() {
//     let { appContext, pageContext, observer, type } = this.props;
//     const collectedFee = observer.getMutableBean();
//     const writeCap = pageContext.hasUserWriteCapability();
//     return (
//       <div className="mx-1">
//         <bs.Row>
//           <bs.Col span={6}>
//             <input.BBStringField field={"label"} label={T("Label")} bean={collectedFee} disable={!writeCap} />
//           </bs.Col>
//           <bs.Col span={6}>
//             <input.BBSelectField options={["Cash", "Wired"]} bean={collectedFee} field={"paymentMethod"} label={T("Payment Method")} disable={!writeCap} />
//           </bs.Col>
//         </bs.Row>
//         <bs.Row>
//           <bs.Col span={12} >
//             <label className={'form-label'}>{T("Confirm By Login ID")}</label>
//             <BBRefAccount
//               appContext={appContext} pageContext={pageContext} disable={!writeCap}
//               label={T('Representative')} placeholder='Enter An Account'
//               bean={collectedFee} accountIdField={'confirmedByLoginId'} accountLabelField={''} />
//           </bs.Col>
//           <bs.Col span={6}>
//             <input.BBDateTimeField bean={collectedFee} field={"collectedDate"} label={T("Collected Date")} timeFormat={false} disable={!writeCap} />
//           </bs.Col>
//           <bs.Col span={6}>
//             <input.BBDateTimeField bean={collectedFee} field={"confirmCollectedDate"} label={T("Confirm Collected")} timeFormat={false} disable={!writeCap} />
//           </bs.Col>
//         </bs.Row>
//         <bs.Row>
//           <bs.Col span={12}>
//             <label className={'form-label'}>{T("Transporter")}</label>
//             {/* <BBTransporterAutoComplete
//               appContext={appContext} pageContext={pageContext} bean={collectedFee}
//               field={"responsibleDriverAccountId"} labelField={"responsibleDriver"} disable={!writeCap} /> */}
//           </bs.Col>
//           {type === "Deliver" ?
//             <bs.Col span={6}>
//               <input.BBCurrencyField bean={collectedFee} field={"cod"} label={T("COD")} disable={!writeCap} />
//             </bs.Col> : <></>
//           }
//           <bs.Col span={type === "Deliver" ? 6 : 12}>
//             <input.BBCurrencyField bean={collectedFee} field={"finalShipmentCharge"} label={T("F. Shipment Charge")} disable={!writeCap} />
//           </bs.Col>
//         </bs.Row>
//         <bs.Row>
//           <bs.Col span={6}>
//             <input.BBCurrencyField bean={collectedFee} field={"goodsInsurance"} label={T("Goods Insurance")} disable={!writeCap} />
//           </bs.Col>
//           <bs.Col span={6}>
//             <input.BBCurrencyField bean={collectedFee} field={"totalCollect"} label={T("Total Collect")} disable={!writeCap} />
//           </bs.Col>
//         </bs.Row>
//       </div>
//     );
//   }
// }

// class UISenderForm extends entity.AppDbEntity {
//   render() {
//     let { appContext, pageContext, observer } = this.props;
//     const sender = observer.getMutableBean();
//     const writeCap = pageContext.hasUserWriteCapability();

//     return (
//       <div className="mx-1">
//         <bs.Row>
//           <bs.Col span={12}>
//             <input.BBStringField bean={sender} field={"senderFullName"} label={T("Full Name")} disable={!writeCap} />
//           </bs.Col>
//         </bs.Row>
//         <input.BBStringField bean={sender} field={"senderContact"} label={T("Contact")} disable={!writeCap} />
//         {/* <bs.Row>
//           <bs.Col span={6}>
//             <bs.FormLabel className={'form-label'}>{T("District")}</bs.FormLabel>
//             <module.settings.BBDistrictAutoComplete
//               appContext={appContext} pageContext={pageContext} disable={!writeCap}
//               bean={sender} field={"senderDistrictId"} labelField={"senderDistrictLabel"} />
//           </bs.Col>
//           <bs.Col span={6}>
//             <bs.FormLabel className={'form-label'}>{T("City")}</bs.FormLabel>
//             <module.settings.BBDistrictAutoComplete
//               appContext={appContext} pageContext={pageContext} disable={!writeCap}
//               bean={sender} field={"senderCityId"} labelField={"senderCityLabel"} />
//           </bs.Col>
//         </bs.Row> */}
//         {/* <bs.Row>
//           <bs.Col span={6}>
//             <bs.FormLabel className={'form-label'}>{T("State")}</bs.FormLabel>
//             <module.settings.BBStateAutoComplete
//               appContext={appContext} pageContext={pageContext}
//               bean={sender} field={"senderStateId"} labelField="senderStateLabel"
//               countryId={sender.senderCountryId} disable={!writeCap} />
//           </bs.Col>
//           <bs.Col span={6}>
//             <bs.FormLabel className={'form-label'}>{T("Country")}</bs.FormLabel>
//             <module.settings.BBCountryAutoComplete
//               appContext={appContext} pageContext={pageContext} bean={sender}
//               field={"senderCountryId"} labelField={"senderCountryLabel"} disable={!writeCap} />
//           </bs.Col>
//         </bs.Row> */}
//         <bs.Row>
//           <bs.Col span={12}>
//             <input.BBTextField style={{ height: "4em" }} bean={sender} field={"senderAddress"} label={T("Address")} disable={!writeCap} />
//           </bs.Col>
//         </bs.Row>
//       </div>
//     );
//   }
// }

// class UIReceiverContact extends entity.AppDbEntityEditor {
//   render() {
//     let { appContext, pageContext, observer } = this.props;
//     let receiver = observer.getMutableBean();
//     const writeCap = pageContext.hasUserWriteCapability();
//     return (
//       <div className="mx-1">
//         <input.BBStringField bean={receiver} field={"receiverFullName"} label={T("Full Name")} disable={!writeCap} />
//         <input.BBStringField bean={receiver} field={"receiverContact"} label={T("Contact")} disable={!writeCap} />
//         {/* <bs.Row>
//           <bs.Col span={6}>
//             <bs.FormLabel className={'form-label'}>{T("District")}</bs.FormLabel>
//             <module.settings.BBDistrictAutoComplete
//               appContext={appContext} pageContext={pageContext} disable={!writeCap}
//               bean={receiver} field={"receiverDistrictId"} labelField={"receiverDistrictLabel"} />
//           </bs.Col>
//           <bs.Col span={6}>
//             <bs.FormLabel className={'form form-label'}>{T("City")}</bs.FormLabel>
//             <module.settings.BBDistrictAutoComplete appContext={appContext} pageContext={pageContext} bean={receiver} disable={!writeCap}
//               field={"receiverCityId"} labelField={"receiverCityLabel"} />
//           </bs.Col>
//         </bs.Row>
//         <bs.Row>
//           <bs.Col span={6}>
//             <bs.FormLabel className={'form form-label'}>{T("State")}</bs.FormLabel>
//             <module.settings.BBStateAutoComplete appContext={appContext} pageContext={pageContext} bean={receiver}
//               field={"receiverStateId"} labelField={"receiverStateLabel"} countryId={receiver.receiverCountryId}
//               disable={!writeCap} />
//           </bs.Col>
//           <bs.Col span={6}>
//             <bs.FormLabel className={'form-label'}>{T("Country")}</bs.FormLabel>
//             <module.settings.BBCountryAutoComplete appContext={appContext} pageContext={pageContext} bean={receiver} disable={!writeCap}
//               field="receiverCountryId" labelField="receiverCountryLabel" />
//           </bs.Col>
//         </bs.Row> */}
//         <bs.Row>
//           <bs.Col span={12}>
//             <input.BBTextField style={{ height: "4em" }} field={"receiverAddress"} label={T("Address")} bean={receiver} disable={!writeCap} />
//           </bs.Col>
//         </bs.Row>
//       </div>
//     );
//   }
// }

// class UITMSBillGoodsEditor extends entity.AppDbComplexEntityEditor {
//   render() {
//     let { appContext, pageContext, observer } = this.props;
//     const writeCap = pageContext.hasUserWriteCapability();
//     const tmsBillGoods = observer.getMutableBean();
//     return (
//       <>
//         <UITMSGoodItemListEditor style={{ minHeight: 220 }}
//           appContext={appContext} pageContext={pageContext} dialogEditor={true} editorTitle={T("Item")}
//           plugin={observer.createVGridEntityListEditorPlugin("goodItems", [])} />
//         <div className="mx-2">
//           <bs.Row>
//             <bs.Col span={3}>
//               <input.BBSelectField
//                 bean={tmsBillGoods} field="goodsType" label={T("Type")} disable={!writeCap}
//                 options={[GoodsType.Letter, GoodsType.Parcel, GoodsType.Pallet, GoodsType.Box]} />
//             </bs.Col>
//             <bs.Col span={3}>
//               <input.BBSelectField bean={tmsBillGoods} field="tmsType" label={("TMS Type")} disable={true}
//                 options={[TMSTransportType.EXPRESS, TMSTransportType.FAST,
//                 TMSTransportType.SAVING, TMSTransportType.GROUND_SERVICE,]} />
//             </bs.Col>
//             <bs.Col span={3}>
//               <input.BBDoubleField bean={tmsBillGoods} field={"quantity"} label={T("Quantity")} disable={!writeCap} />
//             </bs.Col>
//             <bs.Col span={3}>
//               <label className={'form-label'}>{T("Unit")}</label>
//               <module.settings.unit.BBRefUnit appContext={appContext} pageContext={pageContext} bean={tmsBillGoods}
//                 beanIdField={"quantityUnit"} placeholder="Enter Unit" groupNames={["unit"]} disable={!writeCap} />
//             </bs.Col>
//           </bs.Row>
//           <bs.Row>
//             <bs.Col span={6}>
//               <input.BBDoubleField bean={tmsBillGoods} field={"chargeableVolume"} label={T("Chargeable Volume")} disable={!writeCap} />
//             </bs.Col>
//             <bs.Col span={3}>
//               <input.BBDoubleField bean={tmsBillGoods} field={"volume"} label={T("Volume")} disable={!writeCap} />
//             </bs.Col>
//             <bs.Col span={3}>
//               <label className={'form-label'}>{T("Unit")}</label>
//               <module.settings.unit.BBRefUnit appContext={appContext} pageContext={pageContext} bean={tmsBillGoods} beanIdField={"volumeUnit"}
//                 placeholder="Enter Unit" groupNames={["volume"]} disable={!writeCap} />
//             </bs.Col>
//           </bs.Row>
//           <bs.Row>
//             <bs.Col span={6}>
//               <input.BBCurrencyField bean={tmsBillGoods} field={"chargeableWeight"} label={T("Chargeable Weight")} disable={!writeCap} />
//             </bs.Col>
//             <bs.Col span={3}>
//               <input.BBCurrencyField bean={tmsBillGoods} field={"weight"} label={T("Weight")} disable={!writeCap} />
//             </bs.Col>
//             <bs.Col span={3}>
//               <label className={'form-label'}>{T("Unit")}</label>
//               <module.settings.unit.BBRefUnit appContext={appContext} pageContext={pageContext} bean={tmsBillGoods} beanIdField={"weightUnit"}
//                 placeholder="Enter Unit" groupNames={["weight"]} disable={!writeCap} />
//             </bs.Col>
//           </bs.Row>

//           <bs.Row>
//             <bs.Col span={6}>
//               <input.BBStringField bean={tmsBillGoods} field="goods" label={T("Goods")} disable={!writeCap} />
//             </bs.Col>
//             <bs.Col span={3}>
//               <input.BBCurrencyField bean={tmsBillGoods} field="goodsEstimatedValue" label={T("Estimated Value")} disable={!writeCap} />
//             </bs.Col>
//             <bs.Col span={3}>
//               <module.settings.currency.BBRefCurrency
//                 appContext={appContext} pageContext={pageContext} bean={tmsBillGoods}
//                 beanIdField={"goodsEstimatedValueCurrency"} disable={!writeCap}
//                 placeholder="Currency" label="Currency" />
//             </bs.Col>
//           </bs.Row>
//           <bs.Row>
//             <bs.Col span={12}>
//               <input.BBTextField bean={tmsBillGoods} field={"goodsDescription"} label={T("Goods Description")} disable={!writeCap} style={{ height: "4em" }} />
//             </bs.Col>
//           </bs.Row>
//         </div >
//       </>
//     )
//   }
// }

// class TMSBillFeeCards extends entity.AppDbComplexEntityEditor {
//   render() {
//     let { appContext, pageContext, observer } = this.props;
//     const writeCap = pageContext.hasUserWriteCapability();
//     return (
//       <bs.ScrollableCards className="flex-vbox">
//         <bs.Card header={T('Bill Fee')}>
//           <UITMSBillFeeEditor type="cost"
//             appContext={appContext} pageContext={pageContext} readOnly={!writeCap}
//             observer={observer.createComplexBeanObserver("tmsBillFee", {})} />
//         </bs.Card>
//         <bs.Card className='flex-hbox' header={T('Collected Fee')}>
//           <div className={'w-50 card'} style={{ marginRight: 5 }}>
//             <bs.TabPane>
//               <bs.Tab name={'deliver-collected-fee'} label={T('Deliver Collected Fee')} active>
//                 <UITMSBillCollectedFeeForm
//                   appContext={appContext} pageContext={pageContext} readOnly={!writeCap}
//                   observer={observer.createObserver('deliverCollectFee', {})} type={"Deliver"} />
//               </bs.Tab>
//             </bs.TabPane>
//           </div>
//           <div className={'flex-grow-1 card justify-content-end'}>
//             <bs.TabPane>
//               <bs.Tab name={'pickup-collected-fee'} label={T('Pickup Collected Fee')} active>
//                 <UITMSBillCollectedFeeForm
//                   appContext={appContext} pageContext={pageContext} readOnly={!writeCap}
//                   observer={observer.createObserver('pickupCollectFee', {})} type={"Pickup"} />
//               </bs.Tab>
//             </bs.TabPane>
//           </div>
//         </bs.Card>
//       </bs.ScrollableCards>
//     )
//   }
// }


// export class UITMSBill extends entity.AppDbComplexEntityEditor {
//   onPostCommit = (entity: any, _uiEditor?: app.AppComponent) => {
//     this.props.observer.setMutableBean(entity);
//     this.forceUpdate();
//   };

//   onPreCommit = (observer: entity.ComplexBeanObserver) => {
//     this.onUpdateCostItems(observer);
//   };

//   onUpdateCostItems(observer: entity.ComplexBeanObserver) {
//     const items: Array<any> = [];
//     const tmsBillFee = observer.createComplexBeanObserver('tmsBillFee', {});
//     tmsBillFee.commitAndGet();
//     items.push(...tmsBillFee.getBeanProperty('revenueItems', []));
//     items.push(...tmsBillFee.getBeanProperty('costItems', []));
//     tmsBillFee.replaceBeanProperty('items', items);
//     delete tmsBillFee.getMutableBean().revenueItems;
//     delete tmsBillFee.getMutableBean().costItems;
//     observer.commitAndGet();
//   }

//   onPostRollback = (entity: any) => {
//     this.forceUpdate();
//   }

//   onNewResponsibility = (type: ResponsibilityType = ResponsibilityType.Sender) => {
//     let { appContext, pageContext, observer } = this.props;
//     let currentResponsibility = observer.getBeanProperty("currentResponsibility", {});
//     let bill = observer.getMutableBean();
//     const writeCap = pageContext.hasUserWriteCapability();
//     let newState = UITMSBillUtils.createResponsibility(bill, currentResponsibility, type);
//     let responsibilityOb = new entity.BeanObserver(newState);

//     let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
//       let onSave = () => {
//         let responsibility = responsibilityOb.getMutableBean();
//         let activity = UITMSBillUtils.createTMSBillActivity(responsibility);
//         let responsibilityRequest = { tmsBill: bill, responsibility: responsibility, activity: activity, };
//         appContext
//           .createHttpBackendCall('TMSBillService', 'createTMSBillResponsibility', { req: responsibilityRequest })
//           .withSuccessData((data) => {
//             pageCtx.back({ reload: true });
//             this.onPostCommit(data);
//           })
//           .withFailNotification("danger", T("Create Responsibility Fail!"))
//           .withSuccessNotification("success", T("Create Responsibility Success"))
//           .call();
//       };

//       return (
//         <div className="flex-vbox">
//           <UITMSBillResponsibilityForm appContext={appCtx} pageContext={pageCtx}
//             observer={responsibilityOb} />
//           <bs.Toolbar className='border' hide={!writeCap}>
//             <entity.WButtonEntityWrite
//               appContext={appCtx} pageContext={pageCtx} label={T("Create")}
//               icon={FeatherIcon.Save} onClick={onSave} />
//           </bs.Toolbar>
//         </div>
//       );
//     }
//     pageContext.createPopupPage('responsibility', T("New Responsibility"), createAppPage, { size: "md", backdrop: "static" });
//   };

//   createNewButtonState(): Array<bs.ActionModel> {
//     let thisUi = this;
//     let actionItems: Array<bs.ActionModel> = [
//       {
//         name: "picking-up", label: "Picking Up",
//         onSelect() { thisUi.onNewResponsibility(ResponsibilityType.PickingUp); },
//       },
//       {
//         name: "transport", label: "Transport",
//         onSelect() { thisUi.onNewResponsibility(ResponsibilityType.Transport); },
//       },
//       {
//         name: "delivering", label: "Delivering",
//         onSelect() { thisUi.onNewResponsibility(ResponsibilityType.Delivering); },
//       },
//       {
//         name: "others", label: "Others",
//         onSelect() { thisUi.onNewResponsibility(); },
//       },
//     ];
//     return actionItems;
//   }

//   render() {
//     let { appContext, pageContext, observer } = this.props;
//     const bill = observer.getMutableBean();
//     const writeCap = pageContext.hasUserWriteCapability();
//     const currentResponsibility = observer.getBeanProperty("currentResponsibility", {});
//     const dropdownId = `add-inquiry-dropdown-${this.componentId}`;
//     return (
//       <bs.VSplit updateOnResize key={this.viewId} smallScreenView="tabs">
//         <bs.VSplitPane title={T('Bill Info')} width={'50%'}>
//           <div className={'flex-vbox'}>
//             <bs.TabPane>
//               <bs.Tab name="bill-info" label={T("Bill Info")} active>
//                 <UITMSBillInfoCards appContext={appContext} pageContext={pageContext} observer={observer} readOnly={!writeCap} />
//               </bs.Tab>
//               <bs.Tab name="goods" label={T("Goods")}>
//                 <UITMSBillGoodsEditor
//                   appContext={appContext} pageContext={pageContext} observer={observer.createComplexBeanObserver('tmsBillGoods', {})} />
//               </bs.Tab>
//               <bs.Tab name="fee" label={T("Fee")}>
//                 <TMSBillFeeCards
//                   appContext={appContext} pageContext={pageContext} readOnly={!writeCap} observer={observer} />
//               </bs.Tab>
//             </bs.TabPane>

//             <bs.Toolbar className='border' hide={!writeCap}>
//               <bs.Dropdown>
//                 <bs.DropdownToggle laf='primary' dropdownId={dropdownId}>
//                   <FeatherIcon.Plus size={12} />
//                   {T("New Responsibility")}
//                   <FeatherIcon.ChevronDown size={12} />
//                 </bs.DropdownToggle>
//                 <bs.DropdownAction dropdownId={dropdownId} items={this.createNewButtonState()} />
//               </bs.Dropdown>
//               <entity.WButtonEntityCommit appContext={appContext} pageContext={pageContext} observer={observer}
//                 label={T("TMS Bill")} commitURL={ManagementRestURL.tmsBill.save} onPreCommit={this.onPreCommit} onPostCommit={this.onPostCommitCallback} />
//               <entity.WButtonEntityReset appContext={appContext} pageContext={pageContext} readOnly={!writeCap}
//                 observer={observer} onPostRollback={this.onPostRollback} />
//             </bs.Toolbar>
//           </div>
//         </bs.VSplitPane>
//         <bs.VSplitPane title={"Activities Detail"}>
//           <bs.TabPane>
//             <bs.Tab label={T('Responsibilities')} name={'responsibilities-tab'}>
//               <UITMSBillResponsibilityList
//                 plugin={new UITMSBillResponsibilityListPlugin().withTMSBillCode(bill.code)}
//                 appContext={appContext} pageContext={pageContext} readOnly={true} type="page" />
//             </bs.Tab>
//             <bs.Tab label={T('Activities')} name={'activities-tab'} active>
//               <UITMSBillActivityList
//                 appContext={appContext} pageContext={pageContext}
//                 responsibility={currentResponsibility} readOnly={!writeCap}
//                 plugin={new UITMSBillActivityListPlugin().addParam("tmsBillCode", bill.code)} type="page" />
//             </bs.Tab>
//           </bs.TabPane>
//         </bs.VSplitPane>
//       </bs.VSplit >
//     );
//   }
// }