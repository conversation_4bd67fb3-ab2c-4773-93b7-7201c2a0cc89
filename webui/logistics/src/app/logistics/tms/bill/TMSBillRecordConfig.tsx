import React from 'react';
import * as FeatherIcon from 'react-feather'
import { util, bs, grid, input, app, entity } from '@datatp-ui/lib';
import { module } from "@datatp-ui/erp";

import { T } from '../backend';
import { ManagementRestURL, VehicleFleetURL } from '../RestURL';

import {
  TMSBillProcessStatus,
  TMSBillTransportationMode,
  TMSRoundUsedStatus,
} from '../models';
import {
  TMSBillProcessStatusTools,
  TMSBillTransportationModeTools, TMSRoundUsedStatusTools,
  TMSUtils, TMSVGridConfigTool, UIVendorAttachments
} from '../utils';
import {
  renderBtnTrackingAPI,
  UITMSVendorBillList,
} from '../../vendor/UITMSVendorBillList';

import { onSaveTMSBillModified, updateTMSBillData } from './UITMSBillListPageControl';
import { TMS<PERSON><PERSON><PERSON><PERSON><PERSON>, TMS<PERSON><PERSON><PERSON><PERSON>ields, UITMSBillUtils } from './UITMSBillUtils';
import { ButtonTMSBillJobOther } from './UITMSBillJobOtherList';
import { UITMSBillList } from './UITMSBillList';
import { UITMSGoodsTracking, UIVendorBillInfo } from '../../vendor/api/TMSTrackingPlugin';
import { WBtnTMSBillJobTracking } from './UITMSJobTracking';
import { renderSummary } from './TMSBillRecordSummaryConfig';
import { BBOptionAddress, BBRefTMSCustomer } from '../partner/BBRefTMSCustomer';
import { BBRefTMSCarrier } from '../partner/BBRefTMSCarrier';
import { ConvertBFSOneCustomerEditor } from '../partner/UITMSCustomerList';
import { NotificationMessage, WBtnMail } from './UITMSBillMessage';

import { BBRefTMSPartnerAddress } from '../partner/BBRefTMSPartnerAddress';
import { BBRefVehicleFleet } from '../vehicle/BBRefVehicleFleet';
import { UIVehicleTripForm } from 'app/logistics/vehicletrip/UIVehicleTrip';
import BBRefLocation = module.settings.BBRefLocation;

const formatCurrency = (val: any) => { return util.text.formater.currency(val, 0) };

const compactDate = (val: string) => {
  if (!val) return;
  return util.text.formater.compactDate(val);
}

const compactDateTime = (val: string) => {
  if (!val) return;
  return util.text.formater.dateTime(util.TimeUtil.parseCompactDateTimeFormat(val));
}

function isValidTimeFormat(input: string) {
  const regex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
  return regex.test(input);
}

export class WTMSGridInsertRow extends grid.WGridInsertRow {
  override onInsert = () => {
    let { context, row, createInsertRecord, allowInsert } = this.props;
    let dRecord = context.model.getDisplayRecordList().getDisplayRecordAt(row);

    let insert = true;
    if (allowInsert) insert = allowInsert(context, dRecord);

    if (insert) {
      let newRecord = createInsertRecord(context, dRecord)
      context.model.insertDisplayRecordAt(row, newRecord);
      let state = grid.getRecordState(newRecord);
      state.markModified();
      context.getVGrid().forceUpdateView(true);
    }
  }
}

function renderGoodsTrackingButton(
  ctx: grid.VGridContext, bill: any, config?: { btnSize: number, iconSize: number, cssClass?: any }) {
  let trackings: Array<any> = bill.trackings;
  let totalTrucking = 0;
  let truckInfos = [];
  for (let tracking of trackings) {
    if (tracking['vehicleLabel']) {
      totalTrucking++;
      truckInfos.push(TMSJoinFields(
        tracking,
        { 'mobile': '\nMobile', 'identificationNo': '\nID' },
        'vehicleType', 'vehicleLabel', 'driverFullName', 'mobile', 'identificationNo'
      ));
    }
  }
  let countIcon;
  countIcon = (
    <div className={'align-items-center justify-content-center text-center'}
      style={{
        height: 12, width: 12, borderRadius: 50, fontSize: 12,
        backgroundColor: 'red', color: 'white',
        marginTop: -6, marginLeft: -4
      }}>
      {totalTrucking}
    </div>
  );
  let tooltip = truckInfos.join(', \n');
  if (totalTrucking == 0) tooltip = T('No Vehicle Information Yet!!!');

  let btnSize = 22;
  let iconSize = 12
  let cssClass = '';
  if (config) {
    iconSize = config.iconSize;
    btnSize = config.btnSize;
    cssClass = config.cssClass;
  }
  return (
    <bs.Button style={{ width: btnSize, marginLeft: 4 }} className={`text-primary ${cssClass}`}
      onClick={() => onShowTrackings(ctx, bill)} laf='link'>
      <bs.Tooltip tooltip={tooltip} className='flex-hbox'>
        <FeatherIcon.Truck size={iconSize} />
        {countIcon}
      </bs.Tooltip>
    </bs.Button>
  );
}

function onShowTrackings(ctx: grid.VGridContext, bill: any) {
  let uiRoot = ctx.uiRoot as entity.DbEntityList;
  let smallScreen = bs.ScreenUtil.isSmallScreen();
  let { appContext, pageContext } = uiRoot.props;
  if (bill['trackings'] == null) return;
  let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
    let config: bs.TabPaneConfig = {
      tabs: [
        {
          name: 'tracking', label: T('Trackings'), active: true,
          renderContent: (_ctx: bs.UIContext) =>
            <TMSGoodsTrackingLoadable appContext={appCtx} pageContext={pageCtx} billId={bill['id']} />
        },
        {
          name: 'trip', label: T('Trips'),
          renderContent: (_ctx: bs.UIContext) =>
            <VehicleTripsEditor appContext={appContext} pageContext={pageContext} billId={bill['id']} />
        },
      ]
    }
    return (
      <bs.DefaultTabPane className="flex-vbox" config={config} />
    )
  }
  pageContext.createPopupPage('vehicle-trip-goods-trackings', T('Trackings'), createAppPage, { size: smallScreen ? 'xl' : 'lg' })
}


interface VehicleTripsEditorProps extends app.AppComponentProps {
  billId: any;
}

class VehicleTripsEditor extends app.AppComponent<VehicleTripsEditorProps> {
  vehicleTrips: []

  constructor(props: TMSGoodsTrackingLoadableProps) {
    super(props)
    this.markLoading(true);
  }

  componentDidMount(): void {
    let { appContext, billId } = this.props;
    appContext
      .createHttpBackendCall('VehicleRestCallService', 'findVehicleTripsByTMSBillId', { tmsBillId: billId })
      .withSuccessData((data: any) => {
        this.markLoading(false);
        this.vehicleTrips = data;
        this.forceUpdate();
      })
      .call();
  }

  render() {
    let { appContext, pageContext, readOnly } = this.props;
    if (this.isLoading()) return this.renderLoading();
    let config: bs.TabPaneConfig = {
      tabs: this.vehicleTrips.map((vehicleTrip: any, index) => {
        return {
          name: `vehicle-trip-${vehicleTrip.id}`, label: T(vehicleTrip.label), active: index == 0 ? true : false,
          renderContent: (_ctx: bs.UIContext) => {
            let observer = new entity.ComplexBeanObserver(vehicleTrip);

            return <div className='flex-vbox' key={`vehicle-trip-${vehicleTrip.id}`}>
              <UIVehicleTripForm appContext={appContext} pageContext={pageContext} observer={observer} />
              <bs.Toolbar className='border' hide={readOnly}>
                <entity.ButtonEntityCommit
                  appContext={appContext} pageContext={pageContext}
                  observer={observer}
                  onPostCommit={() => {
                    vehicleTrip = observer.getMutableBean();
                    this.forceUpdate();
                  }}
                  commit={{ service: 'VehicleService', commitMethod: 'saveVehicleTrip', entityLabel: T('VehicleTrip') }} />
              </bs.Toolbar>
            </div>
          }
        }
      })
    }
    return (
      <bs.DefaultTabPane className="flex-vbox" config={config} />
    )
  }
}

interface TMSGoodsTrackingLoadableProps extends app.AppComponentProps {
  billId: any;
}

class TMSGoodsTrackingLoadable extends app.AppComponent<TMSGoodsTrackingLoadableProps> {
  state = { trackingInfo: {} }

  constructor(props: TMSGoodsTrackingLoadableProps) {
    super(props)
    this.markLoading(true);
  }

  componentDidMount(): void {
    let { appContext, billId } = this.props;
    appContext
      .createHttpBackendCall('VehicleRestCallService', 'findTrackingInfoByTMSBillId', { tmsBillId: billId })
      .withSuccessData((data: any) => {
        this.markLoading(false);
        this.setState({ trackingInfo: data })
      })
      .call();
  }

  render() {
    let { appContext, pageContext } = this.props;
    if (this.isLoading()) return this.renderLoading();
    return <UITMSGoodsTracking appContext={appContext} pageContext={pageContext} data={this.state.trackingInfo} />
  }
}

function renderVendorTrackingButton(ctx: grid.VGridContext, bill: any, config?: { btnSize: number, iconSize: number, cssClass?: any }) {
  let vendorBills = bill['vendorBills'];
  let vendorBillInfo = T('No Vehicle Information Yet!!!');
  let totalTruck = 0;
  let infos = [];
  if (vendorBills) {
    for (let vendorBill of vendorBills) {
      if (vendorBill['licensePlate']) {
        totalTruck++;
        infos.push(
          TMSJoinFields(
            vendorBill,
            { 'driverMobile': '\nMobile', 'driverIdentificationNo': '\nID' },
            'vehicleType', 'licensePlate', 'driverFullName', 'driverMobile', 'driverIdentificationNo'
          ));
      }
    }
  }
  if (infos.length > 0) vendorBillInfo = infos.join(", \n");
  if (totalTruck == 0 && !bill.vendorBillSendVendor) {
    return <WBtnMail context={ctx} bill={bill} />
  };
  let countIcon = (
    <div className={'align-items-center justify-content-center text-center'}
      style={{
        height: 12, width: 12, borderRadius: 50, fontSize: 12,
        backgroundColor: 'red', color: 'white',
        marginTop: -6, marginLeft: -4
      }}>
      {totalTruck}
    </div>
  );

  let btnSize = 22;
  let iconSize = 12
  let cssClass = '';
  if (config) {
    iconSize = config.iconSize;
    btnSize = config.btnSize;
    cssClass = config.cssClass;
  }
  return (
    <bs.Button style={{ width: btnSize, marginLeft: 4 }}
      className={`text-secondary ${cssClass}`} onClick={() => onShowVendorBill(ctx, bill)} laf='link'>
      <bs.Tooltip tooltip={vendorBillInfo} className='flex-hbox'>
        <FeatherIcon.Truck size={iconSize} />
        {countIcon}
      </bs.Tooltip>
    </bs.Button>
  );
}

export function onShowVendorBill(ctx: grid.VGridContext, bill: any) {
  let uiRoot = ctx.uiRoot as entity.DbEntityList;
  let { appContext, pageContext } = uiRoot.props;
  if (bill.vendorBillId == null) return;
  const successCB = (vendorBill: any) => {
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <UIVendorBillInfo appContext={appCtx} pageContext={pageCtx} vendorBill={vendorBill} />
      )
    }
    pageContext.createPopupPage('vendor-bill', T('Vendor Bill'), createAppPage, { size: 'xl' });
  }
  appContext
    .createHttpBackendCall('TMSVendorBillService', 'loadVendorBillInfo', { vendorBillId: bill.vendorBillId })
    .withSuccessData(successCB)
    .call();

}

export function renderTrackingButton(
  _ctx: grid.VGridContext, bill: any, config?: { btnSize: number, iconSize: number, cssClass?: any }) {
  if (bill.roundUsedStatus) return;
  let vendorBillId = bill.vendorBillId;
  if (bill['trackings']) return renderGoodsTrackingButton(_ctx, bill, config);
  if (vendorBillId) return renderVendorTrackingButton(_ctx, bill, config);
  return;
}

export function renderOpsStatus(bill: any, config?: { iconSize: number, fontSize: number, cssClass?: any }) {
  if (!bill['opsId'] && !bill['vendorBillSendOps']) return;
  let btnSize = 15;
  let fontSize = 11;
  let cssClass = '';
  if (config) {
    btnSize = config.iconSize;
    fontSize = config.fontSize;
    cssClass = config.cssClass;
  }
  let opsStatus = bill['opsId'] ? bill['opsStatus'] : bill['vendorOpsStatus'];
  let summaryOpsInfo = bill['opsId'] ? bill['summaryOpsInfo'] : bill['summaryVendorOpsInfo'];
  let opsNote = bill['opsId'] ? bill['opsNote'] : bill['vendorOpsNote'];
  let onShowInfo = () => {
    let content = (
      <div>
        {summaryOpsInfo}
        <div>
          {`${opsNote ? 'Note: ' + opsNote : ''}`}
        </div>
      </div>

    )
    bs.dialogShow(T(`OPS INFO: ${opsStatus}`), content);
  }
  let borderRadius = bill['opsId'] ? 10 : 3;
  if (opsStatus === 'NEED_CONFIRM') {
    return (
      <div className={`flex-grow-0 flex-hbox align-items-center ${cssClass}`}>
        <bs.Button laf='danger' style={{ height: btnSize, width: btnSize, fontSize: fontSize, borderRadius: borderRadius }}
          className='p-0' onClick={onShowInfo}>
          <bs.Tooltip tooltip={`OPS Need Confirm:${summaryOpsInfo}`}>
            {'C'}
          </bs.Tooltip>
        </bs.Button>
      </div>
    )
  }
  if (opsStatus === 'PROCESSING') {
    return (
      <div className={`flex-grow-0 flex-hbox align-items-center ${cssClass}`}>
        <bs.Button laf='warning' style={{ height: btnSize, width: btnSize, fontSize: fontSize, borderRadius: borderRadius, backgroundColor: '#FFC125' }}
          className='p-0' onClick={onShowInfo}>
          <bs.Tooltip tooltip={`OPS Processing:${summaryOpsInfo}`}>
            {'P'}
          </bs.Tooltip>
        </bs.Button>
      </div>
    )
  }
  if (opsStatus === 'DONE') {
    return (
      <div className={`flex-grow-0 flex-hbox align-items-center ${cssClass}`}>
        <bs.Button laf='success' style={{ height: btnSize, width: btnSize, fontSize: fontSize, borderRadius: borderRadius }}
          className='p-0' onClick={onShowInfo}>
          <bs.Tooltip tooltip={`OPS Done:${summaryOpsInfo}`}>
            {'D'}
          </bs.Tooltip>
        </bs.Button>
      </div>
    )
  }
  return;
}

export function renderRoundUsedStatus(ctx: grid.VGridContext, bill: any, config?: { iconSize: number, fontSize: number, cssClass?: any }) {
  let uiRoot = ctx.uiRoot as entity.DbEntityList;
  let roundUsedStatus: TMSRoundUsedStatus = bill.roundUsedStatus;
  if (!roundUsedStatus) return;
  let onUpdateStatus = (newStatus: TMSRoundUsedStatus) => {
    let { appContext } = uiRoot.props;
    appContext.createHttpBackendCall('TMSRoundUsedService', 'updateTMSRoundUsedStatus', { id: bill.roundUsedId, status: newStatus })
      .withSuccessData((_data: any) => {
        bill.roundUsedStatus = newStatus;
        appContext.addOSNotification("success", T(`${newStatus} Round Used Success`));
        ctx.getVGrid().forceUpdateView();
      })
      .call();
  }
  let btnHeight = 15;
  let btnWidth = 18;
  let fontSize = 12;
  let cssClass = '';
  if (config) {
    btnHeight = config.iconSize;
    btnWidth = config.iconSize;
    fontSize = config.fontSize;
    cssClass = config.cssClass;
  }

  let bgCssClass = TMSRoundUsedStatusTools.getBgColor(roundUsedStatus);
  if (roundUsedStatus === TMSRoundUsedStatus.SendingFromRu) {
    return (
      <bs.Popover flex-hbox-grow-0 closeOnTrigger=".btn" placement='left' className='flex-hbox-grow-1'>
        <bs.PopoverToggle className={`p-0 ${bgCssClass}`} style={{ height: 15, width: 18, fontSize: 11, borderRadius: 3 }}>
          <bs.Tooltip tooltip={`Round Used: ${roundUsedStatus}`} className='text-white fw-bold text-center'>
            {'RU'}
          </bs.Tooltip>
        </bs.PopoverToggle>
        <bs.PopoverContent>
          <div className={`flex-hbox align-items-center ${cssClass}`}>
            <bs.Button laf='success' style={{ height: btnHeight, width: btnWidth, fontSize: fontSize, borderRadius: 5 }}
              className='p-2 mx-1' onClick={() => onUpdateStatus(TMSRoundUsedStatus.Processing)}>
              {'Confirm'}
            </bs.Button>
            <bs.Button laf='danger' style={{ height: btnHeight, width: btnWidth, fontSize: fontSize, borderRadius: 5 }}
              className='p-2 mx-1' onClick={() => onUpdateStatus(TMSRoundUsedStatus.Cancel)}>
              {'Reject'}
            </bs.Button>
          </div>
        </bs.PopoverContent>
      </bs.Popover >
    )
  }
  return (
    <div className={`flex-grow-0 flex-hbox align-items-center ${cssClass}`}>
      <bs.Button laf='primary' style={{ height: btnHeight, width: btnWidth, fontSize: fontSize, borderRadius: 3 }}
        className={`p-0 ${bgCssClass}`}>
        <bs.Tooltip tooltip={`Round Used: ${roundUsedStatus}`} className='text-white fw-bold text-center'>
          {'RU'}
        </bs.Tooltip>
      </bs.Button>
    </div>
  );
}

export function createTMSBillRecordConfig(uiRoot: UITMSBillList, allowSelector: boolean): grid.RecordConfig {
  let { appContext, pageContext, plugin, type, readOnly, jobTrackingProject } = uiRoot.props;
  const writeCap = pageContext.hasUserWriteCapability();
  const modCap = pageContext.hasUserModeratorCapability();


  const onCalculateDelayedTime = (bill: any) => {
    let sDateTime = bill['dateTime'];
    let ddmmyy = util.text.formater.compactDate(sDateTime);
    let estimateTime = bill['estimateTime'];
    let time = bill['time'];
    if (sDateTime && isValidTimeFormat(estimateTime) && isValidTimeFormat(time)) {
      let dateTime = util.TimeUtil.parseCompactDateTimeFormat(`${ddmmyy}@${time}:00+0700`);
      let estimateDateTime = util.TimeUtil.parseCompactDateTimeFormat(`${ddmmyy}@${estimateTime}:00+0700`);
      let estimateDeliveryMinutes = estimateDateTime.getHours() * 60 + estimateDateTime.getMinutes();
      let planDeliveryMinutes = dateTime.getHours() * 60 + dateTime.getMinutes();
      let delayedTime = estimateDeliveryMinutes - planDeliveryMinutes;
      bill['delayedTime'] = delayedTime;
    }
  }

  let onCalculateArrivedOnTime = (bill: any) => {
    if (bill['delayedTime'] <= 0) {
      bill['vehicleArrivedOnTime'] = 2;
    } else if (bill['delayedTime'] <= 30) {
      bill['vehicleArrivedOnTime'] = 1;
    } else {
      bill['vehicleArrivedOnTime'] = 0;
    }
  }

  let aggSumCell = (cell: grid.VGridCell, event: grid.VGridCellEvent, field: any) => {
    let bucketId = cell.getDisplayRecord().bucketId;
    let dRecord: grid.DisplayRecord = event.data;
    if (!bucketId || bucketId != dRecord.bucketId) return;
    let aggModel = plugin.getListModel().getDisplayRecordList() as grid.AggregationDisplayModel;
    let bucket = aggModel.getRootBucket().findBucketById(bucketId);
    if (bucket) {
      let record = cell.getDisplayRecord().record;
      let records = bucket.records;
      let aggSum = util.CollectionMath.sum(records, [field]);
      record[field] = aggSum[field];
      cell.forceUpdate();
    }
  }

  let sumFooter = (record: any) => {
    let fields = [
      'quantity', 'weight', 'fixedPayment', 'extraPayment', 'totalPayment',
      'vendorFixed', 'vendorExtra', 'vendorCost', 'profit'
    ];
    let records = plugin.getListModel().getSelectedRecords();
    let sum = util.CollectionMath.sum(records, fields);
    let volume = 0;
    for (let rec of records) {
      let volumeAsText = rec['volumeAsText'];
      if (!volumeAsText) continue;
      if (TMSUtils.isDecimal(volumeAsText)) volume += Number(volumeAsText)
    }
    record['volumeAsText'] = volume;
    for (let fieldName of fields) {
      record[fieldName] = sum[fieldName];
    }
    record['label'] = `Total Selected (${records.length})`;
    uiRoot.getVGridContext().getVGrid().forceUpdateView();
  }

  let items: Array<grid.RecordAction> = [];
  items.push(
    {
      name: 'add', hint: 'Add', icon: FeatherIcon.Plus,
      customRender: (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
        let create = (_ctx: grid.VGridContext, atRecord: grid.DisplayRecord) => {
          return UITMSBillUtils.cloneBill(atRecord.record);
        }
        let allowInsert = (_ctx: grid.VGridContext, atRecord: grid.DisplayRecord) => {
          let recState = atRecord.getRecordState(false);
          if (!recState) return false;
          if (recState.isMarkDeleted()) {
            return false;
          }
          return true;
        }
        return (<WTMSGridInsertRow style={{ marginRight: 5 }} key={'add'} color='link' context={ctx} row={dRecord.row}
          createInsertRecord={create} allowInsert={allowInsert} />);
      },
    }
  );
  items.push(
    {
      name: 'del', hint: 'Delete', icon: FeatherIcon.Trash2,
      onClick: (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
        let bill = dRecord.record;
        const onConfirm = () => {
          ctx.model.getDisplayRecordList().toggleDeletedDisplayRecord(dRecord.row);
          if (dRecord.getRecordState().isMarkDeleted()) {
            ctx.model.getDisplayRecordList().markSelectDisplayRecord(dRecord.row);
            bill['editState'] = 'DELETED';
          } else {
            dRecord.getRecordState().selected = false;
            delete bill['editState'];
          }
          ctx.getVGrid().forceUpdateView();
        }
        bs.dialogConfirmMessage('Delete Confirm', 'Are you sure delete???', onConfirm);
      },
    },
  );

  let _onDeliveryPlanCellEvent = (field: grid.FieldConfig, cell: grid.VGridCell) => {
    let deliveryPlan = cell.getDisplayRecord().getValue('dateTime');
    if (deliveryPlan && field.name == 'dateTime') {
      let dateTime: Date = util.TimeUtil.parseCompactDateTimeFormat(deliveryPlan);
      let dateNow: Date = new Date();
      if ((dateTime.getFullYear() < dateNow.getFullYear()) || (dateTime.getFullYear() > dateNow.getFullYear() + 1)) {
        let content = (
          <div>
            <div>
              <span>You are creating a different year file</span>
              (<span className='fw-bold'>{dateTime.getFullYear()}</span>).
            </div>
            <div>
              <span>
                You want to update to
              </span>
              <span className='fw-bold px-1'>
                {dateNow.getFullYear()}
              </span>
            </div>
          </div>
        )
        bs.dialogConfirmMessage('Confirm delivery plan', content, () => {
          dateTime.setFullYear(dateNow.getFullYear());
          cell.getDisplayRecord().record['dateTime'] = util.TimeUtil.javaCompactDateTimeFormat(dateTime);
          cell.forceUpdate();
        });
      }
    }
  }

  const _onChangeCost = (field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
    let billCostFields = ['fixedPayment', 'extraPayment'];
    if (billCostFields.includes(field.name)) {
      dRecord.record[field.name] = dRecord.record[field.name] * uiRoot.multiplierBean['val'];
    }
  }
  const _onChangeTime = (field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
    if (field.name === 'time' || field.name === 'estimateTime') {
      onCalculateDelayedTime(dRecord.record);
      onCalculateArrivedOnTime(dRecord.record);
    }
  }

  const onInputChange = (fieldCtx: grid.FieldContext, oldVal: any, newVal: any) => {
    let dRecord = fieldCtx.displayRecord;
    let field = fieldCtx.fieldConfig;
    let ctx = fieldCtx.gridContext;
    let event: grid.VGridCellEvent = {
      row: dRecord.row, field: field, event: 'Modified', data: dRecord
    }
    _onChangeTime(field, dRecord);
    _onChangeCost(field, dRecord);
    if (field.name === 'customerFullName') _onLoadCustomerAddress(ctx, dRecord.record, true);
    if (field.name === 'mode') {
      if (TMSBillTransportationModeTools.type(oldVal) != TMSBillTransportationModeTools.type(newVal)) {
        _onLoadCustomerAddress(ctx, dRecord.record, true);
      } else {
        _onLoadCustomerAddress(ctx, dRecord.record);
      }
    }
    if (field.name === 'truckNo' || field.name === 'licensePlate') {
      dRecord.record['updateVehicleInfo'] = true;
    }
    ctx.broadcastCellEvent(event);
  };

  const _updateSenderAddress = (bill: any, address: any) => {
    bill['senderAddress'] = address['address'];
    bill['senderLocationId'] = address['locationId'];
    bill['senderLocationAddress'] = address['locationAddress'];
    bill['senderStreetName'] = address['streetName'];
  }

  const _updateReceiverAddress = (bill: any, address: any) => {
    bill['receiverAddress'] = address['address'];
    bill['receiverLocationId'] = address['locationId'];
    bill['receiverStreetName'] = address['streetName'];
  }

  const _onLoadCustomerAddress = (ctx: grid.VGridContext, bill: any, overrideAddress: boolean = false) => {
    if (!bill['customerId']) return;
    let mode = bill['mode'];
    let sqlParams = {
      params: {
        'customerId': bill['customerId'],
      }
    }

    appContext.createHttpBackendCall('TMSPartnerService', 'searchPartnerAddresses', { params: sqlParams })
      .withSuccessData((addresses: Array<any>) => {
        if (addresses.length == 0) return;
        const pickupAddresses: Array<any> = addresses.filter(sel => sel['type'] == 'Export' || sel['type'] == 'None');
        const deliveryAddresses: Array<any> = addresses.filter(sel => sel['type'] == 'Import' || sel['type'] == 'None');
        if (TMSBillTransportationModeTools.isExport(mode)) {
          if (bill['senderLocationId'] && !overrideAddress) return;
          if (pickupAddresses.length == 1) {
            let first = pickupAddresses[0];
            _updateSenderAddress(bill, first);
          } else {
            const createContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              return (<BBOptionAddress
                appContext={appCtx} pageContext={pageCtx} addresses={pickupAddresses} onSelectOptions={(address) => {
                  _updateSenderAddress(bill, address);
                  pageCtx.back();
                  ctx.getVGrid().forceUpdateView();
                }}
              />)
            }
            pageContext.createPopupPage('', 'Choose Address', createContent);
          }
          return;
        }
        if (TMSBillTransportationModeTools.isImport(mode)) {
          if (bill['receiverLocationId'] && !overrideAddress) return;
          if (deliveryAddresses.length == 1) {
            let first = deliveryAddresses[0];
            _updateReceiverAddress(bill, first);
          } else {
            const createContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              return (<BBOptionAddress
                appContext={appCtx} pageContext={pageCtx} addresses={deliveryAddresses} onSelectOptions={(address) => {
                  _updateReceiverAddress(bill, address);
                  pageCtx.back();
                  ctx.getVGrid().forceUpdateView();
                }}
              />)
            }
            pageContext.createPopupPage('', 'Choose Address', createContent)
          }
          return;
        }

        if (pickupAddresses.length == 1 && overrideAddress) {
          let first = pickupAddresses[0];
          _updateSenderAddress(bill, first);
        }
        if (deliveryAddresses.length == 1 && overrideAddress) {
          let first = deliveryAddresses[0];
          _updateReceiverAddress(bill, first);
        }
      })
      .call();
  }

  let controlWidth = bs.ScreenUtil.isSmallScreen() ? 25 : 45;
  let collectBills: Array<any> = [];

  let records: grid.RecordConfig = {
    computeDataRowHeight: (_ctx: grid.VGridContext, dRec: grid.DisplayRecord) => {
      let rec = dRec.record;
      if (dRec.isDataRecord()) {
        let stopLocations = rec['stopLocations'];
        if (stopLocations && stopLocations.length > 1) {
          let stopCount = stopLocations.length;
          let height = stopCount * 25;
          return height;
        }
        return 35;
      }
      return 20;
    },
    control: {
      width: controlWidth,
      items: items,
    },
    editor: {
      supportViewMode: ['aggregation', 'table'],
      enable: true || type === 'page'
    },
    fields: [
      ...entity.DbEntityListConfigTool.FIELD_SELECTOR(allowSelector),
      {
        ...entity.DbEntityListConfigTool.FIELD_INDEX(), width: 50,
        computeCssClasses: (_ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
          if (!dRecord.isDataRecord()) return '';
          if (!dRecord.getValue('id')) {
            return 'bg-success bg-opacity-25'
          }
          return '';
        },
        customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
          if (!dRecord.isDataRecord()) return;
          let _state = dRecord.getRecordState();
          let icon;
          if (_state.isMarkDeleted()) {
            icon = <FeatherIcon.Trash2 size={14} className='text-danger mx-1' />
          } else if (_state.isMarkModified()) {
            icon = <FeatherIcon.Edit size={14} className='text-warning mx-1' />
          }
          return (
            <div className='flex-hbox align-items-center'>
              {icon}
              <div>
                {dRecord.getDisplayRow()}
              </div>
            </div>
          )
        },
        listener: {
          onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
            let dRec = cell.getDisplayRecord();
            if (event.row == cell.getRow()) {
              dRec.getRecordState().markModified();
              cell.forceUpdate()
            }
          },
        },
      },
      /** @TMSBill_Info */
      {
        name: 'label', label: T('File No.'), width: 150, filterableType: 'string', filterable: true, container: 'fixed-left',
        editor: {
          type: 'string', onInputChange: onInputChange
        },
        computeCssClasses: (ctx, dRecord) => {
          let bill = dRecord.record;
          let collectBill = bill['collect'];
          if (!collectBill) return '';
          let cssClass = '';
          let id = 0;
          if (!collectBills.includes(collectBill)) collectBills.push(collectBill);
          collectBills.forEach((value, index) => {
            if (value === collectBill) {
              id = index;
            }
          });
          if (id % 2 == 0) return `${cssClass} text-success`;
          return `${cssClass} text-warning`;
        },
        listener: {
          onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
            if (event.row == cell.getRow()) {
              cell.forceUpdate()
            }
          },
        },
        customRender(ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) {
          if (!dRecord.isDataRecord()) {
            if (dRecord.type == 'footer') {
              return (
                <bs.Button laf='link' onClick={() => sumFooter(dRecord.record)}>
                  {dRecord.record[field.name]}
                </bs.Button>
              )
            }
            return;
          }
          let bill = dRecord.record;
          let cssClass = field.cssClass;
          if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(ctx, dRecord)}`;
          return <div className='flex-hbox align-items-center'>
            < FeatherIcon.Copy className='mx-1 my-2 text-warning' style={{ cursor: 'pointer' }} size={12}
              onClick={() => {
                navigator.clipboard.writeText(bill[field.name]);
                appContext.addOSNotification('success', 'Copy success')
              }
              } />
            <button type="button" className="btn btn-link">
              <div className={cssClass} onClick={() => { uiRoot.onSelect(dRecord); }} > {bill[field.name]}</div >
            </button>
          </div >
        },
      },
      {
        name: 'hwbNo', label: T('HWB No'), container: 'fixed-left',
        editor: {
          type: 'string',
          onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            const { displayRecord, fieldConfig, tabIndex, focus } = fieldCtx;
            let bill = displayRecord.record;
            let verifyHblNoIcon = null;
            let verifyHblNo = bill['verifyHblNo'];
            if (bill['verifyHblNo']) {
              verifyHblNoIcon = (
                <FeatherIcon.CheckCircle className='text-success mx-1' size={14} />
              )
            } else if (verifyHblNo === false) {
              verifyHblNoIcon = (
                <FeatherIcon.XCircle className='text-danger mx-1' size={14} />
              )
            }
            let width = fieldConfig.width ? fieldConfig.width : 120;
            return (
              <div className='flex-hbox justify-content-center '>
                <input.BBStringField style={{ width: width - 25 }}
                  bean={bill} field={fieldConfig.name} tabIndex={tabIndex} focus={focus}
                  onInputChange={onInputChange} />
                <div className='100vh'>
                  {verifyHblNoIcon}
                </div>
              </div>
            );
          }
        }
      },
      {
        name: 'verifyHblNo', label: T('V.HblNo'), filterable: true, filterableType: 'options', width: 120, sortable: true
      },
      {
        name: 'customerFullName', label: T('Customer'), width: 170, cssClass: 'px-1', sortable: true, container: 'fixed-left',
        dataTooltip: true, filterableType: 'Options', filterable: true,
        computeCssClasses: (_ctx, dRecord) => {
          return dRecord.record['customerId'] == null ? 'text-warning' : '';
        },
        listener: {
          onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
            if (event.row == cell.getRow() && event.field.name === 'customerFullName') {
              cell.forceUpdate()
            }
          },
        },
        editor: {
          type: 'string',
          onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            let field = fieldCtx.fieldConfig;
            let dRecord = fieldCtx.displayRecord;
            let tabIndex = fieldCtx.tabIndex;
            let focus = fieldCtx.focus;
            let bill = dRecord.record;
            const oldVal = `${bill[field.name]}-${bill['customerId']}`;
            let cssClass = field.cssClass;
            if (field.computeCssClasses) cssClass = field.computeCssClasses(uiRoot.getVGridContext(), dRecord);
            return (
              <BBRefTMSCustomer allowUserInput minWidth={400}
                className={cssClass} appContext={appContext} pageContext={pageContext} tabIndex={tabIndex} autofocus={focus}
                bean={bill} beanIdField={'customerId'} beanLabelField={'customerFullName'} placeholder={'Customer'}
                onPostUpdate={(_inputUI: React.Component, bean: any, _selectOpt: any, _userInput: string) => {
                  if (_selectOpt) {
                    bill['payOnBehalfCustomerId'] = _selectOpt['id'];
                    bill['payOnBehalfCustomerName'] = _selectOpt['shortName'];
                  }
                  if (bill['senderLocationId']) {
                    bill['senderAddress'] = null;
                    bill['senderLocationId'] = null;
                    bill['senderLocationAddress'] = null;
                  }
                  if (bill['receiverLocationId']) {
                    bill['receiverAddress'] = null;
                    bill['receiverLocationId'] = null;
                    bill['receiverLocationAddress'] = null;
                  }
                  onInputChange(bean, field.name, oldVal, `${bill[field.name]}-${bill['customerId']}`);
                }}
              />
            )
          },
        },
      },
      {
        name: 'editCus', label: '', container: 'fixed-left', width: 40, cssClass: 'p-0', state: { visible: false },
        customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
          if (!dRecord.isDataRecord()) return;
          let record = dRecord.record;
          let onPostCommit = (partner: any, actions: entity.ModifyBeanActions) => {
            if (actions === entity.ModifyBeanActions.CREATE) {
              appContext
                .createHttpBackendCall('TMSCustomerService', 'loadTMSCustomerByPartnerId', { partnerId: partner.id })
                .withSuccessData((customer: any) => {
                  if (customer) record['customerId'] = customer['id'];
                  record['customerFullName'] = partner['shortName'];
                  onSaveTMSBillModified(ctx, [dRecord]);
                })
                .call();
            }
          }
          return (
            <div className={'flex-hbox'}>
              {renderBtnEditTMSCustomer(ctx, record['customerId'], record['customerFullName'], onPostCommit)}
            </div>
          )
        },
      },
      {
        name: 'dateTime', label: T('Date'), width: 100, cssClass: 'flex-grow-1 text-end', container: 'fixed-left',
        sortable: true, filterableType: 'date', filterable: true, format: compactDate,
        listener: {
          onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
            if (event.row == cell.getRow() && event.field.name === 'dateTime') {
              _onDeliveryPlanCellEvent(event.field, cell);
              cell.forceUpdate();
            }
          },
        },
        editor: {
          type: 'date',
          onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            let field = fieldCtx.fieldConfig;
            let dRecord = fieldCtx.displayRecord;
            let tabIndex = fieldCtx.tabIndex;
            let focus = fieldCtx.focus;
            let bill = dRecord.record;
            let cssClass = field.cssClass;
            if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiRoot.getVGridContext(), dRecord)}`;
            return (
              <input.BBDateInputMask className={cssClass}
                bean={bill} field={field.name} tabIndex={tabIndex} focus={focus} format={"DD/MM/YYYY"}
                onInputChange={onInputChange} />
            );
          },
        }
      },
      {
        name: 'time', label: T('Time'), width: 60, container: 'fixed-left',
        filterableType: 'options', filterable: true,
        editor: {
          type: 'string',
          onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            let field = fieldCtx.fieldConfig;
            let dRecord = fieldCtx.displayRecord;
            let tabIndex = fieldCtx.tabIndex;
            let focus = fieldCtx.focus;
            let bill = dRecord.record;
            let cssClass = field.cssClass;
            if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiRoot.getVGridContext(), dRecord)}`;
            return (
              <input.BBTimeInputMask
                className={cssClass} bean={bill} field={field.name} tabIndex={tabIndex} focus={focus}
                onInputChange={onInputChange} />
            );
          },
        }
      },
      {
        name: 'estimateTime', label: T('E.Time'), hint: T('Estimate Time'), width: 80, container: 'fixed-left',
        filterableType: 'options', filterable: true,
        editor: {
          type: 'string',
          onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            let field = fieldCtx.fieldConfig;
            let dRecord = fieldCtx.displayRecord;
            let tabIndex = fieldCtx.tabIndex;
            let focus = fieldCtx.focus;
            let bill = dRecord.record;
            let vendorEstimateTime = bill['vendorEstimateTime'];
            let time = bill['time'];
            let sDateTime = bill['dateTime'];
            let readOnly = false;
            if (!isValidTimeFormat(time)) time = '00:00';
            if (sDateTime) {
              sDateTime = sDateTime.replace(/\d{2}:\d{2}:\d{2}/, `${time}:00`)
              let dateTime = util.TimeUtil.parseCompactDateTimeFormat(sDateTime);
              let dateRange = new util.TimeRange();
              dateRange.fromSetDate(dateTime);
              dateRange.toSetDate(new Date());
              let diffM = dateRange.diff('minutes');
              if (diffM > 15) readOnly = true;
            }
            if (vendorEstimateTime) {
              let date: Date = util.TimeUtil.parseCompactDateTimeFormat(vendorEstimateTime);
              let time = `${date.getHours().toString().padStart(2, "0")}:${date.getMinutes().toString().padStart(2, "0")}`
              return (
                <div className='text-success'>{time}</div>
              )
            }
            let cssClass = field.cssClass;
            if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiRoot.getVGridContext(), dRecord)}`;
            return (
              <input.BBTimeInputMask className={cssClass} bean={bill} field={field.name} tabIndex={tabIndex} focus={focus}
                onInputChange={onInputChange} disabled={readOnly && !pageContext.hasUserModeratorCapability()}
              />
            );
          },
        }
      },
      {
        name: 'payOnBehalfCustomerName', label: T('To Inv Company'),
        width: 150, cssClass: 'px-1', dataTooltip: true, filterableType: 'Options', filterable: true,
        listener: {
          onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
            if (event.row == cell.getRow() && event.field.name === 'customerFullName') {
              cell.forceUpdate()
            }
          },
        },
        editor: {
          type: 'string',
          onInputChange: onInputChange,
          renderCustom(ctx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            let bill = ctx.displayRecord.record;
            const oldVal = `${bill['payOnBehalfCustomerId']}-${bill['payOnBehalfCustomerFullName']}`;
            return (
              <BBRefTMSCustomer key={`pay-on-behalf-${bill['payOnBehalfCustomerId']}`} allowUserInput
                minWidth={650} placeholder={'To Invoice Company'} showTaxCode
                appContext={appContext} pageContext={pageContext} tabIndex={ctx.tabIndex} autofocus={ctx.focus}
                bean={bill} beanIdField={'payOnBehalfCustomerId'} beanLabelField={'payOnBehalfCustomerName'}
                onPostUpdate={(_inputUI: React.Component, bean: any, _selectOpt: any, _userInput: string) => {
                  onInputChange(bean, ctx.fieldConfig.name, oldVal, `${bill[ctx.fieldConfig.name]}-${bill['customerId']}`);
                }}
              />
            )
          },
        },
      },
      {
        name: 'modifiedEstimateTime', label: T('Modified E.Time'), width: 155, cssClass: 'flex-grow-1 text-end',
        format: compactDateTime, state: { visible: false },
        fieldDataGetter: (record) => {
          return record['vendorModifiedEstimateTime'] ? record['vendorModifiedEstimateTime'] : record['modifiedEstimateTime'];
        },
        computeCssClasses(ctx, dRecord) {
          let bill = dRecord.record;
          let sDateTime = bill['dateTime'];
          let ddmmyy = util.text.formater.compactDate(sDateTime);
          let time = bill['time'];
          if (!sDateTime || !isValidTimeFormat(time)) return "text-danger";
          let dateTime = util.TimeUtil.parseCompactDateTimeFormat(`${ddmmyy}@${time}:00+0700`);
          let modifiedEstimateTime = util.TimeUtil.parseCompactDateTimeFormat(bill['modifiedEstimateTime']);
          if (bill['vendorModifiedEstimateTime']) modifiedEstimateTime = util.TimeUtil.parseCompactDateTimeFormat(bill['vendorModifiedEstimateTime']);
          let checkTime = dateTime.getTime() < modifiedEstimateTime.getTime();
          if (checkTime) return "text-warning";
          return "";
        },
      },
      {
        name: 'delayedTime', label: T('Delayed'), hint: T('Delayed Time'), state: { visible: false },
        cssClass: 'flex-grow-1 text-end', width: 100, sortable: true,
        customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
          let record = dRecord.record;
          let delayedTime = record['vendorDelayedTime'] ? record['vendorDelayedTime'] : record[field.name];
          if (!delayedTime) return;
          let hours = ~~(delayedTime / 60);
          let minutes = delayedTime % 60;
          let html;
          if (delayedTime > 0) {
            html = (
              <div className='text-warning'>{`${hours > 0 ? hours + 'h' : ''}${minutes ? String(minutes).padStart(2, '0') + '\'' : ''} late`}</div>
            )
          } else {
            hours = hours * -1;
            minutes = minutes * -1;
            html = (
              <div>{`${hours > 0 ? hours + 'h' : ''}${minutes ? String(minutes).padStart(2, '0') + '\'' : ''} early`}</div>
            )
          }
          return (
            <div className={field.cssClass} >
              {html}
            </div>
          )
        },
        listener: {
          onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
            if (event.row == cell.getRow() && (event.field.name === 'time' || event.field.name === 'estimateTime')) {
              cell.forceUpdate()
            }
          },
        },
      },
      {
        name: "responsibleFullName", label: T('PIC.'), width: 120, cssClass: 'pe-1', filterableType: 'options', filterable: true,
        customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
          let record = dRecord.record;
          let uiList = ctx.uiRoot as entity.DbEntityList
          const { appContext, pageContext } = uiList.props;
          return (
            <div className='flex-hbox justify-content-center align-items-center' >
              <module.account.WAvatars className='px-2'
                appContext={appContext} pageContext={pageContext} avatarIds={[record['responsibleAccountId']]}
                avatarIdType='AccountId' width={20} height={20} borderRadius={10} />
              <bs.Tooltip tooltip={record['responsibleFullName']} className="flex-hbox">
                {record['userName'] ? record['userName'] : record['responsibleFullName']}
              </bs.Tooltip>
            </div>
          )
        }
      },
      {
        name: 'office', label: T('Office'), width: 80, hint: 'Office', filterableType: 'Options', filterable: true,
        editor: {
          type: 'string', onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            let field = fieldCtx.fieldConfig;
            let dRecord = fieldCtx.displayRecord;
            let tabIndex = fieldCtx.tabIndex;
            let focus = fieldCtx.focus;
            let forwarder = dRecord.record;
            let cssClass = field.cssClass;
            if (field.computeCssClasses) cssClass = field.computeCssClasses(uiRoot.getVGridContext(), dRecord);
            return (
              < input.BBOptionAutoComplete tabIndex={tabIndex} autofocus={focus} className={cssClass} allowUserInput
                bean={forwarder} field={field.name}
                options={['BEEHPH', 'BEEHAN', 'BEEHCM', 'BEEDAD', 'BEELS', 'BEEND', 'BEETH', 'BEEHNA', 'BEENA', 'BEEHD', 'MARINE']}
                onInputChange={onInputChange} />
            )
          },
        }
      },
      /** @TMSBill_Forwarder */
      {
        name: 'truckType', label: T('Truck Type'), cssClass: 'justify-content-center ',
        filterableType: 'options', filterable: true,
        editor: {
          type: 'string',
          onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            let field = fieldCtx.fieldConfig;
            let dRecord = fieldCtx.displayRecord;
            let tabIndex = fieldCtx.tabIndex;
            let focus = fieldCtx.focus;
            let forwarder = dRecord.record;
            const oldVal = forwarder[field.name];
            let cssClass = field.cssClass;
            if (field.computeCssClasses) cssClass = field.computeCssClasses(uiRoot.getVGridContext(), dRecord);
            return (
              <module.settings.BBRefUnit
                className={cssClass}
                appContext={appContext} pageContext={pageContext}
                minWidth={300}
                placement="left"
                placeholder="Enter Unit"
                groupNames={['truck']}
                bean={forwarder} beanIdField={field.name}
                tabIndex={tabIndex} autofocus={focus}
                onPostUpdate={(inputUI, bean, selectOpt, userInput) => onInputChange(bean, field.name, oldVal, bean[field.name])} />
            )
          },
        }
      },
      {
        name: 'mode', label: T('Mode'), width: 100, filterableType: 'options', filterable: true, sortable: true,
        computeCssClasses(ctx, dRecord) {
          return TMSBillTransportationModeTools.getColor(dRecord.record.mode);
        },
        fieldDataGetter(record) {
          return TMSBillTransportationModeTools.getLabel(record.mode);
        },
        listener: {
          onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
            if (event.row == cell.getRow() && event.field.name === 'mode') {
              cell.forceUpdate()
            }
          },
        },
        editor: {
          type: 'string',
          onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            let field = fieldCtx.fieldConfig;
            let dRecord = fieldCtx.displayRecord;
            let tabIndex = fieldCtx.tabIndex;
            let focus = fieldCtx.focus;
            let forwarder = dRecord.record;
            let cssClass = field.cssClass;
            if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiRoot.getVGridContext(), dRecord)}`;
            let onChange = (bean: any, field: string, oldVal: any, newVal: any) => {
              let oldValIsExport = TMSBillTransportationModeTools.isExport(oldVal);
              let newValIsExport = TMSBillTransportationModeTools.isExport(newVal);
              let oldValIsImport = TMSBillTransportationModeTools.isImport(oldVal);
              let newValIsImport = TMSBillTransportationModeTools.isImport(newVal);
              let isDomestic = TMSBillTransportationModeTools.isDomestic(newVal);
              if (!(oldValIsExport == newValIsExport || oldValIsImport === newValIsImport || isDomestic)) {
                const receiverContact = forwarder['receiverContact'];
                const senderContact = forwarder['senderContact'];

                forwarder['receiverContact'] = senderContact;
                forwarder['senderContact'] = receiverContact;
                forwarder['receiverAddress'] = null;
                forwarder['receiverLocationId'] = null;
                forwarder['receiverLocationAddress'] = null;
                forwarder['receiverLocationShortLabel'] = null;
                forwarder['senderAddress'] = null;
                forwarder['senderLocationId'] = null;
                forwarder['senderLocationAddress'] = null;
                forwarder['senderLocationShortLabel'] = null;
              }
              if (onInputChange) onInputChange(bean, field, oldVal, newVal);
            }
            return (
              <input.BBSelectField className={cssClass}
                bean={forwarder} field={field.name} tabIndex={tabIndex} focus={focus}
                options={[
                  TMSBillTransportationMode.ExportFcl, TMSBillTransportationMode.ExportLcl, TMSBillTransportationMode.ExportAir,
                  TMSBillTransportationMode.ImportFcl, TMSBillTransportationMode.ImportLcl, TMSBillTransportationMode.ImportAir,
                  TMSBillTransportationMode.Domestic,
                  TMSBillTransportationMode.CBT
                ]}
                optionLabels={[
                  'Export Fcl', 'Export Lcl', 'Export Air',
                  'Import Fcl', 'Import Lcl', 'Import Air',
                  'Domestic', 'CBT'
                ]} onInputChange={onChange} />
            )
          },
        }
      },
      {
        name: 'goodsDescription', label: T('Commodity'), width: 120, dataTooltip: true,
        filterableType: 'string', filterable: true,
        editor: { type: 'string', onInputChange: onInputChange, }
      },
      {
        name: 'containerNo', label: T('Container No'),
        customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
          let record = dRecord.record;
          let containerNo = record[field.name];
          let width = field.width ? field.width : 120;
          let cssClass = field.cssClass;
          if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(ctx, dRecord)}`;
          return (
            <div className={`flex-hbox`}>
              <bs.Tooltip className={`flex-grow-1 text-truncate ${cssClass}`} style={{ width: width - 25 }} tooltip={containerNo}>
                {containerNo}
              </bs.Tooltip>
              {UITMSBillUtils.containerValidate(containerNo)}
            </div>
          )
        },
        editor: {
          type: 'string', onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            let field = fieldCtx.fieldConfig;
            let dRecord = fieldCtx.displayRecord;
            let tabIndex = fieldCtx.tabIndex;
            let focus = fieldCtx.focus;
            let bill = dRecord.record;
            let containerNo = bill[field.name];
            let cssClass = field.cssClass;
            if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiRoot.getVGridContext(), dRecord)}`;
            return (
              <div className={`flex-hbox`}>
                <input.BBStringField className={cssClass}
                  bean={bill} field={field.name} tabIndex={tabIndex} focus={focus} onInputChange={onInputChange} />
                {UITMSBillUtils.containerValidate(containerNo)}
              </div>
            )
          },
        }
      },
      { name: 'sealNo', label: T('Seal No'), editor: { type: 'string', onInputChange: onInputChange, } },
      {
        name: 'quantity', label: T('Quantity'), width: 80,
        listener: {
          onAggregationDataCellEvent(cell: grid.VGridCell, event: grid.VGridCellEvent) {
            if (event.field.name === 'quantity') {
              aggSumCell(cell, event, 'quantity');
            }
          },
        },
        editor: { type: 'double', onInputChange: onInputChange, }
      },
      {
        name: 'quantityUnit', label: T('Q.Unit'), width: 130,
        editor: {
          type: 'string',
          onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            let field = fieldCtx.fieldConfig;
            let dRecord = fieldCtx.displayRecord;
            let tabIndex = fieldCtx.tabIndex;
            let focus = fieldCtx.focus;
            let forwarder = dRecord.record;
            const oldVal = forwarder[field.name];
            let cssClass = field.cssClass;
            if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiRoot.getVGridContext(), dRecord)}`;
            return (
              <module.settings.BBRefUnit
                appContext={appContext} pageContext={pageContext}
                className={cssClass}
                minWidth={400}
                placement="left"
                placeholder="Enter Unit"
                bean={forwarder} beanIdField={field.name}
                tabIndex={tabIndex} autofocus={focus}
                onPostUpdate={(inputUI, bean, selectOpt, userInput) => onInputChange(bean, field.name, oldVal, bean[field.name])} />
            )
          },
        }
      },
      {
        name: 'weight', label: T('Weight'), width: 80,
        listener: {
          onAggregationDataCellEvent(cell: grid.VGridCell, event: grid.VGridCellEvent) {
            if (event.field.name === 'weight') {
              aggSumCell(cell, event, 'weight');
            }
          },
        },
        editor: { type: 'double', onInputChange: onInputChange }
      },
      { name: 'chargeableWeight', label: T('C.Weight'), state: { visible: false }, width: 80, editor: { type: 'double', onInputChange: onInputChange, } },
      {
        name: 'weightUnit', label: T('W.Unit'), state: { visible: false },
        editor: {
          type: 'string',
          onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            let field = fieldCtx.fieldConfig;
            let dRecord = fieldCtx.displayRecord;
            let tabIndex = fieldCtx.tabIndex;
            let focus = fieldCtx.focus;
            let forwarder = dRecord.record;
            const oldVal = forwarder[field.name];
            let cssClass = field.cssClass;
            if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiRoot.getVGridContext(), dRecord)}`;
            return (
              <module.settings.BBRefUnit
                className={cssClass}
                appContext={appContext} pageContext={pageContext}
                minWidth={300}
                placement="left"
                placeholder="Enter Unit"
                groupNames={['weight']}
                bean={forwarder} beanIdField={field.name}
                tabIndex={tabIndex} autofocus={focus}
                onPostUpdate={(inputUI, bean, selectOpt, userInput) => onInputChange(bean, field.name, oldVal, bean[field.name])} />
            )
          },
        }
      },
      {
        name: 'volumeAsText', label: T('Volume'), width: 80, cssClass: 'flex-grow-1 text-end',
        listener: {
          onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
            if (event.row == cell.getRow() && (event.field.name === 'volumeAsText')) {
              let bill = cell.getDisplayRecord().record;
              let volumeAsText = bill['volumeAsText'];
              if (TMSUtils.isDecimal(volumeAsText)) {
                bill['volume'] = Number(volumeAsText);
              } else {
                bill['volume'] = 0;
              }
            }
          },
        },
        editor: {
          type: 'string', onInputChange: onInputChange,
        }
      },
      {
        name: 'volume', label: T('volume'), width: 80,
        state: { visible: false }, editor: { type: 'double', onInputChange: onInputChange }
      },
      {
        name: 'chargeableVolume', label: T('C.Volume'), width: 80,
        state: { visible: false }, editor: { type: 'double', onInputChange: onInputChange }
      },
      {
        name: 'volumeUnit', label: T('V.Unit'), state: { visible: false },
        editor: {
          type: 'string',
          onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            let field = fieldCtx.fieldConfig;
            let dRecord = fieldCtx.displayRecord;
            let tabIndex = fieldCtx.tabIndex;
            let focus = fieldCtx.focus;
            let forwarder = dRecord.record;
            const oldVal = forwarder[field.name];
            let cssClass = field.cssClass;
            if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiRoot.getVGridContext(), dRecord)}`;
            return (
              <module.settings.BBRefUnit
                className={cssClass}
                appContext={appContext} pageContext={pageContext}
                minWidth={300}
                placement="left"
                placeholder="Enter Unit"
                groupNames={['volume']}
                bean={forwarder} beanIdField={field.name}
                tabIndex={tabIndex} autofocus={focus}
                onPostUpdate={(inputUI, bean, selectOpt, userInput) => onInputChange(bean, field.name, oldVal, bean[field.name])} />
            )
          },
        }
      },
      {
        name: 'carrierFullName', label: T('Carrier'), width: 150,
        editor: {
          type: 'string',
          onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            let field = fieldCtx.fieldConfig;
            let dRecord = fieldCtx.displayRecord;
            let tabIndex = fieldCtx.tabIndex;
            let focus = fieldCtx.focus;
            let bill = dRecord.record;
            const oldVal = bill[field.name];
            let cssClass = field.cssClass;
            if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiRoot.getVGridContext(), dRecord)}`;
            return (
              <BBRefTMSCarrier minWidth={400}
                className={cssClass} appContext={appContext} pageContext={pageContext} tabIndex={tabIndex} autofocus={focus}
                bean={bill} beanIdField={'carrierId'} beanLabelField={'carrierFullName'} placeholder={'Carrier'}
                onPostUpdate={(_inputUI: React.Component, bean: any, _selectOpt: any, _userInput: string) => {
                  onInputChange(bean, field.name, oldVal, bean[field.name])
                }}
              />
            )
          },
        }
      },
      { name: 'bookingCode', label: T('Booking/Bill'), width: 150, editor: { type: 'string', onInputChange: onInputChange, } },
      {
        name: 'etaCutOffTime', label: T('COT/ETA'), width: 130, dataType: 'string',
        listener: {
          onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
            if (event.row == cell.getRow() && event.field.name === 'mode') {
              cell.forceUpdate()
            }
          },
        },
        editor: {
          type: 'string',
          onInputChange: onInputChange,
        }
      },
      {
        name: 'warehouseLabel', label: T('Full Return WH'), width: 175, state: { visible: false },
      },
      {
        name: 'declarationNumber', label: T('CDS'), hint: T('Declaration Number'), width: 150,
        editor: { type: 'string', onInputChange: onInputChange, }
      },
      //Sender
      {
        name: 'senderContact', label: T('S/WH.Contact'), hint: T('Sender/WH Contact'), cssClass: 'px-1', width: 155, dataTooltip: true,
        sortable: true,
        listener: {
          onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
            if (event.row == cell.getRow() && event.field.name === 'mode') {
              cell.forceUpdate()
            }
          },
        },
        customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, focus: boolean) => {
          let bill = dRecord.record;
          let cssClass = field.cssClass;
          if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(ctx, dRecord)}`;
          return TMSUtils.renderTMSGridTooltip(bill[field.name], bill[field.name], field.width, dRecord.row);
        },
        editor: {
          type: 'string',
          onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            let field = fieldCtx.fieldConfig;
            let dRecord = fieldCtx.displayRecord;
            let tabIndex = fieldCtx.tabIndex;
            let bill = dRecord.record;
            let cssClass = field.cssClass;
            let mode = bill.mode;
            let placeholder = TMSBillTransportationModeTools.isExport(mode) || TMSBillTransportationModeTools.isDomestic(mode) ? 'Contact' : 'WH Contact';

            if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiRoot.getVGridContext(), dRecord)}`;
            let html = (
              <div className='flex-hbox'>
                <input.BBStringField className={cssClass} placeholder={placeholder}
                  tabIndex={tabIndex} focus={fieldCtx.focus} bean={bill} field={field.name} onInputChange={onInputChange} />
                {UITMSBillUtils.renderButtonInfo('Sender Mobile', bill[field.name])}
              </div>
            )
            return html;
          },
        },
      },
      {
        name: 'senderAddress', label: T('S.Address/Full Return WH'), hint: T('Sender Address'), cssClass: 'text-warning', width: 250, dataTooltip: true,
        listener: {
          onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
            if (event.row == cell.getRow() && event.field.name === 'mode' || event.field.name === 'customerFullName') {
              cell.forceUpdate()
            }
          },
        },
        // fieldDataGetter(record) {
        //   let senderAddress = record['senderAddress'];
        //   let mode = record.mode;
        //   if (TMSBillTransportationModeTools.isImport(mode) && record['senderLocationShortLabel']) {
        //     senderAddress = record['senderLocationShortLabel'];
        //   }
        //   return senderAddress;
        // },
        customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
          let bill = dRecord.record;
          let cssClass = 'text-warning flex-hbox justify-content-end px-2';
          let value = field.fieldDataGetter ? field.fieldDataGetter(bill) : bill[field.name];
          return TMSUtils.renderTMSGridTooltip(value, value, field.width, dRecord.row, { className: cssClass });
        },
        editor: {
          type: 'string',
          onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            let field = fieldCtx.fieldConfig;
            let dRecord = fieldCtx.displayRecord;
            let tabIndex = fieldCtx.tabIndex;
            let focus = fieldCtx.focus;
            let bill = dRecord.record;
            const senderLocationId = bill['senderLocationId'];
            let mode = bill.mode;
            let cssClass = field.cssClass;
            if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiRoot.getVGridContext(), dRecord)}`;
            let html = TMSBillTransportationModeTools.isExport(mode) || TMSBillTransportationModeTools.isDomestic(mode) ?
              <BBRefTMSPartnerAddress key={`customer-${bill['customerId']}-${mode}`}
                minWidth={500}
                className={`flex-grow-1 ${cssClass}`} autofocus={fieldCtx.focus} tabIndex={tabIndex} allowUserInput
                appContext={appContext} pageContext={pageContext} placeholder='Address' bean={bill} customerId={bill['customerId']}
                beanIdField='senderLocationId' beanLabelField={field.name} types={['Export', 'None']}
                onPostUpdate={(_inputUI: React.Component, bean: any, _selectOpt: any, _userInput: string) => {
                  bean["senderLocationAddress"] = _selectOpt['locationAddress'];
                  bean["senderStreetName"] = _selectOpt['streetName'];
                  onInputChange(bean, field.name, senderLocationId, bean['senderLocationId']);
                }}
              />
              :
              <BBRefLocation
                className={`flex-grow-1 ${cssClass}`} autofocus={focus} tabIndex={tabIndex} required locationTags={['app:tms']} minWidth={500}
                appContext={appContext} pageContext={pageContext} bean={bill} disable={!writeCap} refLocationBy='id'
                beanIdField={'senderLocationId'} beanLabelField={field.name} placeholder='Full Return WH'
                onPostUpdate={(_inputUI: React.Component, bean: any, _selectOpt: any, _userInput: string) => {
                  bean["senderLocationAddress"] = _selectOpt['address'];
                  // bean["senderAddress"] = _selectOpt['address'];
                  onInputChange(bean, field.name, senderLocationId, bean['senderLocationId']);
                }} />
            return UITMSBillUtils.renderTooltip(fieldCtx.fieldConfig.fieldDataGetter ? fieldCtx.fieldConfig.fieldDataGetter(bill) : bill[field.name], html);
          },
        },
      },
      {
        name: 'senderLocationAddress', label: T('S.Location'), hint: T('Sender Location'), width: 250, state: { visible: false },
        customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
          let bill = dRecord.record;
          let address = bill[field.name];
          return TMSUtils.renderTMSGridTooltip(address, address, field.width, dRecord.row, { className: 'text-warning flex-hbox justify-content-end px-2' });
        },
        editor: {
          type: 'string',
          onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            let field = fieldCtx.fieldConfig;
            let dRecord = fieldCtx.displayRecord;
            let bill = dRecord.record;
            const oldVal = bill[field.name];
            let className = 'flex-hbox align-items-center justify-content-end text-warning h-100'
            return (
              <BBRefLocation
                className={`flex-grow-1 ${className}`} tabIndex={fieldCtx.tabIndex} autofocus={fieldCtx.focus} required locationTags={['app:tms']} minWidth={500}
                appContext={appContext} pageContext={pageContext} bean={bill} disable={!writeCap} refLocationBy='id'
                beanIdField={'senderLocationId'} beanLabelField={'senderLocationAddress'} beanRefLabelField='address' placeholder='Location'
                onPostUpdate={(_inputUI: React.Component, bean: any, _selectOpt: any, _userInput: string) => {
                  if (!bean["senderAddress"]) bean["senderAddress"] = _selectOpt['address'];
                  onInputChange(bean, field.name, oldVal, bean[field.name]);
                }}
              />
            )
          },
        }
      },
      {
        name: 'stopLocations', label: T('Stop Location'), width: 250,
        customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
          let bill = dRecord.record;
          let stopLocations: Array<any> = bill['stopLocations'];
          let addresses: Array<string> = [];
          if (stopLocations) {
            for (let stopLocation of stopLocations) {
              addresses.push(stopLocation.address);
            }

          }

          return <bs.CssTooltip >
            <bs.CssTooltipToggle className='flex-vbox'>
              {
                addresses.map((address: string, index: number) =>
                  <div style={{
                    borderBottom: index !== addresses.length - 1 ? '1px solid #ccc' : 'none',
                    padding: '2px 0'
                  }}>
                    {address}
                  </div>)
              }
            </bs.CssTooltipToggle>
            <bs.CssTooltipContent
              className={`d-flex align-items-end text-secondary`}
              style={{ whiteSpace: 'break-spaces', transform: 'translate(-5px, -75px)' }}>
              {
                addresses ? addresses.map((address) =>
                  <div className='p-2'>
                    {address}
                  </div>
                ) : <></>
              }
            </bs.CssTooltipContent>
          </bs.CssTooltip>
        },
        editor: {
          type: 'string',
          onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            let dRecord = fieldCtx.displayRecord;
            let bill = dRecord.record;
            let stopLocations: Array<any> = bill['stopLocations'];
            let addresses: Array<string> = [];
            if (stopLocations) {
              for (let stopLocation of stopLocations) {
                addresses.push(stopLocation.address);
              }
            }
            return (
              <div className={`flex-hbox align-items-center`}>
                <bs.CssTooltip style={{ width: fieldCtx.fieldConfig.width! - 22 }}>
                  <bs.CssTooltipToggle style={{ textOverflow: 'ellipsis', overflow: 'hidden' }} className='flex-vbox'>
                    {
                      addresses.map((address: string, index: number) =>
                        <div style={{
                          width: '100%',
                          borderBottom: index !== addresses.length - 1 ? '1px solid #ccc' : 'none',
                          padding: '2px 0'
                        }}>
                          {address}
                        </div>)
                    }
                  </bs.CssTooltipToggle>
                  <bs.CssTooltipContent
                    className={`d-flex align-items-end text-secondary`}
                    style={{ whiteSpace: 'break-spaces', transform: 'translate(-5px, -75px)' }}>
                    {
                      addresses.map((address) =>
                        <div className='p-2'>
                          {address}
                        </div>
                      )
                    }
                  </bs.CssTooltipContent>
                </bs.CssTooltip>
                <bs.Button laf='link' className='p-0 me-1' disabled={!writeCap} onClick={() => uiRoot.onShowStopLocations(bill)}>
                  <FeatherIcon.Edit size={12} className='mx-1 state-modified' />
                </bs.Button>
              </div>
            )
          },
        }
      },
      //Receiver
      {
        name: 'receiverContact', label: T('R/WH.Contact'), hint: T('Receiver/WH Contact'), width: 155, dataTooltip: true,
        sortable: true,
        listener: {
          onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
            if (event.row == cell.getRow() && event.field.name === 'mode') {
              cell.forceUpdate()
            }
          },
        },
        customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
          let bill = dRecord.record;
          return TMSUtils.renderTMSGridTooltip(bill[field.name], bill[field.name], field.width, dRecord.row);
        },
        editor: {
          type: 'string',
          onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            let field = fieldCtx.fieldConfig;
            let dRecord = fieldCtx.displayRecord;
            let tabIndex = fieldCtx.tabIndex;
            let focus = fieldCtx.focus;
            let bill = dRecord.record;
            let cssClass = field.cssClass;
            let mode = bill.mode;
            let placeholder = TMSBillTransportationModeTools.isImport(mode) || TMSBillTransportationModeTools.isDomestic(mode) ? 'Contact' : 'WH Contact';
            if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiRoot.getVGridContext(), dRecord)}`;
            let html = (
              <div className='flex-hbox'>
                <input.BBStringField className={cssClass}
                  focus={focus} tabIndex={tabIndex} bean={bill} field={field.name} onInputChange={onInputChange} placeholder={placeholder} />
                {UITMSBillUtils.renderButtonInfo('Receiver Mobile', bill[field.name])}
              </div>
            )
            return html;
          },
        },
      },
      {
        name: 'receiverAddress', label: T('R.Address/Full Return WH'), hint: T('Receiver Address'), cssClass: 'text-success', width: 250, dataTooltip: true,
        listener: {
          onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
            if (event.row == cell.getRow() && event.field.name === 'mode') {
              cell.forceUpdate()
            }
          },
        },
        customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
          let bill = dRecord.record;
          let cssClass = 'text-success flex-hbox justify-content-end px-2';
          let value = field.fieldDataGetter ? field.fieldDataGetter(bill) : bill[field.name];
          return TMSUtils.renderTMSGridTooltip(value, value, field.width, dRecord.row, { className: cssClass });
        },
        editor: {
          type: 'string',
          onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            let field = fieldCtx.fieldConfig;
            let dRecord = fieldCtx.displayRecord;
            let tabIndex = fieldCtx.tabIndex;
            let focus = fieldCtx.focus;
            let bill = dRecord.record;
            const receiverLocationId = bill['receiverLocationId'];
            let mode = bill.mode;
            let cssClass = field.cssClass;
            let html = TMSBillTransportationModeTools.isImport(mode) || TMSBillTransportationModeTools.isDomestic(mode) ?
              <BBRefTMSPartnerAddress key={`customer-${bill['customerId']}-${mode}`}
                minWidth={500}
                className={`flex-grow-1 ${cssClass}`} autofocus={fieldCtx.focus} tabIndex={tabIndex} allowUserInput
                appContext={appContext} pageContext={pageContext} placeholder='Address' bean={bill} customerId={bill['customerId']}
                beanIdField='receiverLocationId' beanLabelField={field.name} types={['Import', 'None']}
                onPostUpdate={(_inputUI: React.Component, bean: any, _selectOpt: any, _userInput: string) => {
                  bean["receiverLocationAddress"] = _selectOpt['locationAddress'];
                  bean["receiverStreetName"] = _selectOpt['streetName'];
                  onInputChange(bean, field.name, receiverLocationId, bean['receiverLocationId']);
                }}
              />
              :
              <BBRefLocation
                className={`flex-grow-1 ${cssClass}`} tabIndex={tabIndex} autofocus={focus} required locationTags={['app:tms']} minWidth={500}
                appContext={appContext} pageContext={pageContext} bean={bill} disable={!writeCap} refLocationBy='id'
                beanIdField={'receiverLocationId'} beanLabelField={field.name} placeholder='Full Return WH'
                onPostUpdate={(_inputUI: React.Component, bean: any, _selectOpt: any, _userInput: string) => {
                  bean["receiverLocationAddress"] = _selectOpt['address'];
                  // bean["receiverAddress"] = _selectOpt['address'];
                  onInputChange(bean, field.name, receiverLocationId, bean['receiverLocationId']);
                }} />
            return UITMSBillUtils.renderTooltip(fieldCtx.fieldConfig.fieldDataGetter ? fieldCtx.fieldConfig.fieldDataGetter(bill) : bill[field.name], html);;
          },
        },
      },
      {
        name: 'receiverLocationAddress', label: T('R.Location'), hint: T('Receiver Location'), width: 250, state: { visible: false },
        customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
          let bill = dRecord.record;
          let address = bill[field.name];
          return TMSUtils.renderTMSGridTooltip(address, address, field.width, dRecord.row, { className: 'text-success flex-hbox justify-content-end px-2' });
        },
        editor: {
          type: 'string',
          onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            let field = fieldCtx.fieldConfig;
            let dRecord = fieldCtx.displayRecord;
            let bill = dRecord.record;
            const oldVal = bill[field.name];
            let className = 'flex-hbox align-items-center justify-content-end text-success h-100'
            return (
              <BBRefLocation
                className={`flex-grow-1 ${className}`} autofocus={fieldCtx.focus} tabIndex={fieldCtx.tabIndex} required locationTags={['app:tms']} minWidth={500}
                appContext={appContext} pageContext={pageContext} bean={bill} disable={!writeCap} refLocationBy='id'
                beanIdField={'receiverLocationId'} beanLabelField={'receiverLocationAddress'} beanRefLabelField='address' placeholder='Location'
                onPostUpdate={(_inputUI: React.Component, bean: any, _selectOpt: any, _userInput: string) => {
                  if (!bean["receiverAddress"]) bean["receiverAddress"] = _selectOpt['address'];
                  onInputChange(bean, field.name, oldVal, bean[field.name]);
                }}
              />
            )
          },
        }
      },
      {
        name: 'description', label: T('Notes'), width: 200, dataTooltip: true, cssClass: 'px-1 text-danger',
        customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, focus: boolean) => {
          let bill = dRecord.record;
          let html = (
            <bs.CssTooltip style={{ width: field.width }}>
              <bs.CssTooltipToggle style={{ textOverflow: 'ellipsis', overflow: 'hidden' }}>
                {bill[field.name]}
              </bs.CssTooltipToggle>
              <bs.CssTooltipContent
                className={`d-flex align-items-end text-secondary`}
                style={{ whiteSpace: 'break-spaces', transform: 'translate(-5px, -75px)' }}>
                <div className='p-2'>
                  {bill[field.name]}
                </div>
              </bs.CssTooltipContent>
            </bs.CssTooltip>
          )
          return html;
        },
        editor: {
          type: 'string', onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            let field = fieldCtx.fieldConfig;
            let dRecord = fieldCtx.displayRecord;
            let tabIndex = fieldCtx.tabIndex;
            let bill = dRecord.record;
            let cssClass = field.cssClass;
            if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiRoot.getVGridContext(), dRecord)}`;
            return (
              <div className='flex-hbox'>
                <input.BBStringField className={cssClass} tabIndex={tabIndex} focus={fieldCtx.focus} bean={bill} field={field.name} onInputChange={onInputChange} />
                {UITMSBillUtils.renderButtonInfo('Note', bill[field.name])}
              </div>
            );
          },
        }
      },
      {
        name: 'tmsTrackingDescription', label: T('Tracking Note'), width: 200, dataTooltip: true,
        hint: 'Ghi Chú Điều Vận', cssClass: 'text-warning',
        fieldDataGetter: (record) => {
          let trackings: Array<any> = record['trackings'];
          let vendorBills: Array<any> = record['vendorBills'];
          if (trackings) {
            return trackings.map(
              tracking => {
                let vehicleLabel = tracking.vehicleLabel ? tracking.vehicleLabel : 'No Vehicle';
                let description = tracking.description;
                if (!description) return;
                return trackings.length > 1 ? `${vehicleLabel} : ${description}` : description;
              }
            ).join('\n');
          }
          if (vendorBills) {
            return vendorBills.map(
              vendorBill => {
                let vehicleLabel = vendorBill.licensePlate ? vendorBill.licensePlate : 'No Vehicle';
                let description = vendorBill.description;
                if (!description) return;
                return vendorBills.length > 1 ? `${vehicleLabel} : ${description}` : description;
              }
            ).join(', ');
          }
          return "";
        },
      },

      {
        name: 'vendorFullName', label: T('Vendor'), width: 150, cssClass: 'px-1', filterableType: 'options', filterable: true,
        computeCssClasses(ctx, dRecord) {
          let vendorId = dRecord.getValue('vendorId');
          if (!vendorId) return 'text-warning'
          return '';
        },
        editor: {
          type: 'string',
          onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            let field = fieldCtx.fieldConfig;
            let dRecord = fieldCtx.displayRecord;
            let tabIndex = fieldCtx.tabIndex;
            let focus = fieldCtx.focus;
            let bill = dRecord.record;
            let cssClass = field.cssClass;
            const oldVal = bill[field.name];
            if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiRoot.getVGridContext(), dRecord)}`;
            return (
              <BBRefVehicleFleet minWidth={400}
                className={cssClass} appContext={appContext} pageContext={pageContext} tabIndex={tabIndex} autofocus={focus}
                bean={bill} beanIdField={'vendorId'} beanLabelField={'vendorFullName'} placeholder={'Vendor'}
                onPostUpdate={(_inputUI: React.Component, bean: any, _selectOpt: any, _userInput: string) => {
                  onInputChange(bean, field.name, oldVal, bean[field.name])
                }}
              />
            )
          },
        }
      },
      {
        name: 'reportBFSOne', label: T('Report BFSOne'), dataTooltip: true, width: 150,
        fieldDataGetter(record: any) {
          let trackings: Array<any> = record['trackings'];
          let vendorBills: Array<any> = record['vendorBills'];
          let trackingInfo = [];
          if (trackings) {
            for (let tracking of trackings) {
              if (!tracking['vehicleLabel']) continue;
              trackingInfo.push(
                TMSJoinFields(
                  tracking,
                  { 'mobile': 'Mobile', 'identificationNo': 'ID' },
                  'vehicleType', 'vehicleLabel', 'driverFullName', 'mobile', 'identificationNo'
                ));
            }
          } else if (vendorBills) {
            for (let vendorBill of vendorBills) {
              if (!vendorBill['licensePlate']) continue;
              trackingInfo.push(
                TMSJoinFields(
                  vendorBill,
                  { 'driverMobile': 'Mobile', 'driverIdentificationNo': 'ID' },
                  'vehicleType', 'licensePlate', 'driverFullName', 'driverMobile', 'driverIdentificationNo'
                ));
            }
          } else {
            trackingInfo = [record['truckNo']];
          }
          let info = TMSJoinFields(record, {}, 'declarationNumber', 'collect', 'truckType');
          return info + ' ' + trackingInfo.join(' ');
        },
        customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
          let value = field.fieldDataGetter ? field.fieldDataGetter(dRecord.record) : "";
          return (
            <div className='flex-hbox'>
              <div className='text-truncate flex-grow-1' style={{ width: 120 }}>
                {value}
              </div>
              {UITMSBillUtils.renderButtonInfo('Report BFSOne', value)}
            </div>
          )
        }
      },
      {
        name: 'tmsTrackingFleetLabel', label: T('Ref. Vendor'), width: 120, cssClass: 'text-warning',
        filterableType: 'options', filterable: true,
        fieldDataGetter: (record) => {
          let trackings: Array<any> = record['trackings'];
          if (trackings) {
            return trackings.map(tracking => tracking.fleetLabel).join(', ');
          }
          return "";
        },
      },
      {
        name: 'licensePlate', label: T('License Plate'), width: 120,
        filterableType: 'options', filterable: true,
        fieldDataGetter: (record) => {
          let trackings: Array<any> = record['trackings'];
          if (trackings) {
            return trackings.map(tracking => tracking.vehicleLabel).join(', ');
          }
          let vendorBills: Array<any> = record['vendorBills'];
          if (vendorBills) {
            return vendorBills.map(vendorBill => vendorBill.licensePlate).join(', ');
          }
          return null;
        },
        editor: {
          type: 'string', onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            let field = fieldCtx.fieldConfig;
            let dRecord = fieldCtx.displayRecord;
            let tabIndex = fieldCtx.tabIndex;
            let focus = fieldCtx.focus;
            let bill = dRecord.record;
            let licensePlate = bill['licensePlate'];
            if (!licensePlate) bill['licensePlate'] = field.fieldDataGetter ? field.fieldDataGetter(bill) : licensePlate;
            let cssClass = field.cssClass;
            if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiRoot.getVGridContext(), dRecord)}`;
            return (
              <input.BBStringField disable={!writeCap || bill['trackings']}
                className={cssClass} tabIndex={tabIndex} focus={focus} bean={bill} field={field.name}
                onInputChange={(bean: any, field: string, oldVal: any, newVal: any) => {
                  let vendorBills: Array<any> = bean['vendorBills'];
                  if (vendorBills) {
                    let find = vendorBills.find(vendorBill => vendorBill['licensePlate'] === oldVal);
                    if (find) {
                      find['licensePlate'] = newVal;
                    }
                  }
                  onInputChange(bean, field, oldVal, newVal);
                }} />
            );
          },
        }
      },
      {
        name: 'truckNo', label: T('Truck Info'), dataTooltip: true,
        fieldDataGetter(record: any) {
          let trackings: Array<any> = record['trackings'];
          let vendorBills: Array<any> = record['vendorBills'];
          let info = [];
          if (trackings) {
            for (let tracking of trackings) {
              if (!tracking['vehicleLabel']) continue;
              info.push(
                TMSJoinFields(
                  tracking,
                  { 'mobile': 'Mobile', 'identificationNo': 'ID' },
                  'vehicleType', 'vehicleLabel', 'driverFullName', 'mobile', 'identificationNo'
                ));
            }
            return info.join(', \n');
          }
          let truckNo = record['truckNo'] ? record['truckNo'] : '';
          if (vendorBills) {
            for (let vendorBill of vendorBills) {
              if (!vendorBill['licensePlate']) continue;
              info.push(
                TMSJoinFields(
                  vendorBill,
                  { 'driverMobile': 'Mobile', 'driverIdentificationNo': 'ID' },
                  'licensePlate', 'driverFullName', 'driverMobile', 'driverIdentificationNo'
                ));
            }
            return truckNo + "\n" + info.join(', \n');
          }
          return truckNo;
        },
        editor: {
          type: 'string', onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            let field = fieldCtx.fieldConfig;
            let dRecord = fieldCtx.displayRecord;
            let tabIndex = fieldCtx.tabIndex;
            let focus = fieldCtx.focus;
            let cssClass = field.cssClass;
            if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiRoot.getVGridContext(), dRecord)}`;
            let bill = dRecord.record;
            if (bill['trackings'] && field.fieldDataGetter) {
              return TMSCssTooltip(field.fieldDataGetter(bill), { width: field.width, opacity: 0.87 });
            }
            return (
              <div className='flex-hbox'>
                <input.BBStringField
                  className={cssClass} tabIndex={tabIndex} focus={focus} bean={bill} field={field.name}
                  onInputChange={(bean: any, field: string, oldVal: any, newVal: any) => {
                    let vendorBills: Array<any> = bean['vendorBills'];
                    if (vendorBills) {
                      let find = vendorBills.find(vendorBill => vendorBill['licensePlate'] === bill['licensePlate']);
                      if (find) {
                        find['note'] = newVal;
                      }
                    }
                    onInputChange(bean, field, oldVal, newVal);
                  }}
                />
                <bs.Button laf='link' onClick={() => {
                  UITMSVendorBillList.onShowVendorBillTracking(appContext, pageContext, bill.id, (data) => {
                    bill['vendorBills'] = data['billTrackings'];
                    fieldCtx.gridContext.getVGrid().forceUpdateView();
                  })
                }}>
                  <FeatherIcon.Edit size={12} />
                </bs.Button>
              </div>
            )
          },
        }
      },
      {
        name: 'jobOtherInfo', label: T('Job Info'), dataTooltip: true, width: 120, state: { visible: false },
        customRender: (_ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, focus: boolean) => {
          if (!dRecord.isDataRecord()) return;
          let bill = dRecord.record;
          return <ButtonTMSBillJobOther appContext={appContext} pageContext={pageContext} tmsBill={bill} onPostCommit={(bean) => {
            if (bill['id']) {
              updateTMSBillData(_ctx, [bill], () => {
                appContext.addOSNotification("success", T("Update Bill Success"));
                _ctx.getVGrid().forceUpdateView();
              })
            } else {
              bill['jobs'] = bean['jobs'];
              bill['jobOtherInfo'] = bean['jobOtherInfo'];
              _ctx.getVGrid().forceUpdateView();
            }
          }} />;
        },
      },

      //Charge
      {
        name: 'adjustTotalShipmentCharge', label: T('Revenue'),
        hint: T('Total Shipment Charge'), format: formatCurrency, dataType: 'double', state: { visible: false },
        customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, focus: boolean) => {
          let uiList = ctx.uiRoot as UITMSBillList;
          let bill = dRecord.record;
          let cssClass = field.cssClass;
          if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(ctx, dRecord)}`;
          let edit = (
            <div className='flex-grow-0 text-end'>
              <bs.Button style={{ paddingBottom: 5 }} key={'profit'} laf='link' onClick={() => uiList.onShowTMSBillFee(dRecord.record)}>
                <FeatherIcon.Edit className={'text-warning'} size={12} />
              </bs.Button>
            </div>
          )
          if (bill.id == null) edit = <></>;
          return (
            <div className={`flex-hbox ${cssClass}`}>
              <div className='flex-grow-1 text-info px-1 text-end'>{formatCurrency(dRecord.record[field.name])}</div>
              <div style={{ width: 15 }}>
                {edit}
              </div>
            </div>
          );
        }
      },
      { name: 'adjustTotalTax', label: T('Total Tax'), format: formatCurrency, dataType: 'double', state: { visible: false } },
      { name: 'adjustFinalCharge', label: T('Final Charge'), format: formatCurrency, dataType: 'double', state: { visible: false } },
      {
        name: 'paymentNote', label: T('Payment Note'), width: 120,
        editor: {
          type: 'string',
          onInputChange: onInputChange
        }
      },
      {
        name: 'feedback', label: T('Feedback'), width: 120,
        editor: {
          type: 'text',
          onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            let { displayRecord, focus, tabIndex, fieldConfig } = fieldCtx;
            let bill = displayRecord.record;
            let disable = !writeCap;
            if (!bill['vendorBillId']) {
              disable = true;
            }
            return (
              <input.BBTextField bean={bill} disable={disable} field={fieldConfig.name} tabIndex={tabIndex} focus={focus} onInputChange={onInputChange} />
            )
          }
        }
      },
      // {
      //   name: 'matchPrice', label: '', hint: T('Match Price'), width: 40,
      //   customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
      //     let record = dRecord.record;
      //     return (
      //       <div className={'flex-hbox text-center'}>
      //         {renderMatchPriceButton(ctx, record)}
      //       </div>
      //     )
      //   },
      // },
      {
        name: 'fixedPayment', label: T(`Fixed`), format: formatCurrency, dataType: 'double',
        customHeaderRender(ctx, field, _headerEle) {
          return UITMSBillUtils.onRenderCellHeaderMultiplier(field, uiRoot.multiplierBean, (val) => {
            let selectRecs: Array<any> = plugin.getListModel().getSelectedRecords();
            selectRecs.forEach(sel => {
              sel['fixedPayment'] = sel['fixedPayment'] * val;
              sel['totalPayment'] = sel['fixedPayment'] + sel['extraPayment'];
              let state = grid.getRecordState(sel);
              state.markModified(true);
            });
            ctx.getVGrid().forceUpdateView();
          });
        },
        listener: {
          onAggregationDataCellEvent(cell: grid.VGridCell, event: grid.VGridCellEvent) {
            if (event.field.name === 'fixedPayment') {
              aggSumCell(cell, event, 'fixedPayment');
            }
          },
        },
        computeCssClasses: (_ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
          let bill = dRecord.record;
          if (bill['trackings']) return 'text-info';
          let fixedPayment = bill['fixedPayment'];
          let tmsTrackingFixedCharge = bill['tmsTrackingFixedCharge'] ? bill['tmsTrackingFixedCharge'] : bill['vendorFixed'];
          tmsTrackingFixedCharge = tmsTrackingFixedCharge ? tmsTrackingFixedCharge : 0;
          if (fixedPayment != tmsTrackingFixedCharge) return 'text-danger';
          return '';
        },
        editor: {
          type: 'currency',
          onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            let field = fieldCtx.fieldConfig;
            let dRecord = fieldCtx.displayRecord;
            let tabIndex = fieldCtx.tabIndex;
            let focus = fieldCtx.focus;
            let bill = dRecord.record;
            let trackings = bill['trackings'];
            let _writeCap = writeCap && !trackings;
            let cssClass = field.cssClass;
            if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiRoot.getVGridContext(), dRecord)}`;
            let isPaid = bill['paymentStatus'] == 'Paid';
            return (
              <input.BBCurrencyField disable={!_writeCap || isPaid} className={cssClass} tabIndex={tabIndex} focus={focus}
                bean={bill} field={field.name} onInputChange={onInputChange} />
            )
          },
        }
      },
      {
        name: 'extraPayment', label: T('Extra'), format: formatCurrency, dataType: 'double',
        computeCssClasses: (_ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
          let bill = dRecord.record;
          if (bill['trackings']) return 'text-info';
          let extraPayment = bill['extraPayment'];
          let tmsTrackingExtraCharge = bill['tmsTrackingExtraCharge'] ? bill['tmsTrackingExtraCharge'] : bill['vendorExtra'];
          tmsTrackingExtraCharge = tmsTrackingExtraCharge ? tmsTrackingExtraCharge : 0;
          if (extraPayment != tmsTrackingExtraCharge) return 'text-danger';
          return '';
        },
        listener: {
          onAggregationDataCellEvent(cell: grid.VGridCell, event: grid.VGridCellEvent) {
            if (event.field.name === 'extraPayment') {
              aggSumCell(cell, event, 'extraPayment');
            }
          },
        },
        editor: {
          type: 'currency',
          onInputChange: onInputChange,
          renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            let field = fieldCtx.fieldConfig;
            let dRecord = fieldCtx.displayRecord;
            let tabIndex = fieldCtx.tabIndex;
            let focus = fieldCtx.focus;
            let bill = dRecord.record;
            let trackings = bill['trackings'];
            let _writeCap = writeCap && !trackings;
            let cssClass = field.cssClass;
            if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(uiRoot.getVGridContext(), dRecord)}`;
            let isPaid = bill['paymentStatus'] == 'Paid';
            return (
              <input.BBCurrencyField disable={!_writeCap || isPaid} className={cssClass} tabIndex={tabIndex} focus={focus}
                bean={bill} field={field.name} onInputChange={onInputChange} />
            )
          },
        }
      },
      {
        name: 'totalPayment', label: T('Total'), hint: T('Total Payment'), dataType: 'double', format: formatCurrency,
        computeCssClasses: (_ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
          let bill = dRecord.record;
          if (bill['trackings']) return 'text-info';
          let totalPayment = bill['totalPayment'];
          let tmsTrackingTotalCharge = bill['tmsTrackingTotalCharge'] ? bill['tmsTrackingTotalCharge'] : bill['vendorCost'];
          tmsTrackingTotalCharge = tmsTrackingTotalCharge ? tmsTrackingTotalCharge : 0;
          if (totalPayment != tmsTrackingTotalCharge) return 'text-danger';
          return '';
        },
        listener: {
          onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
            if (event.row == cell.getRow() && (event.field.name === 'fixedPayment' || event.field.name === 'extraPayment')) {
              let bill = cell.getDisplayRecord().record;
              bill.totalPayment = bill.fixedPayment + bill.extraPayment;
              cell.forceUpdate();
            }
          },
          onAggregationDataCellEvent(cell: grid.VGridCell, event: grid.VGridCellEvent) {
            if (event.field.name === 'fixedPayment' || event.field.name === 'extraPayment') {
              aggSumCell(cell, event, 'totalPayment');
            }
          },
        },
      },
      {
        name: '_editCost', label: '', hint: T('Edit Cost'), filterable: true, filterableType: 'options', width: 60,
        customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, focus: boolean) => {
          let uiList = ctx.uiRoot as UITMSBillList;
          let bill = dRecord.record;
          let editCost = (
            <bs.Button style={{ paddingBottom: 5 }} className='me-1' laf='link' onClick={() => uiList.onShowTMSBillFee(dRecord.record)}>
              <FeatherIcon.Edit className={'text-warning'} size={12} />
            </bs.Button>
          )
          if (bill.id == null || !dRecord.isDataRecord()) return;
          let cssClass = field.cssClass;
          if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(ctx, dRecord)}`;
          let color = 'text-danger';
          let vendorCostStatus = bill['vendorCostStatus'];
          if (vendorCostStatus == 'AUTO_CONFIRM' || vendorCostStatus == 'MANUAL_CONFIRM') color = 'text-success';
          let uiCheckVendorCost = (
            <div style={{ width: 15 }} className={`flex-grow-0 text-end`}>
              <bs.Button style={{ paddingBottom: 5 }} laf='link' onClick={() => {
                uiList.onShowSyncCost(bill);
              }} >
                <FeatherIcon.Repeat className={color} size={15} />
              </bs.Button>
            </div>
          );
          if (bill['closePayment']) uiCheckVendorCost = <></>;
          return (
            <div className={`flex-hbox`}>
              {editCost}
              {uiCheckVendorCost}
            </div>
          );
        }
      },
      {
        name: 'vendorFixed', label: T('Vendor Fixed'), format: formatCurrency, dataType: 'double',
        fieldDataGetter(record) {
          if (record['tmsTrackingFixedCharge']) return record['tmsTrackingFixedCharge'];
          if (record['vendorFixed']) return record['vendorFixed'];
          return 0;
        },
        computeCssClasses: (_ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
          let bill = dRecord.record;
          let fixedPayment = bill['fixedPayment'];
          let tmsTrackingFixedCharge = bill['tmsTrackingFixedCharge'] ? bill['tmsTrackingFixedCharge'] : bill['vendorFixed'];
          tmsTrackingFixedCharge = tmsTrackingFixedCharge ? tmsTrackingFixedCharge : 0;
          if (fixedPayment != tmsTrackingFixedCharge) return 'text-danger';
          return '';
        },
        customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, focus: boolean) => {
          let value = field.fieldDataGetter ? field.fieldDataGetter(dRecord.record) : 0;
          let cssClass = field.computeCssClasses ? field.computeCssClasses(ctx, dRecord) : "";
          return (
            <div className={`flex-vbox ${cssClass} cell-number`}>
              {formatCurrency(value)}
            </div>
          )
        }
      },
      {
        name: 'vendorExtra', label: T('Vendor Extra'), format: formatCurrency, dataType: 'double',
        fieldDataGetter(record) {
          if (record['tmsTrackingExtraCharge']) return record['tmsTrackingExtraCharge'];
          if (record['vendorExtra']) return record['vendorExtra'];
          return 0;
        },
        computeCssClasses: (_ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
          let bill = dRecord.record;
          let extraPayment = bill['extraPayment'];
          let tmsTrackingExtraCharge = bill['tmsTrackingExtraCharge'] ? bill['tmsTrackingExtraCharge'] : bill['vendorExtra'];
          tmsTrackingExtraCharge = tmsTrackingExtraCharge ? tmsTrackingExtraCharge : 0;
          if (extraPayment != tmsTrackingExtraCharge) return 'text-danger';
          return '';
        },
        customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, focus: boolean) => {
          let value = field.fieldDataGetter ? field.fieldDataGetter(dRecord.record) : 0;
          let cssClass = field.computeCssClasses ? field.computeCssClasses(ctx, dRecord) : "";
          return (
            <div className={`flex-vbox ${cssClass} cell-number`}>
              {formatCurrency(value)}
            </div>
          )
        }
      },
      {
        name: 'vendorCost', label: T('Vendor Total Cost'), format: formatCurrency, dataType: 'double',
        fieldDataGetter(record) {
          if (record['tmsTrackingTotalCharge']) return record['tmsTrackingTotalCharge'];
          if (record['vendorCost']) return record['vendorCost'];
          return 0;
        },
        computeCssClasses: (_ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
          let bill = dRecord.record;
          let totalPayment = bill['totalPayment'];
          let tmsTrackingTotalCharge = bill['tmsTrackingTotalCharge'] ? bill['tmsTrackingTotalCharge'] : bill['vendorCost'];
          tmsTrackingTotalCharge = tmsTrackingTotalCharge ? tmsTrackingTotalCharge : 0;
          if (totalPayment != tmsTrackingTotalCharge) return 'text-danger';
          return '';
        },
        customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, focus: boolean) => {
          let value = field.fieldDataGetter ? field.fieldDataGetter(dRecord.record) : 0;
          let cssClass = field.computeCssClasses ? field.computeCssClasses(ctx, dRecord) : "";
          return (
            <div className={`flex-vbox ${cssClass} cell-number`}>
              {formatCurrency(value)}
            </div>
          )
        }
      },
      //
      {
        name: 'profit', label: T('Profit'), format: formatCurrency, width: 110, dataType: 'double', state: { visible: false },
        listener: {
          onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
            if (event.row == cell.getRow() && (event.field.name === 'fixedPayment' || event.field.name === 'extraPayment')) {
              let bill = cell.getDisplayRecord().record;
              bill.profit = bill.adjustTotalShipmentCharge - bill.totalPayment;
              cell.forceUpdate()
            }
          },
          onAggregationDataCellEvent(cell: grid.VGridCell, event: grid.VGridCellEvent) {
            if (event.field.name === 'fixedPayment' || event.field.name === 'extraPayment' || event.field.name === 'adjustTotalShipmentCharge') {
              aggSumCell(cell, event, 'profit');
            }
          },
        },
        customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, focus: boolean) => {
          let cssClass = field.cssClass;
          if (field.computeCssClasses) cssClass = `${cssClass} ${field.computeCssClasses(ctx, dRecord)}`;
          return (
            <div className={`flex-hbox ${cssClass}`}>
              <div className='flex-grow-1 text-warning px-1 text-end'>{formatCurrency(dRecord.record[field.name])}</div>
            </div>
          );
        },
      },
      {
        name: '_verifyPaymentInfo', label: 'V.Pay', hint: T('Verify Payment Info'),
        filterable: true, filterableType: 'options', width: 60,
        fieldDataGetter(record) {
          let value = 'N/A';
          let verifyPaymentInfo = record['verifyPaymentInfo'];
          if (verifyPaymentInfo === true || verifyPaymentInfo === false) return verifyPaymentInfo;
          return value;
        },
        customRender: (_ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, focus: boolean) => {
          let bill = dRecord.record;
          let verifyPaymentInfo = bill['verifyPaymentInfo'];
          let verifyPaymentNote = bill['verifyPaymentNote'];
          const createContent = (content: any) => {
            if (!verifyPaymentNote) return (
              <div className='flex-vbox align-items-center' >
                {content}
              </div>
            );
            return (
              <bs.CssTooltip >
                <bs.CssTooltipToggle className='flex-vbox'>
                  {content}
                </bs.CssTooltipToggle>
                <bs.CssTooltipContent>
                  <div style={{ whiteSpace: 'pre-line' }}>
                    {verifyPaymentNote}
                  </div>
                </bs.CssTooltipContent>
              </bs.CssTooltip>
            )
          }
          if (verifyPaymentInfo == true) {
            return createContent(<FeatherIcon.CheckCircle className='text-success' size={16} />);
          }
          if (verifyPaymentInfo == false) {
            return createContent(<FeatherIcon.XCircle className='text-danger' size={16} />);
          }
          return null;
        }
      },
      { name: 'verifyPaymentNote', label: T('Verify Payment Note'), width: 50 },
      {
        name: '_closePayment', label: '', hint: T('Close Payment'), filterable: true, filterableType: 'options', width: 40,
        listener: {
          onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
            if (event.row == cell.getRow() && event.field.name === '_closePayment') {
              cell.forceUpdate()
            }
          },
        },
        fieldDataGetter(record) {
          let closePayment = record['closePayment'];
          if (closePayment) return 'CLOSE PAYMENT';
          return 'OPEN PAYMENT';
        },
        customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
          let bill = dRecord.record;
          const onChange = (date: any) => {
            if (!modCap && !date) return;
            bill['closePayment'] = date;
            let event: grid.VGridCellEvent = {
              row: dRecord.row, field: field, event: 'Modified', data: dRecord
            }
            ctx.broadcastCellEvent(event);
          }
          let closePayment = bill['closePayment'];
          if (!closePayment) return (
            <bs.Button laf='link' className='flex-vbox text-center'
              onClick={() => onChange(util.TimeUtil.javaCompactDateTimeFormat(new Date()))}>
              <FeatherIcon.Unlock size={16} className='text-success' />
            </bs.Button>
          );
          return (
            <bs.CssTooltip >
              <bs.CssTooltipToggle className='flex-vbox'>
                <bs.Button laf='link' className='flex-vbox text-center' onClick={() => onChange(null)}>
                  <FeatherIcon.Lock size={16} className='text-danger' />
                </bs.Button>
              </bs.CssTooltipToggle>
              <bs.CssTooltipContent>
                <div style={{ whiteSpace: 'pre-line' }}>
                  {closePayment}
                </div>
              </bs.CssTooltipContent>
            </bs.CssTooltip>
          )
        }
      },
      {
        name: '_verifyVehicleInfo', label: 'V.Vehicle', hint: T('Verify Vehicle Info'), filterable: true, filterableType: 'options', width: 60,
        fieldDataGetter(record) {
          let value = 'N/A';
          let verifyVehicleInfo = record['verifyVehicleInfo'];
          if (verifyVehicleInfo === true || verifyVehicleInfo === false) return verifyVehicleInfo;
          return value;
        },
        customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, focus: boolean) => {
          let bill = dRecord.record;
          let verifyPaymentInfo = bill['verifyVehicleInfo'];
          let verifyPaymentNote = bill['verifyVehicleInfoNote'];
          const createContent = (content: any) => {
            if (!verifyPaymentNote) return (
              <div className='flex-vbox align-items-center' >
                {content}
              </div>
            );
            return (
              <bs.CssTooltip >
                <bs.CssTooltipToggle className='flex-vbox'>
                  {content}
                </bs.CssTooltipToggle>
                <bs.CssTooltipContent>
                  <div style={{ whiteSpace: 'pre-line' }}>
                    {verifyPaymentNote}
                  </div>
                </bs.CssTooltipContent>
              </bs.CssTooltip>
            )
          }
          if (verifyPaymentInfo == true) {
            return createContent(<FeatherIcon.CheckCircle className='text-success' size={16} />);
          }
          if (verifyPaymentInfo == false) {
            return createContent(<FeatherIcon.XCircle className='text-danger' size={16} />);
          }
          return null;
        }
      },
      { name: 'verifyVehicleInfoNote', label: T('Verify Note'), width: 50 },
      {
        name: '_vehicleInfoLockDate', label: '', hint: T('Vehicle Info Lock Date'), filterable: true, filterableType: 'options', width: 40,
        fieldDataGetter(record) {
          let vehicleInfoLockDate = record['vehicleInfoLockDate'];
          if (vehicleInfoLockDate) return 'LOCK VEHICLE INFO';
          return 'OPEN VEHICLE INFO';
        },
        customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, focus: boolean) => {
          let bill = dRecord.record;
          let vehicleInfoLockDate = bill['vehicleInfoLockDate'];
          if (!vehicleInfoLockDate) return;
          return (
            <bs.CssTooltip >
              <bs.CssTooltipToggle className='flex-vbox'>
                <FeatherIcon.Lock size={16} className='text-danger' />
              </bs.CssTooltipToggle>
              <bs.CssTooltipContent>
                <div style={{ whiteSpace: 'pre-line' }}>
                  {vehicleInfoLockDate}
                </div>
              </bs.CssTooltipContent>
            </bs.CssTooltip>
          )
        }
      },
      {
        name: 'collect', label: T('Collect'),
        editor: {
          type: 'string',
          onInputChange: onInputChange
        }
      },
      {
        name: 'uploadError', label: T('Upload Error'), dataTooltip: true, filterable: true, filterableType: 'options'
      },
      { name: 'code', label: T('Code'), state: { visible: false } },
      {
        name: '_extension', label: T('Extension'), state: { visible: false },
        customRender: (_ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
          let renderBillAttachments = (tmsBill: any) => {
            let tmsBillId = tmsBill['id'];
            let vendorBillId = tmsBill['vendorBillId'];
            const onShow = () => {
              appContext.createHttpBackendCall('VehicleService', 'getDataTMSBillTripAttachment', { tmsBillId: tmsBillId })
                .withSuccessData((data: any) => {
                  const createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
                    let dataModels: Array<any> = data;
                    let uiTMSBillAttachment = (
                      <bs.TabPane>
                        {vendorBillId ?
                          <bs.Tab name='vendor-bill' label='Public' active>
                            <UIVendorAttachments readOnly={!uiRoot.props.pageContext.hasUserWriteCapability()}
                              appContext={appCtx} pageContext={pageCtx}
                              loadMethod={{ component: 'TMSVendorBillService', method: 'findTMSVendorAttachments', param: { id: vendorBillId } }}
                              commitMethod={{ component: 'TMSVendorBillService', method: 'saveTMSVendorAttachments', param: { id: vendorBillId } }}
                            />
                          </bs.Tab>
                          : null
                        }
                        <bs.Tab name='tms-bill' label='Private' active={vendorBillId ? false : true}>
                          <module.storage.UIAttachments
                            appContext={appCtx} pageContext={pageCtx}
                            readOnly={!writeCap}
                            commitURL={ManagementRestURL.tmsBill.saveAttachments(tmsBillId)}
                            loadURL={ManagementRestURL.tmsBill.loadAttachments(tmsBillId)}
                            onChange={(plugin) => {
                              if (tmsBill['billAttachmentCount'] != plugin.getModel().getRecords().length) {
                                tmsBill['billAttachmentCount'] = plugin.getModel().getRecords().length;
                                uiRoot.getVGridContext().getVGrid().forceUpdateView();
                              }
                            }}
                          />
                        </bs.Tab>
                      </bs.TabPane>
                    )
                    if (dataModels.length === 0) {
                      return uiTMSBillAttachment;
                    }
                    let uiVehicleTripAttachment = [];
                    for (let i = 0; i < dataModels.length; i++) {
                      let model = dataModels[i];
                      let vehicleLabel = model.vehicleLabel;
                      if (vehicleLabel === null) {
                        vehicleLabel = 'N/A'
                      }
                      let tripId = model.vehicleTripId;
                      if (!tripId) continue;
                      let active = i === 0;
                      uiVehicleTripAttachment.push(
                        <bs.Tab name={`vehicle-trip-${i}`} key={`vehicle-trip-${i}`} label={T(vehicleLabel)} active={active}>
                          <module.storage.UIAttachments readOnly={!writeCap}
                            appContext={appContext} pageContext={pageContext}
                            commitURL={VehicleFleetURL.vehicleTrip.saveAttachments(tripId)}
                            loadURL={VehicleFleetURL.vehicleTrip.loadAttachments(tripId)}
                          />
                        </bs.Tab>)
                    }
                    return (
                      <bs.VSplit smallScreenView="cards" >
                        <bs.VSplitPane width={'50%'}>
                          {uiTMSBillAttachment}
                        </bs.VSplitPane>
                        <bs.VSplitPane>
                          <bs.TabPane>
                            <bs.Card header={T('Vehicle Trip')} className='flex-vbox h-100'>
                              <bs.TabPane>
                                {uiVehicleTripAttachment}
                              </bs.TabPane>
                            </bs.Card>
                          </bs.TabPane>
                        </bs.VSplitPane>
                      </bs.VSplit>
                    )
                  }
                  pageContext.createPopupPage('attachments', T("Attachments"), createAppPage, { size: "xl", backdrop: "static" });

                })
                .call();
            }
            let count = tmsBill['billAttachmentCount'] + tmsBill['vehicleTripAttachmentCount']
            return TMSUtils.renderFileAttachmentsIcon(count, onShow);
          }
          return (
            <div className={'flex-hbox'}>
              {renderBillAttachments(dRecord.record)}
            </div>
          )
        }
      },
      {
        name: '_storageState', label: T('Storage State'), state: { visible: false },
        editor: {
          type: 'string',
          onInputChange: onInputChange,
          renderCustom(ctx: grid.FieldContext, onInputChange: grid.OnInputChange) {
            let bill = ctx.displayRecord.record;
            return (
              <input.BBSelectField
                bean={bill} field={'storageState'} options={['ACTIVE', 'ARCHIVED']} tabIndex={ctx.tabIndex} focus={ctx.focus}
                onInputChange={onInputChange} disable={!pageContext.hasUserWriteCapability()} />
            )
          },
        }
      },
      {
        name: '_actions', label: T('Acs'), container: 'fixed-right', width: 80, cssClass: 'p-0',
        customHeaderRender(ctx, field, headerEle) {
          return (
            <div className='flex-hbox'>
              <div className='flex-grow-1'>
                <bs.Badge laf='primary' className='flex-vbox p-1 w-100 align-items-center justify-content-center'>
                  <FeatherIcon.BookOpen size={15} />
                </bs.Badge>
              </div>
              <NotificationMessage context={ctx} fieldName={field.name} />
            </div>
          )
        },
        listener: {
          onDataCellEvent(cell, event) {
            if (event.field.name === '_actions' && cell.getRow() === event.row) {
              cell.forceUpdate();
            }
          },
        },
        customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
          if (!dRecord.isDataRecord()) return;
          let record = dRecord.record;
          let issueTemplate = {
            'refEntityId': record['id'],
            'refEntity': 'lgc_tms_bill',
            'label': record['label'],
            'label2': record['customerFullName'],
            'resolverAccountId': record['responsibleAccountId'],
            'resolverAccountFullName': record['responsibleFullName'],
            'status': 'CONFIRMED',
          }
          if (record['jobTrackingId']) {
            issueTemplate['refEntity'] = 'lgc_job_tracking';
            issueTemplate['refEntityId'] = record['jobTrackingId'];
          } record
          let billTemplate = {
            tmsBillId: record['id'],
            billIssueTotal: record['billIssueTotal'],
            jobTrackingIssueTotal: record['jobTrackingIssueTotal']
          }
          return (
            <div className={'flex-hbox'}>
              {/* <WIconPopupTMSJobTrackingIssue
                appContext={appContext} pageContext={pageContext}
                template={issueTemplate} billTemplate={billTemplate}
                onUpdateIssueTotal={(val) => {
                  record['billIssueTotal'] = record['billIssueTotal'] + val;
                  ctx.getVGrid().forceUpdateView();
                }}
              /> */}
              {renderRoundUsedStatus(ctx, record)}
              {/* {renderOpsStatus(record)} */}
              {renderTrackingButton(ctx, record)}
              {renderBtnTrackingAPI(ctx, record, { cssClass: 'px-0 py-1' })}
            </div>
          )
        },
      },
      {
        name: 'jobTracking', label: '', container: 'fixed-right', width: 40, state: { visible: !!jobTrackingProject },
        customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
          let record = dRecord.record;
          return (
            <WBtnTMSBillJobTracking context={ctx} bill={record} jobTrackingProject={jobTrackingProject} />
          )
        },
      },
      {
        name: 'processStatus', label: T('Status'), sortable: true, filterableType: 'options', filterable: true,
        container: 'fixed-right', width: 105,
        listener: {
          onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
            if (event.row == cell.getRow() && event.field.name === 'processStatus') {
              cell.forceUpdate()
            }
          },
        },
        customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
          if (!dRecord.isDataRecord()) return;
          let bill = dRecord.record;
          let status = bill['processStatus'];
          let color = TMSBillProcessStatusTools.getColor(status);
          const allValues: Array<any> = Object.values(TMSBillProcessStatus);
          return (
            <bs.Popover style={{ width: 66 }} className="d-flex flex-center w-100"
              title={T('Status')} closeOnTrigger=".btn" >
              <bs.PopoverToggle
                className={`flex-hbox flex-center p-1 rounded-2 bg-${color}-subtle text-${color} w-100 `}>
                <span>{TMSBillProcessStatusTools.getIcon(status)} {TMSBillProcessStatusTools.getLabel(status)}</span>
              </bs.PopoverToggle>
              <bs.PopoverContent>
                <div className='flex-vbox gap-2' style={{ width: '200px' }}>
                  {allValues.map((value: TMSBillProcessStatus) => {
                    return (
                      <bs.Button laf={TMSBillProcessStatusTools.getColor(value)} onClick={() => {
                        bill['processStatus'] = value;
                        let event: grid.VGridCellEvent = {
                          row: dRecord.row, field: field, event: 'Modified', data: dRecord
                        }
                        ctx.broadcastCellEvent(event);
                      }}>
                        {TMSBillProcessStatusTools.getIcon(value)} {TMSBillProcessStatusTools.getLabel(value)}
                      </bs.Button>
                    )
                  })}
                </div>
              </bs.PopoverContent>
            </bs.Popover>
          )
        },
      },
      ...TMSVGridConfigTool.ENTITY_COLUMNS
    ],
    fieldGroups: {
      "billInfo": {
        label: T('Bill Info'),
        visible: true,
        fields: [
          'label', 'customerFullName', 'editCus', 'hwbNo'
        ]
      },
      "deliveryPlan": {
        label: T('Delivery Plan'),
        visible: true,
        fields: [
          'time', 'estimateTime', 'description', 'tmsTrackingDescription', 'licensePlate', 'truckNo', 'vendorFullName', 'reportBFSOne',
          'tmsTrackingFleetLabel', 'dateTime', 'planPickupSuccessTime', 'planDeliverySuccessTime'
        ]
      },
      "goodsInfo": {
        label: T('Quantity Info'),
        visible: true,
        fields: [
          'truckType', 'mode', 'goodsDescription', 'containerNo', 'sealNo',
          'quantity', 'quantityUnit', 'weight', 'chargeableWeight', 'volumeAsText', 'volume', 'chargeableVolume', 'volumeUnit',
          'carrierFullName', 'bookingCode', 'etaCutOffTime', 'warehouseLabel', 'declarationNumber'
        ]
      },
      'pickupInfo': {
        label: T('Pickup Info'),
        visible: true,
        fields: [
          'senderFullName',
          'senderAddress', 'senderContact', 'senderLocationAddress'
        ]
      },
      'deliveryInfo': {
        label: T('Delivery Info'),
        visible: true,
        fields: [
          'receiverFullName',
          'receiverAddress', 'receiverContact', 'receiverLocationAddress'
        ]
      },
      'revenue': {
        label: T('Revenue'),
        visible: true,
        fields: [
          'adjustTotalShipmentCharge', 'adjustTotalTax', 'adjustFinalCharge']
      },
      'cost': {
        label: T('Cost'),
        visible: true,
        fields: [
          'payOnBehalfCustomerName', 'fixedPayment', 'extraPayment', 'matchPrice',
          'totalPayment', 'paymentStatus', 'totalPaymentTax', 'finalPayment', 'paymentNote', 'feedback', 'vendorCostStatus'
        ]
      },
      'vendorCost': {
        label: T('Cước Thầu Phụ'),
        visible: true,
        fields: [
          'vendorFixed', 'vendorExtra', 'vendorCost'
        ]
      },
    },
    summary: {
      dataCellHeight: 210,
      render: (ctx: grid.VGridContext, dRecord: grid.DisplayRecord, _viewMode: grid.ViewMode) => {
        return renderSummary(ctx, dRecord);
      }
    },
  }

  if (!pageContext.hasUserWriteCapability() || readOnly) delete records.editor;
  if (records.editor) {
    for (let sel of records.fields) {
      if (sel.editor) {
        sel.editor.enable = records.editor.enable;
        if (sel.name === 'label') continue;
        if (!sel.editor.renderCustom) {
          if (sel.editor.type === 'string') {
            sel.editor.renderCustom = (fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
              let { displayRecord, fieldConfig, focus, tabIndex } = fieldCtx;
              let bean = displayRecord.record;
              return <input.BBStringField
                focus={focus} tabIndex={tabIndex} bean={bean} field={fieldConfig.name} disable={!writeCap}
                onInputChange={onInputChange} />
            }
          }
          if (sel.editor.type === 'double') {
            sel.editor.renderCustom = (fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
              let { displayRecord, fieldConfig, focus, tabIndex } = fieldCtx;
              let bean = displayRecord.record;
              return <input.BBDoubleField
                focus={focus} tabIndex={tabIndex} bean={bean} field={fieldConfig.name} disable={!writeCap}
                onInputChange={onInputChange} />
            }
          }
        }
        const renderCustom = sel.editor.renderCustom;
        if (renderCustom) {
          sel.editor.renderCustom = (fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
            let { displayRecord, gridContext, fieldConfig, focus } = fieldCtx;
            let closePayment = displayRecord.record['closePayment'];
            if (closePayment) {
              if (sel.customRender) return sel.customRender(gridContext, fieldConfig, displayRecord, focus);
              if (sel.fieldDataGetter) return sel.fieldDataGetter(displayRecord.record);
              let value = displayRecord.getValue(fieldConfig.name);
              if (sel.format) value = sel.format(displayRecord.getValue(fieldConfig.name));
              let css = 'cell-text';
              if (sel.dataType === 'double' || sel.dataType === 'currency') css = 'cell-number';
              return (
                <div className={`flex-vbox ${css}`}>
                  {value}
                </div>
              )
            }
            return renderCustom(fieldCtx, onInputChange);
          }
        }
      }
    }
  }
  if (bs.ScreenUtil.isSmallScreen()) delete records.control;
  return records;
}

export function renderBtnEditTMSCustomer(
  ctx: grid.VGridContext, customerId: number, customerFullName: string,
  onPostCommit: (customer: any, actions: entity.ModifyBeanActions) => void) {
  let uiRoot = ctx.uiRoot as entity.DbEntityList;
  let { appContext, pageContext } = uiRoot.props;
  let onClick = () => {
    let successCallback = (refEntity: any) => {
      let actions = entity.ModifyBeanActions.MODIFY;
      if (!refEntity) {
        actions = entity.ModifyBeanActions.CREATE;
        refEntity = {
          label: customerFullName,
          shortName: customerFullName,
          permissions: [],
        }
      }
      let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
        return (
          <ConvertBFSOneCustomerEditor readOnly={!pageContext.hasUserWriteCapability()}
            appContext={appCtx} pageContext={pageCtx} observer={new entity.ComplexBeanObserver({ 'partner': refEntity })}
            onPostCommit={(bean) => {
              onPostCommit(bean, actions);
              pageCtx.back();
            }} />
        )
      }
      pageContext.createPopupPage('ref-entity', 'Ref Entity', createAppPage, { size: 'lg' });
    }
    appContext
      .createHttpBackendCall('TMSCustomerService', 'loadTMSPartnerByCustomerId', { 'customerId': customerId })
      .withSuccessData(successCallback)
      .call();
  }
  let color: 'primary' | 'secondary' | 'link' | 'info' | 'success' | 'danger' | 'warning' = customerId ? 'info' : 'warning';
  return (
    <bs.Button laf={color} outline style={{ height: 15, width: 25, fontSize: 11, borderRadius: 3 }} className={`p-0 mx-1`}
      onClick={onClick}>
      {'CUS'}
    </bs.Button>
  )
}
