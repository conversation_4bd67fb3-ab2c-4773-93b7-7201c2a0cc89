import React from 'react';
import * as FeatherIcon from 'react-feather';
import { grid, input, bs, util, app, entity } from '@datatp-ui/lib';
import { module } from "@datatp-ui/erp";

import { T } from '../../backend';

import { TMSBillTransportationModeTools, TMSUtils } from '../../utils';
import { UITMSBillReceiptOfDeliveryList } from './UIVehicleTripGoodsTracking';
import { UITMSBillListPlugin } from '../../bill/UITMSBillList';
import { UITMSReceiptOfDeliveryPrint } from '../../bill/UITMSBillPrint';
import { BBRefTransporter } from '../../transport/BBRefTransporter';
import { UIVehicleTripGoodsTrackingListPlugin } from './UIVehicleTripGoodsTrackingList';
import { BBRefVehicleFleet } from '../BBRefVehicleFleet';
import { BBRefVehicle } from '../BBRefVehicle';

interface UISplitVehicleTripGoodsTrackingProps extends entity.AppDbEntityEditorProps {
  currentTrackingId: number;
}
export class UISplitVehicleTripGoodsTracking extends entity.AppDbEntityEditor<UISplitVehicleTripGoodsTrackingProps> {
  tmsBillOb: entity.ComplexBeanObserver;
  styleWarning = {};
  goodsTrackingListPlugin: UIVehicleTripGoodsTrackingListPlugin;

  constructor(props: UISplitVehicleTripGoodsTrackingProps) {
    super(props);
    let { appContext, observer } = this.props;
    this.goodsTrackingListPlugin = new UIVehicleTripGoodsTrackingListPlugin().withTMSBillId(observer.getBeanProperty('tmsBillId'));
    let tmsBillId = observer.getBeanProperty('tmsBillId');

    appContext.createHttpBackendCall('TMSBillService', 'getTMSBillById', { id: tmsBillId })
      .withSuccessData((data: any) => {
        this.tmsBillOb = new entity.ComplexBeanObserver(data);
        this.forceUpdate();
      })
      .call()
  }

  onConfirm = () => {
    let { appContext, pageContext, observer, onPostCommit } = this.props;
    let params = {
      model: {
        generalGoodsTrackingInfo: observer.getMutableBean(),
        splitGoodsTrackings: this.goodsTrackingListPlugin.getRecords()
      }
    }

    appContext.createHttpBackendCall('VehicleRestCallService', 'splitVehicleTripGoodsTracking', params)
      .withSuccessData((data: any) => {
        let trackings: Array<any> = data;
        let ids: Array<any> = [];
        trackings.forEach(sel => ids.push(sel['id']));

        let params = {
          params: { ids: ids }
        }
        appContext.createHttpBackendCall('VehicleRestCallService', 'searchVehicleTripGoodsTrackings', { params: params })
          .withSuccessData((data: any) => {
            if (onPostCommit) onPostCommit(data);
            pageContext.back();
            appContext.addOSNotification("success", T('Split Tracking Success!'));
          })
          .call();
      })
      .call();
  }

  onRenderAddress = () => {
    let { observer } = this.props;
    let tracking = observer.getMutableBean();
    let mode = tracking.mode;
    let height = '90px';
    let span = 12;
    if (TMSBillTransportationModeTools.isDomestic(mode)) span = 6;
    return (
      <bs.Row>
        {TMSBillTransportationModeTools.isExport(mode) || TMSBillTransportationModeTools.isDomestic(mode) ?
          <bs.Col span={span}>
            <input.BBTextField placeholder='From address' style={{ height: height }} className={'text-info'}
              bean={tracking} field={'senderAddress'} label={'From Address'} disable />
          </bs.Col>
          : <></>
        }
        {TMSBillTransportationModeTools.isImport(mode) || TMSBillTransportationModeTools.isDomestic(mode) ?
          <bs.Col span={span}>
            <input.BBTextField placeholder='To address' style={{ height: height }} className={'text-success'}
              bean={tracking} field={'receiverAddress'} label={'To Address'} disable />
          </bs.Col>
          : <></>
        }
      </bs.Row>
    )
  }

  render(): React.ReactNode {
    let { appContext, pageContext, observer, currentTrackingId } = this.props;
    let tracking = observer.getMutableBean();
    if (!this.tmsBillOb) return;
    let tmsBill = this.tmsBillOb.getMutableBean();
    let mode = tracking['mode'];
    let receiver = tmsBill['receiver'];
    let sender = tmsBill['sender'];
    let title = T(`Pickup Location`);
    if (TMSBillTransportationModeTools.isImport(mode)) title = T(`Delivery Location`);
    return (
      <div className='flex-vbox' >
        <bs.Card className='flex-grow-0 flex-hbox' header={`${title} : ${tmsBill.customer.customerFullName}`}
          style={{ height: 175, fontSize: '16px', ...this.styleWarning }}>
          <div className='flex-vbox flex-grow-0 justify-content-center' style={{ width: '40%' }}>
            <div className='px-2'>
              <span className='fw-bolder'>{T('Address :')}</span>
              <span className='px-1'>
                {TMSBillTransportationModeTools.isImport(mode) ? receiver.receiverAddress : sender.senderAddress}
              </span>
            </div>
            <div className='px-2'>
              <span className='fw-bolder'>{T('Contact :')}</span>
              <span className='px-1'>
                {TMSBillTransportationModeTools.isImport(mode) ? receiver.receiverContact : sender.senderContact}
              </span>
            </div>
          </div>
          <UITMSBillReceiptOfDeliveryList appContext={appContext} pageContext={pageContext}
            plugin={new UITMSBillListPlugin().withIds([tmsBill['id']]).withDataScope(app.AppDataScope.COMPANY)} />
        </bs.Card>
        <div className='flex-vbox flex-grow-1 '>
          <UISplitVehicleTripGoodsTrackingList appContext={appContext} pageContext={pageContext} currentTrackingId={currentTrackingId}
            plugin={this.goodsTrackingListPlugin} tmsBill={tmsBill} onCommit={this.onConfirm}
            onVerifyQuantity={(_records: Array<any>, state) => {
              if (state === 'warning') {
                this.styleWarning = { border: '2px solid rgba(255, 48, 48, 0.68)', borderRadius: 5 };
              } else {
                this.styleWarning = {};
              }
              this.forceUpdate();
            }} />
        </div>
      </div>
    )
  }
}
interface UISplitVehicleTripGoodsTrackingListProps extends entity.DbEntityListProps {
  currentTrackingId: number;
  tmsBill: any;
  onVerifyQuantity(tracking: Array<any>, state: 'warning' | 'none'): void;
  onCommit(): void;
}
class UISplitVehicleTripGoodsTrackingList extends entity.DbEntityList<UISplitVehicleTripGoodsTrackingListProps> {
  popoverId: string = `popover-${util.IDTracker.next()}`;
  onCalculate = (record: any, excludeRow?: number) => {
    let { plugin, tmsBill } = this.props;
    let records = plugin.getRecords();
    if (excludeRow != null) {
      let holder: Array<any> = [];
      plugin.getListModel().getDisplayRecordList().getDisplayRecords().forEach(sel => {
        if (sel.row != excludeRow) {
          holder.push(sel.record);
        }
      });
      records = holder;
    };

    let sum = util.CollectionMath.sum(records, ['quantity', 'weight']);
    let tmsBillGoods = tmsBill.tmsBillGoods;
    let quantity = tmsBillGoods['quantity'] - sum['quantity'];
    let weight = tmsBillGoods['weight'] - sum['weight'];
    let volume = 0;
    let volumeAsText = tmsBillGoods['volumeAsText'];
    if (volumeAsText && TMSUtils.isDecimal(volumeAsText)) {
      let billVolume = Number(volumeAsText);
      let totalVolume = 0;
      records.forEach(sel => {
        if (sel['volumeAsText'] && TMSUtils.isDecimal(sel['volumeAsText'])) totalVolume += Number(sel['volumeAsText']);
      })
      volume = billVolume - totalVolume;
      volume = Math.round(volume * 100) / 100;
    }
    record.quantity = quantity;
    record.weight = weight;
    record.volume = volume;
    record.volumeAsText = `${volume}`;
    return record;
  }

  createVGridConfig() {
    let { appContext, pageContext, plugin, tmsBill, currentTrackingId, onVerifyQuantity } = this.props;
    let tmsBillGoods = tmsBill['tmsBillGoods'];

    let onInputChange = (fieldCtx: grid.FieldContext, oldVal: any, newVal: any) => {
      let dRecord = fieldCtx.displayRecord;
      let field = fieldCtx.fieldConfig;
      let ctx = fieldCtx.gridContext
      let event: grid.VGridCellEvent = {
        row: dRecord.row, field: field, event: 'Modified', data: dRecord
      };
      ctx.broadcastCellEvent(event);
    }

    let config: grid.VGridConfig = {
      record: {
        dataCellHeight: 30,
        computeDataRowHeight: (_ctx: grid.VGridContext, dRec: grid.DisplayRecord) => {
          let rec = dRec.record;
          if (rec.rowHeight > 0) return rec.rowHeight;
          return 30;
        },
        onchangeDataRowHeight: (_ctx: grid.VGridContext, dRec: grid.DisplayRecord, deltaY: number) => {
          let rec = dRec.record;
          let currHeight = rec.rowHeight;
          if (currHeight < 10) currHeight = grid.DEFAULT.row.height;
          rec.rowHeight = currHeight + deltaY;
          let state = dRec.getRecordState();
          state.markModified();
          return true;
        },
        control: {
          width: 115,
          items: [
            {
              name: 'delete', hint: 'delete', icon: FeatherIcon.Trash,
              customRender: (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
                let onClick = () => {
                  plugin.getListModel().removeRecord(dRecord.record);
                  this.nextViewId();
                  this.forceUpdate();
                }
                return (
                  <bs.Button laf='link' disabled={dRecord.record['id']} onClick={onClick}>
                    <FeatherIcon.Trash size={14} />
                  </bs.Button>
                )
              },
            },
            {
              name: 'calculate', hint: 'calculate', icon: FeatherIcon.Edit,
              customRender: (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
                let onClick = () => {
                  this.onCalculate(dRecord.record, dRecord.row);
                  onVerifyQuantity(plugin.getRecords(), 'none');
                  this.nextViewId();
                  this.forceUpdate();
                }
                return (
                  <bs.Button className='mx-1' laf='link' onClick={onClick}>
                    <FeatherIcon.Edit size={14} />
                  </bs.Button>
                )
              },
            },
            {
              name: 'copy', hint: 'Copy', icon: FeatherIcon.Plus,
              customRender: (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
                if (plugin.getRecords().length - 1 != dRecord.row) return <div className='mx-1' style={{ width: 16 }}></div>;
                let onClick = () => {
                  let record = dRecord.record;
                  let newRecord = {
                    updateTracking: true,
                    billLabel: record['billLabel'],
                    label: record['billLabel'],
                    fileTrucking: record['fileTrucking'],
                    quantity: 0,
                    weight: 0,
                    quantityUnit: record['quantityUnit'],
                    weightUnit: record['weightUnit'],
                    volume: 0,
                    volumeAsText: '0',
                    volumeUnit: record['volumeUnit'],
                    responsibleAccountId: record['responsibleAccountId'],
                    responsibleFullName: record['responsibleFullName'],
                    uikey: `tracking/${util.IDTracker.next()}`
                  };
                  this.onCalculate(newRecord);
                  ctx.model.insertDisplayRecordAt(dRecord.row, newRecord);
                  ctx.model.getDisplayRecordList().updateDisplayRecords();
                  onVerifyQuantity(plugin.getRecords(), 'none');
                  this.nextViewId();
                  this.forceUpdate();
                }
                return (
                  <bs.Button className='mx-1' laf='link' onClick={onClick}>
                    <FeatherIcon.PlusSquare size={14} />
                  </bs.Button>
                )
              },
            },
            {
              name: 'split', hint: 'split', icon: FeatherIcon.DivideSquare,
              customRender: (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
                let record = dRecord.record;
                let onSplit = (split: number) => {
                  let splitQuantity = Math.round(record['quantity'] / split * 100) / 100;
                  let splitWeight = Math.round(record['weight'] / split * 100) / 100;

                  let splitVolume = 0;
                  let volumeAsText = record['volumeAsText'];
                  if (volumeAsText && TMSUtils.isDecimal(volumeAsText)) {
                    let volume = Number(volumeAsText);
                    splitVolume = Math.round(volume / split * 100) / 100;
                  }

                  for (let i = 0; i < split - 1; i++) {
                    let record = dRecord.record;
                    let newRecord = {
                      billLabel: record['billLabel'],
                      label: record['billLabel'],
                      fileTrucking: record['fileTrucking'],
                      quantity: splitQuantity,
                      weight: splitWeight,
                      quantityUnit: record['quantityUnit'],
                      weightUnit: record['weightUnit'],
                      volume: splitVolume,
                      volumeAsText: `${splitVolume}`,
                      volumeUnit: record['volumeUnit'],
                      fromLocationLabel: record['fromLocationLabel'],
                      toLocationLabel: record['toLocationLabel'],
                      fromAddress: record['fromAddress'],
                      toAddress: record['toAddress'],
                      rowHeight: record['rowHeight'],
                      // status: record['status'],
                      responsibleAccountId: record['responsibleAccountId'],
                      responsibleFullName: record['responsibleFullName'],
                      uikey: `tracking/${util.IDTracker.next()}`
                    };
                    ctx.model.insertDisplayRecordAt(dRecord.row, newRecord);
                    ctx.model.getDisplayRecordList().updateDisplayRecords();
                  }

                  record['quantity'] = splitQuantity;
                  record['weight'] = splitWeight;
                  if (splitVolume) {
                    record['volume'] = splitVolume;
                    record['volumeAsText'] = `${splitVolume}`;
                  }
                  this.forceUpdate();
                }
                let splitMode = [2, 3, 4];
                let renderButton = () => {
                  let buttons: Array<any> = [];
                  splitMode.forEach(sel => {
                    buttons.push(
                      <bs.Button key={`divide-${sel}`} className='py-1 px-2 flex-hbox' style={{ marginBottom: 2 }}
                        laf='secondary' outline onClick={() => onSplit(sel)}>
                        <FeatherIcon.Divide size={14} />
                        <div>{`${sel}`}</div>
                      </bs.Button>
                    );
                  })
                  return buttons;
                }
                return (
                  <div className='flex-hbox'>
                    <bs.Popover className='mx-1' flex-hbox-grow-0 closeOnTrigger=".btn">
                      <bs.PopoverToggle style={{ height: 14, width: 20, padding: 0 }} laf='info' outline >
                        <FeatherIcon.ChevronsDown style={{ marginBottom: 5 }} size={12} />
                      </bs.PopoverToggle>
                      <bs.PopoverContent className='dropdown-content'>
                        <div className='flex-vbox'>
                          {renderButton()}
                        </div>
                      </bs.PopoverContent>
                    </bs.Popover>
                  </div>
                )
              },
            },
            {
              name: 'current-tracking', hint: 'Current Tracking', icon: FeatherIcon.ArrowRight,
              customRender: (_ctx: grid.VGridContext, _dRecord: grid.DisplayRecord) => {
                let rec = _dRecord.record;
                if (rec['id'] !== currentTrackingId) return;
                return (
                  <FeatherIcon.ArrowRight className='text-primary mx-1' size={14} />
                )
              },
            }
          ]
        },
        editor: {
          supportViewMode: ['table'],
          enable: true,
        },
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          {
            name: 'billLabel', label: 'File No.', width: 180,
            computeCssClasses: (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
              let rec = dRecord.record;
              return rec['id'] === currentTrackingId ? 'text-primary' : '';
            },
          },
          {
            name: 'fileTrucking', label: T('File Trucking'), hint: T('File Trucking'),
            editor: { type: 'string', enable: true }, state: { visible: false }
          },
          {
            name: 'quantity', label: T('Quantity'), width: 80, dataType: 'double', hint: 'Số kiện',
            listener: {
              onFooterDataCellEvent(cell: grid.VGridCell, event: grid.VGridCellEvent) {
                if (event.field.name === 'quantity') {
                  let total = cell.getDisplayRecord().record;
                  let sum = util.CollectionMath.sum(plugin.getRecords(), ['quantity']);
                  total['quantity'] = sum['quantity'];
                  if (total['quantity'] > tmsBillGoods['quantity']) {
                    onVerifyQuantity(plugin.getRecords(), 'warning');
                  } else {
                    onVerifyQuantity(plugin.getRecords(), 'none');
                  }
                  cell.forceUpdate();
                }
              }
            },
            editor: { type: 'double', enable: true, onInputChange: onInputChange },
            state: { visible: false }
          },
          {
            name: 'quantityUnit', label: T('Q.Unit'), hint: "Đơn Vị", width: 100,
            editor: {
              type: 'string', enable: true,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let field = fieldCtx.fieldConfig;
                let forwarder = dRecord.record;
                return (
                  <module.settings.BBRefUnit
                    appContext={appContext} pageContext={pageContext}
                    minWidth={300}
                    placement="left"
                    placeholder="Enter Unit"
                    groupNames={['packing']}
                    bean={forwarder} beanIdField={field.name}
                    tabIndex={tabIndex} autofocus={focus}
                    onPostUpdate={(inputUI, bean, selectOpt, userInput) => {
                      onInputChange(bean, field.name, '', bean[field.name])
                    }} />
                )
              },
            },
            state: { visible: false }
          },
          {
            name: 'weight', label: T('Weight'), width: 80, hint: 'Khối Lượng',
            listener: {
              onDataCellEvent(cell: grid.VGridCell, event: grid.VGridCellEvent) {
                if (event.field.name === 'quantity' && cell.getRow() === event.row) {
                  let weightUnit = tmsBillGoods['weight'] / tmsBillGoods['quantity'];
                  let record = cell.getDisplayRecord().record;
                  record['weight'] = weightUnit * record['quantity'];
                }
              },
              onFooterDataCellEvent(cell: grid.VGridCell, event: grid.VGridCellEvent) {
                if (event.field.name === 'weight') {
                  let total = cell.getDisplayRecord().record;
                  let sum = util.CollectionMath.sum(plugin.getRecords(), ['weight']);
                  total['weight'] = sum['weight'];
                  if (total['weight'] > tmsBillGoods['weight']) {
                    onVerifyQuantity(plugin.getRecords(), 'warning');
                  } else {
                    onVerifyQuantity(plugin.getRecords(), 'none');
                  }
                  cell.forceUpdate();
                }
              }
            },
            editor: {
              type: 'double', enable: true,
              onInputChange: onInputChange,
            },
            state: { visible: false }
          },
          {
            name: 'weightUnit', label: T('W.Unit'), hint: "Weight Unit", state: { visible: false },
            editor: {
              type: 'string', enable: true,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let field = fieldCtx.fieldConfig;
                let forwarder = dRecord.record;
                return (
                  <module.settings.BBRefUnit
                    appContext={appContext} pageContext={pageContext}
                    minWidth={300}
                    placement="left"
                    placeholder="Enter Unit"
                    groupNames={['weight']}
                    bean={forwarder} beanIdField={field.name}
                    tabIndex={tabIndex} autofocus={focus}
                    onPostUpdate={(inputUI, bean, selectOpt, userInput) => {
                      onInputChange(bean, field.name, '', bean[field.name])
                    }} />
                )

              },
            }
          },
          {
            name: 'volumeAsText', label: T('Volume'), width: 130, cssClass: 'flex-grow-1 text-end', hint: 'Thể Tích',
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row == cell.getRow() && (event.field.name === 'volumeAsText')) {
                  let bill = cell.getDisplayRecord().record;
                  let volumeAsText = bill['volumeAsText'];
                  if (TMSUtils.isDecimal(volumeAsText)) {
                    bill['volume'] = Number(volumeAsText);
                  }
                }
              },
              onFooterDataCellEvent(cell: grid.VGridCell, event: grid.VGridCellEvent) {
                if (event.field.name === 'volumeAsText') {
                  let records = plugin.getRecords();
                  let total = cell.getDisplayRecord().record;
                  let totalVolume = 0;
                  records.forEach(sel => {
                    if (sel['volumeAsText'] && TMSUtils.isDecimal(sel['volumeAsText'])) totalVolume += Number(sel['volumeAsText']);
                  });

                  let volumeAsText = tmsBillGoods['volumeAsText'];
                  if (volumeAsText && TMSUtils.isDecimal(volumeAsText)) {
                    let billVolume = Number(volumeAsText);
                    if (totalVolume > billVolume) {
                      onVerifyQuantity(records, 'warning');
                    } else {
                      onVerifyQuantity(records, 'none');
                    }
                  }
                  total['volumeAsText'] = totalVolume;
                  cell.forceUpdate();
                }
              }
            },
            editor: {
              type: 'string', enable: true,
              onInputChange: onInputChange
            },
            state: { visible: false }
          },
          {
            name: 'pickupLocation', label: T('Pickup'), cssClass: 'flex-grow-1 text-center', hint: 'Điểm Lấy Hàng',
            width: 60,
            editor: {
              type: 'string'
            },
          },
          {
            name: 'deliveryLocation', label: T('Delivery'), cssClass: 'flex-grow-1 text-center', hint: 'Điểm Trả Hàng',
            width: 65,
            editor: {
              type: 'string'
            }
          },
          {
            name: 'fromAddress', label: T('From Address'), width: 200,
            state: { visible: false },
            editor: {
              type: 'text',
              enable: true
            }
          },
          {
            name: 'toAddress', label: T('To Address'), width: 200,
            state: { visible: false },
            editor: {
              type: 'text',
              enable: true
            }
          },
          {
            name: 'transportType', label: T('Transport Type'), width: 130,
            editor: {
              type: 'string',
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let field = fieldCtx.fieldConfig;
                let tripGoods = dRecord.record;
                return (
                  <input.BBSelectField tabIndex={tabIndex} focus={focus}
                    bean={tripGoods} field={field.name} options={['', 'Nguyên chuyến', 'Kết hợp', 'Ghép']} onInputChange={onInputChange} />
                )
              },
            }
          },
          {
            name: 'estimateDistanceInKm', label: T('Km'), dataType: 'double', hint: T('Km'),
            editor: {
              type: 'double',
              enable: true
            }
          },
          {
            name: 'fleetLabel', label: T('Fleet'), hint: 'Đội Xe',
            editor: {
              type: 'string', enable: true,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let field = fieldCtx.fieldConfig;
                let tracking = dRecord.record;
                return (
                  <BBRefVehicleFleet minWidth={400} tabIndex={tabIndex} autofocus={focus} allowUserInput
                    appContext={appContext} pageContext={pageContext}
                    bean={tracking} beanIdField={'fleetId'} beanLabelField={'fleetLabel'} placeholder={'Fleet'}
                    onPostUpdate={(inputUI: React.Component, bean: any, selectOpt: any, userInput: string) => onInputChange(bean, field.name, '', bean[field.name])}
                  />
                )
              }
            }
          },
          {
            name: 'vehicleLabel', label: T('License plate'), hint: 'Biển Số',
            editor: {
              type: 'string', enable: true,
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let field = fieldCtx.fieldConfig;
                let tracking = dRecord.record;
                return (
                  <BBRefVehicle appContext={appContext} pageContext={pageContext} placeholder='Vehicle'
                    placement="bottom-start" tabIndex={tabIndex} autofocus={focus} allowUserInput minWidth={500}
                    bean={tracking} beanIdField={'vehicleId'} beanLabelField={'vehicleLabel'}
                    onPostUpdate={(inputUI: React.Component, bean: any, selectOpt: any, userInput: string) => {
                      if (selectOpt && !tracking['vehicleType']) tracking['vehicleType'] = selectOpt['vehicleType'];
                      onInputChange(bean, field.name, '', bean[field.name])
                    }}
                  />
                )
              }
            }
          },

          {
            name: 'vehicleType', label: T('Vehicle Type'), hint: 'Loại Xe',
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row == cell.getRow() && event.field.name === 'vehicleLabel') {
                  cell.forceUpdate()
                }
              },
            },
            editor: {
              type: 'string', enable: true,
            }
          },
          {
            name: 'driverFullName', label: T('Driver'), hint: 'Lái Xe', width: 175,
            editor: {
              type: 'string', enable: true,
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let field = fieldCtx.fieldConfig;
                let tracking = dRecord.record;
                return (
                  <BBRefTransporter
                    appContext={appContext} pageContext={pageContext}
                    minWidth={300} placement="left" placeholder="Enter Transporter"
                    bean={tracking} beanIdField={'driverId'} beanLabelField={'driverFullName'}
                    tabIndex={tabIndex} autofocus={focus} allowUserInput
                    refTransporterBy={'id'} onPostUpdate={(inputUI, bean, selectOp, userInput) => {
                      if (selectOp) tracking['mobile'] = selectOp['mobile'];
                      onInputChange(bean, field.name, '', bean[field.name])
                    }} />
                )
              }
            }
          },
          {
            name: 'mobile', label: T('Mobile'), hint: 'Số Điện Thoại Lái Xe',
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row == cell.getRow() && event.field.name === 'driverFullName') {
                  cell.forceUpdate()
                }
              },
            },
            editor: {
              type: 'string', enable: true,
            }
          },
        ],
      },
      toolbar: {
        actions: [],
      },
      footer: {
        page: {
          render: (ctx: grid.VGridContext) => {
            let { tmsBill, onCommit } = this.props;
            return (
              <bs.Toolbar className='border flex-grow-0' >
                <entity.WButtonEntityWrite icon={FeatherIcon.Printer}
                  appContext={appContext} pageContext={pageContext}
                  label={T('Print')} onClick={() => this.onPrintPOD(tmsBill['id'])} />
                <entity.WButtonEntityWrite
                  appContext={appContext} pageContext={pageContext} icon={FeatherIcon.GitMerge}
                  label={T('Confirm')} onClick={onCommit} />
              </bs.Toolbar>
            );
          }
        },
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table',
            footer: {
              createRecords: (ctx: grid.VGridContext) => {
                let { plugin } = this.props;
                let records = plugin.getListModel().getFilterRecords();
                let footerRecords = new Array<any>();
                let total = util.CollectionMath.sum(
                  records, [
                  'quantity', 'weight'
                ], { label: 'Total' });
                let totalVolume = 0;
                records.forEach(sel => {
                  if (sel['volumeAsText'] && TMSUtils.isDecimal(sel['volumeAsText'])) totalVolume += Number(sel['volumeAsText']);
                });
                total['volumeAsText'] = totalVolume;
                footerRecords.push(total);
                return footerRecords;
              },
            }
          },
        },
      },
    };
    return config;
  }

  onPrintPOD = (tmsBillId: number) => {
    let { pageContext } = this.props;
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <UITMSReceiptOfDeliveryPrint key={`print-${util.IDTracker.next()}`}
          appContext={appCtx} pageContext={pageCtx} billIds={[tmsBillId]} />
      )
    }
    pageContext.createPopupPage('receipt-of-delivery', T('Receipt Of Delivery'), createAppPage, { size: 'xl' });
  }
}