import React from "react";
import * as FeatherIcon from 'react-feather';
import { bs, input, util, entity, grid, app } from '@datatp-ui/lib';

import { T } from "../../backend";
import { UIVehicleTripGoodsTrackingList } from "../tracking/UIVehicleTripGoodsTrackingList";
import { UIDriverSalaryReportList } from "./UIDriverSalaryReportList";
import { UIFuelPriceList, UIFuelPriceListPlugin } from "../UIFuelPriceList";
import { FuelProduct } from "../../models";
import { UIVehicleProfitReportList } from "./UIVehicleProfitReportList";
import { UIVehicleTripGoodsTrackingProfitEstimateList } from "./UIVehicleTripProfitEstimateList";
import { BBRefVehicleFleet } from "../BBRefVehicleFleet";

function initDriverReportDefault() {
  let fromDate = new Date();
  let toDate = new Date();
  fromDate.setDate(26);
  toDate.setDate(25);
  let dateRange = new util.TimeRange(fromDate, toDate);
  let n = -2;
  dateRange.fromAdd(n, 'months');
  dateRange.toAdd(n + 1, 'months');
  let bean: any = {
    fleetId: 6,
    fleetLabel: "BEEHPH",
    minSalary: 5200000,
    reportFrom: dateRange.fromFormat(),
    reportTo: dateRange.toFormat(),
  }
  return bean;
}

function initProfitReportDefault() {
  let fromDate = new Date();
  let toDate = new Date();
  let dateRange = new util.TimeRange(fromDate, toDate);
  let n = -1;
  dateRange.fromAdd(n, 'months');
  dateRange.toAdd(n, 'months');
  dateRange.fromStartOf('M');
  dateRange.toEndOf('M');
  let bean: any = {
    fleetId: 6,
    fleetLabel: "BEEHPH",
    minSalary: 5200000,
    reportFrom: dateRange.fromFormat(),
    reportTo: dateRange.toFormat(),
  }
  return bean;
}

export function createVehicleTripGoodsTrackingReport(context: grid.VGridContext) {
  let uiRoot = context.uiRoot as UIVehicleTripGoodsTrackingList;
  let { pageContext } = uiRoot.props;
  let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
    return (
      <UIDriverReport appContext={appCtx} pageContext={pageCtx}
        observer={new entity.BeanObserver(initDriverReportDefault())}
        onFinish={() => {
          pageCtx.back();
          uiRoot.reloadData();
        }} />
    )
  }
  pageContext.createPopupPage("vehicle-trip-report", T("Vehicle Trip Goods Trackings Report"), createAppPage, { size: 'xl', backdrop: 'static' });
}

interface UIDriverReportProps extends entity.AppDbEntityEditorProps {
  onFinish?: () => void;
}
export class UIDriverReport extends entity.AppDbEntityEditor<UIDriverReportProps> {
  trackings: Array<any> = [];
  driverSalaryReports: Array<any> = [];
  steps: Array<any> = [];
  currentStepPosition: number = 0;
  toolbars: Array<any> = [];

  constructor(props: UIDriverReportProps) {
    super(props);
    this.onInit();
  }

  onInit = () => {
    this.trackings = [];
    this.currentStepPosition = 0;
    this.steps = [
      {
        name: 'step1',
        label: 'Load Files',
        done: false,
      },
      {
        name: 'step2',
        label: 'Confirm File 2 Ways & Estimate Salary',
        done: false,
      },
      {
        name: 'step3',
        label: 'Create Report',
        done: false,
      },
      {
        name: 'step4',
        label: 'Finish & Close',
        done: false,
      },
    ];
    this.toolbars = [
      {
        name: 'multi-file-on-trip',
        createComponent(_ctx: grid.VGridContext) {
          let model = _ctx.model;
          let uiRoot = _ctx.uiRoot as UIVehicleTripGoodsTrackingProfitEstimateList;
          return (
            <input.BBCheckboxField
              field={'fileCombined'} label={T('File Combined')} bean={uiRoot.bean} value={false}
              onInputChange={(_bean: any, _field: string, _oldVal: any, newVal: any) => {
                if (newVal) {
                  model.getRecordFilter().addFieldValueFilter('combineFile', 'combineFileAsText', 'true');
                  model.filter();
                  _ctx.getVGrid().forceUpdateView(true);
                } else {
                  model.getRecordFilter().removeFilter('combineFile');
                  model.filter();
                  // _ctx.getVGrid().forceUpdateView(true);
                  uiRoot.forceUpdate();
                }
              }} />
          )
        },
      },
      {
        name: 'not-verify-way',
        createComponent(_ctx: grid.VGridContext) {
          let model = _ctx.model;
          let uiRoot = _ctx.uiRoot as UIVehicleTripGoodsTrackingProfitEstimateList;
          return (
            <input.BBCheckboxField
              field={'notVerifyWay'} label={T('Not Verify Ways')} bean={uiRoot.bean} value={false}
              onInputChange={(_bean: any, _field: string, _oldVal: any, newVal: any) => {
                if (newVal) {
                  model.getRecordFilter().addFieldValueFilter('twoWay', 'twoWayAsText', 'false');
                  model.filter();
                  _ctx.getVGrid().forceUpdateView(true);
                } else {
                  model.getRecordFilter().removeFilter('twoWay');
                  model.filter();
                  _ctx.getVGrid().forceUpdateView(true);
                }
              }} />
          )
        },
      },
      {
        name: 'mix-mode',
        createComponent(_ctx: grid.VGridContext) {
          let model = _ctx.model;
          let uiRoot = _ctx.uiRoot as UIVehicleTripGoodsTrackingProfitEstimateList;
          return (
            <input.BBCheckboxField
              field={'mixModeTrip'} label={T('Mix Mode')} bean={uiRoot.bean} value={false}
              onInputChange={(_bean: any, _field: string, _oldVal: any, newVal: any) => {
                if (newVal) {
                  model.getRecordFilter().addFieldValueFilter('mixModeTrip', 'mixModeTrip', 'true');
                  model.filter();
                  _ctx.getVGrid().forceUpdateView(true);
                } else {
                  model.getRecordFilter().removeFilter('mixModeTrip');
                  model.filter();
                  _ctx.getVGrid().forceUpdateView(true);
                }
              }} />
          )
        },
      },
    ]
  }

  onRefresh = () => {
    this.onInit();
    this.forceUpdate();
  }

  onProcessStep1 = (step: any) => {
    let { appContext, observer } = this.props;
    let bean = observer.getMutableBean();
    let dateRangeParams = new util.TimeRange(util.TimeUtil.parseCompactDateTimeFormat(bean['reportFrom']), util.TimeUtil.parseCompactDateTimeFormat(bean['reportTo']));
    dateRangeParams.fromStartOf('day');
    dateRangeParams.toEndOf('day');
    bean['reportFrom'] = dateRangeParams.fromFormat();
    bean['reportTo'] = dateRangeParams.toFormat();

    this.markLoading(true);
    this.forceUpdate();
    const successCB = (data: any) => {
      this.trackings = data;
      step.done = true;
      this.currentStepPosition++;
      this.markLoading(false);
      this.forceUpdate();
    }
    appContext
      .createHttpBackendCall('VehicleTripProfitService', 'routeCalculateAndGetVehicleTripGoodsTrackings', { params: bean })
      .withSuccessData(successCB)
      .call();
  }

  onProcessStep2 = (step: any, plugin: entity.DbEntityListPlugin) => {
    let { appContext } = this.props;

    this.markLoading(true);
    this.forceUpdate();
    const successCB = (data: any) => {
      this.trackings = data;
      step.done = true;
      this.currentStepPosition++;
      this.markLoading(false);
      this.forceUpdate();
    }
    appContext
      .createHttpBackendCall('VehicleTripProfitService', 'calculateDriverSalary', { params: plugin.getRecords() })
      .withSuccessData(successCB)
      .call();
  }

  onProcessStep3 = (step: any, plugin: entity.DbEntityListPlugin) => {
    let { appContext, observer } = this.props;
    let tripIds: Set<number> = new Set();
    for (let rec of plugin.getRecords()) {
      tripIds.add(rec['vehicleTripId']);
    }
    let params = {
      ...observer.getMutableBean(),
      'tripIds': [...tripIds],
    }

    this.markLoading(true);
    this.forceUpdate();
    const successCB = (data: any) => {
      this.driverSalaryReports = data;
      step.done = true;
      this.currentStepPosition++;
      this.markLoading(false);
      this.forceUpdate();
    }
    appContext
      .createHttpBackendCall('VehicleService', 'createDriverReports', { 'params': params })
      .withSuccessData(successCB)
      .call();
  }

  onProcessStep4 = (step: any) => {
    let { onFinish } = this.props;
    if (onFinish) {
      onFinish();
    } else {
      step.done = true;
      this.forceUpdate();
    }
  }

  onProcess = (step: any, plugin: entity.DbEntityListPlugin) => {
    switch (step.name) {
      case 'step1':
        this.onProcessStep1(step);
        return;
      case 'step2':
        this.onProcessStep2(step, plugin);
        return;
      case 'step3':
        this.onProcessStep3(step, plugin);
        return;
      case 'step4':
        this.onProcessStep4(step);
        return;
      default:
        return;
    }
  }

  onRenderStep = () => {
    let stepContents: Array<any> = [];
    let nextStep = this.steps[this.currentStepPosition + 1];
    for (let step of this.steps) {
      let cssClass = step.done ? 'text-success' : 'text-secondary';
      let currentStep = this.steps[this.currentStepPosition];
      let icon: any = null;
      if (currentStep.name === step.name) {
        cssClass = 'text-primary';
        icon = <FeatherIcon.CornerDownRight size={12} />;
      }
      if (step.done) {
        cssClass = 'text-success';
        icon = <FeatherIcon.CheckCircle size={12} />;
      }
      if (this.isLoading() && nextStep.name === step.name) {
        icon = <FeatherIcon.Loader size={12} style={{ animation: '0.75s linear infinite spinner-border' }} />;
      }

      stepContents.push(
        <div key={step.name} className={cssClass}>
          {icon}
          <span className="mx-1">
            {step.label}
          </span>
        </div>
      )
    }
    return (
      <div className="flex-vbox my-2">
        {stepContents}
      </div>
    );
  }

  render(): React.ReactNode {
    let { appContext, pageContext, observer } = this.props;
    let bean = observer.getMutableBean();
    let step = this.steps[this.currentStepPosition];
    let aggregationField = 'dateTime';
    if (step.name == 'step3') aggregationField = 'vehicleLabel';
    let plugin = new entity.DbEntityListPlugin(this.trackings);
    return (
      <bs.VSplit>
        <bs.VSplitPane width={400} className="flex-vbox">
          <div className="flex-grow-1">
            <input.BBDateInputMask
              bean={bean} field={'reportFrom'} format='DD/MM/YYYY' label='From' onInputChange={() => this.forceUpdate()} />
            <input.BBDateInputMask
              bean={bean} field={'reportTo'} format='DD/MM/YYYY' label='To' onInputChange={() => this.forceUpdate()} />
            <BBRefVehicleFleet minWidth={400} label='Fleet'
              appContext={appContext} pageContext={pageContext}
              bean={bean} beanIdField={'fleetId'} beanLabelField={'fleetLabel'} placeholder={'Fleet'}
            />
            <hr />
            {this.onRenderStep()}
          </div>
          <bs.Toolbar className='border' >
            <entity.WButtonEntityWrite
              appContext={appContext} pageContext={pageContext} icon={FeatherIcon.ChevronRight}
              label={T(step.label)} onClick={() => this.onProcess(step, plugin)} />
            <entity.WButtonEntityWrite
              appContext={appContext} pageContext={pageContext} icon={FeatherIcon.RefreshCw}
              label={T('Refresh')} onClick={this.onRefresh} />
          </bs.Toolbar>
        </bs.VSplitPane>
        <bs.VSplitPane>
          {step.name === 'step4' ?
            <UIDriverSalaryReportList collapseGroupExplorer
              appContext={appContext} pageContext={pageContext} readOnly
              plugin={new entity.DbEntityListPlugin(this.driverSalaryReports)} />
            :
            <UIVehicleTripGoodsTrackingProfitEstimateList key={`${util.IDTracker.next()}`} type="page" toolbarFilterActions={this.toolbars}
              aggregationField={aggregationField} viewName="aggregation" screenReportView="driverReportView"
              appContext={appContext} pageContext={pageContext} plugin={plugin} readOnly />
          }
        </bs.VSplitPane>
      </bs.VSplit>
    );
  }
}


export function createVehicleTripGoodsTrackingProfitReport(context: grid.VGridContext) {
  let uiRoot = context.uiRoot as UIVehicleTripGoodsTrackingList;
  let { pageContext } = uiRoot.props;
  let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
    return (
      <UIVehicleTripProfitReport appContext={appCtx} pageContext={pageCtx}
        observer={new entity.BeanObserver(initProfitReportDefault())}
        onFinish={() => {
          pageCtx.back();
          uiRoot.reloadData();
        }} />
    )
  }
  pageContext.createPopupPage("vehicle-trip-report", T("Vehicle Trip Goods Trackings Report"), createAppPage, { size: 'xl', backdrop: 'static' });
}

interface UIVehicleTripProfitReportProps extends entity.AppDbEntityEditorProps {
  onFinish?: () => void;
}
export class UIVehicleTripProfitReport extends entity.AppDbEntityEditor<UIVehicleTripProfitReportProps> {
  trackings: Array<any> = [];
  profitReports: Array<any> = [];
  steps: Array<any> = [];
  currentStepPosition: number = 0;

  constructor(props: UIVehicleTripProfitReportProps) {
    super(props);
    this.onInit();
  }

  onInit = () => {
    this.trackings = [];
    this.currentStepPosition = 0;
    this.steps = [
      {
        name: 'step1',
        label: 'Verify Fuel Price',
        done: false,
      },
      {
        name: 'step2',
        label: 'Estimate Fuel Cost',
        done: false,
      },
      {
        name: 'step3',
        label: 'Input Expense & Update Profit',
        done: false,
      },
      {
        name: 'step4',
        label: 'Create Report Profit',
        done: false,
      },
      {
        name: 'step5',
        label: 'Finish & Close',
        done: false,
      },
    ];
  }

  onRefresh = () => {
    this.onInit();
    this.forceUpdate();
  }

  onProcessStep1 = (step: any) => {
    let { appContext, pageContext, observer } = this.props;
    let bean = observer.getMutableBean();
    let dateRangeParams = new util.TimeRange(util.TimeUtil.parseCompactDateTimeFormat(bean['reportFrom']), util.TimeUtil.parseCompactDateTimeFormat(bean['reportTo']));
    dateRangeParams.fromStartOf('day');
    dateRangeParams.toEndOf('day');
    let plugin = new UIFuelPriceListPlugin(dateRangeParams.fromFormat(), dateRangeParams.toFormat());

    let newFuels: Array<any> = [];
    this.markLoading(true);
    this.forceUpdate();
    const successCB = (records: Array<any>) => {
      let dateRange = new util.TimeRange(util.TimeUtil.parseCompactDateTimeFormat(bean['reportFrom']), util.TimeUtil.parseCompactDateTimeFormat(bean['reportTo']));
      dateRange.fromStartOf('day');
      dateRange.toStartOf('day');
      while (dateRange.diff('day') >= 0) {
        let from = util.TimeUtil.toCompactDateFormat(dateRange.fromAsDate());
        let find = records.find(sel => sel['formattedDateTime'] === from);
        if (!find) {
          let fuelPrice = {
            'date': util.TimeUtil.javaCompactDateTimeFormat(dateRange.fromAsDate()),
            'currency': 'VND',
            'taxRate': 0.1,
            'product': FuelProduct.DO,
          }
          newFuels.push(fuelPrice);
        }
        dateRange.fromAdd(1, 'd');
      }

      if (newFuels.length > 0) {
        let createContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return (
            <UIFuelPriceList appContext={appCtx} pageContext={pageCtx} plugin={new entity.DbEntityListPlugin(newFuels)} />
          )
        }
        pageContext.createPopupPage('create-fuel-price', T('Create Fuel Price'), createContent, { size: 'xl' });
        this.markLoading(false);
        this.forceUpdate();
      } else {
        step.done = true;
        this.currentStepPosition++;
        this.markLoading(false);
        this.forceUpdate();
      }
    }

    appContext
      .createHttpBackendCall('FuelPriceService', 'searchFuelPrices', { params: plugin.searchParams })
      .withSuccessData(successCB)
      .call();
  }

  onProcessStep2 = (step: any) => {
    let { appContext, observer } = this.props;

    this.markLoading(true);
    this.forceUpdate();
    const successCB = (data: any) => {
      this.trackings = data;
      step.done = true;
      this.currentStepPosition++;
      this.markLoading(false);
      this.forceUpdate();
    }
    appContext
      .createHttpBackendCall('VehicleTripProfitService', 'calculateFuelVehicleTripGoodsTracking', { params: observer.getMutableBean() })
      .withSuccessData(successCB)
      .call();
  }

  onProcessStep3 = (step: any, plugin: entity.DbEntityListPlugin) => {
    let { appContext } = this.props;
    let modifiedRecords = plugin.getListModel().getModifiedRecords();
    modifiedRecords.forEach(sel => sel['editState'] = 'MODIFIED');

    this.markLoading(true);
    this.forceUpdate();
    const successCB = (data: any) => {
      this.trackings = data;
      step.done = true;
      this.currentStepPosition++;
      this.markLoading(false);
      this.forceUpdate();
    }
    appContext
      .createHttpBackendCall('VehicleTripProfitService', 'updateExpenseVehicleTripGoodsTracking', { 'params': plugin.getRecords() })
      .withSuccessData(successCB)
      .call();
  }

  onProcessStep4 = (step: any, plugin: entity.DbEntityListPlugin) => {
    let { appContext, observer } = this.props;
    let tripIds: Set<number> = new Set();
    for (let rec of plugin.getRecords()) {
      tripIds.add(rec['vehicleTripId']);
    }
    let params = {
      ...observer.getMutableBean(),
      'tripIds': [...tripIds],
    }
    this.markLoading(true);
    this.forceUpdate();
    const successCB = (data: any) => {
      this.profitReports = data;
      step.done = true;
      this.currentStepPosition++;
      this.markLoading(false);
      this.forceUpdate();
    }
    appContext
      .createHttpBackendCall('VehicleService', 'createVehicleProfitReports', { 'params': params })
      .withSuccessData(successCB)
      .call();
  }

  onProcessStep5 = (step: any) => {
    let { onFinish } = this.props;
    if (onFinish) {
      onFinish();
    } else {
      step.done = true;
      this.forceUpdate();
    }
  }

  onProcess = (step: any, plugin: entity.DbEntityListPlugin) => {
    switch (step.name) {
      case 'step1':
        this.onProcessStep1(step);
        return;
      case 'step2':
        this.onProcessStep2(step);
        return;
      case 'step3':
        this.onProcessStep3(step, plugin);
        return;
      case 'step4':
        this.onProcessStep4(step, plugin);
        return;
      case 'step5':
        this.onProcessStep5(step);
        return;
      default:
        return;
    }
  }

  onRenderStep = () => {
    let stepContents: Array<any> = [];
    for (let step of this.steps) {
      let cssClass = step.done ? 'text-success' : 'text-secondary';
      let currentStep = this.steps[this.currentStepPosition];
      let nextStep = this.steps[this.currentStepPosition + 1];
      let icon: any = null;
      if (currentStep.name === step.name) {
        cssClass = 'text-primary';
        icon = <FeatherIcon.CornerDownRight size={12} />;
      }
      if (step.done) {
        cssClass = 'text-success';
        icon = <FeatherIcon.CheckCircle size={12} />;
      }
      if (this.isLoading() && nextStep.name === step.name) {
        icon = <FeatherIcon.Loader size={12} style={{ animation: '0.75s linear infinite spinner-border' }} />;
      }

      stepContents.push(
        <div key={step.name} className={cssClass}>
          {icon}
          <span className="mx-1">
            {step.label}
          </span>
        </div>
      )
    }
    return (
      <div className="flex-vbox my-2">
        {stepContents}
      </div>
    );
  }

  render(): React.ReactNode {
    let { appContext, pageContext, observer } = this.props;
    let bean = observer.getMutableBean();
    let step = this.steps[this.currentStepPosition];
    let aggregationField = 'dateTime';
    if (step.name == 'step3') aggregationField = 'vehicleLabel';
    let plugin = new entity.DbEntityListPlugin(this.trackings);
    return (
      <bs.VSplit>
        <bs.VSplitPane width={400} className="flex-vbox">
          <div className="flex-grow-1">
            <input.BBDateInputMask
              bean={bean} field={'reportFrom'} format='DD/MM/YYYY' label='From' onInputChange={() => this.forceUpdate()} />
            <input.BBDateInputMask
              bean={bean} field={'reportTo'} format='DD/MM/YYYY' label='To' onInputChange={() => this.forceUpdate()} />
            <BBRefVehicleFleet minWidth={400} label='Fleet'
              appContext={appContext} pageContext={pageContext}
              bean={bean} beanIdField={'fleetId'} beanLabelField={'fleetLabel'} placeholder={'Fleet'}
            />
            <hr />
            {this.onRenderStep()}
          </div>
          <bs.Toolbar className='border' >
            <entity.WButtonEntityWrite
              appContext={appContext} pageContext={pageContext} icon={FeatherIcon.ChevronRight}
              label={T(step.label)} onClick={() => this.onProcess(step, plugin)} />
            <entity.WButtonEntityWrite
              appContext={appContext} pageContext={pageContext} icon={FeatherIcon.RefreshCw}
              label={T('Refresh')} onClick={this.onRefresh} />
          </bs.Toolbar>
        </bs.VSplitPane>
        <bs.VSplitPane>
          {step.name === 'step5' ?
            <UIVehicleProfitReportList collapseGroupExplorer
              appContext={appContext} pageContext={pageContext} readOnly
              plugin={new entity.DbEntityListPlugin(this.profitReports)} />
            :
            // <UIVehicleTripGoodsTrackingList key={`${util.IDTracker.next()}`} screen="profitReportView" type="page"
            //   appContext={appContext} pageContext={pageContext} plugin={plugin} aggregationField={aggregationField} readOnly />
            <UIVehicleTripGoodsTrackingProfitEstimateList key={`${util.IDTracker.next()}`} type="page"
              aggregationField={aggregationField} viewName="aggregation" screenReportView="profitReportView"
              appContext={appContext} pageContext={pageContext} plugin={plugin} readOnly />
          }
        </bs.VSplitPane>
      </bs.VSplit>
    );
  }
}


