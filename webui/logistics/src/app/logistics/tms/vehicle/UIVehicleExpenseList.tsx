import React from 'react';
import * as FeatherIcon from 'react-feather'
import { grid, sql, bs, util, input, entity } from '@datatp-ui/lib';
import { module } from '@datatp-ui/erp';

import { T } from '../backend';

import { VehicleExpenseStatus, VehicleExpenseType } from '../models';
import { BBRefVehicle } from './BBRefVehicle';

export class UIVehicleExpenseListPlugin extends entity.DbEntityListPlugin {
  vehicleId: number;
  licensePlate: string;
  constructor() {
    super([]);

    this.backend = {
      context: 'company',
      service: 'VehicleService',
      searchMethod: 'searchVehicleExpenses'
    }

    this.searchParams = {
      filters: [...sql.createSearchFilter()],
      optionFilters: [sql.createStorageStateFilter([entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED])],
      maxReturn: 1000,
    };
  }

  loadData(uiList: entity.DbEntityList<any>): void {
    this.createBackendSearch(uiList, { params: this.getSearchParams() }).call();
  }

  withVehicle(vehicleId: number, licensePlate: string) {
    this.addSearchParam('vehicleId', vehicleId);
    this.vehicleId = vehicleId;
    this.licensePlate = licensePlate;
    return this;
  }

  hasVehicle() {
    return this.vehicleId !== undefined ? true : false
  }

}

export class UIVehicleExpenseList extends entity.DbEntityList {
  createVGridConfig() {
    let { plugin, appContext, pageContext } = this.props;
    let pluginVehileExpense = plugin as UIVehicleExpenseListPlugin;
    const writeCap = pageContext.hasUserWriteCapability();
    let modCap = pageContext.hasUserModeratorCapability();
    let onInputChange = (fieldCtx: grid.FieldContext, oldVal: any, newVal: any) => {
      let ctx = fieldCtx.gridContext;
      let dRecord = fieldCtx.displayRecord;
      let field = fieldCtx.fieldConfig;
      let record = dRecord.record;
      let event: grid.VGridCellEvent = {
        row: dRecord.row,
        field: field,
        event: 'Modified'
      }
      if (field.name === 'price' || field.name === 'quantity') {
        record.total = record.price * record.quantity;
      }
      ctx.broadcastCellEvent(event);
    }
    const buttonStyle = "mb-1 w-150 text-start";
    let config: grid.VGridConfig = {
      title: 'Vehicles',
      record: {
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          {
            name: 'label', label: T('Label'), width: 150, state: { showRecordState: true },
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row == cell.getRow()) {
                  cell.forceUpdate()
                }
              },
            },
            editor: { type: 'string', onInputChange: onInputChange }
          },
          { name: 'code', label: T('Code'), state: { visible: false }, width: 150 },
          {
            name: 'licensePlate', label: T('License Plate'), state: { visible: true }, width: 150,
            editor: {
              type: 'string', enable: true,
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let tracking = dRecord.record;
                return (
                  <BBRefVehicle appContext={appContext} pageContext={pageContext}
                    placeholder='Vehicle' tabIndex={tabIndex} autofocus={focus}
                    placement="bottom-start" minWidth={500} disable={!writeCap}
                    bean={tracking} beanIdField={'vehicleId'} beanLabelField={'licensePlate'}
                    onPostUpdate={(inputUI: React.Component, bean: any, selectOpt: any, userInput: string) => {
                      onInputChange(bean, field.name, '', bean[field.name])
                    }}
                  />
                )
              }
            }
          },
          {
            name: 'requestDate', label: T('Date'), state: { visible: true }, width: 150, format: util.text.formater.compactDate,
            editor: {
              type: 'date',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let vehicleExpense = dRecord.record;
                let cssClass = field.cssClass;
                return (
                  <input.BBDateInputMask className={cssClass}
                    bean={vehicleExpense} field={field.name} tabIndex={tabIndex} focus={focus} format='DD/MM/YYYY'
                    onInputChange={onInputChange} />
                );
              },
            }
          },
          { name: 'requesterFullName', label: T('Full Name'), state: { visible: true }, width: 150 },
          {
            name: 'confirmedDate', label: T('Date'), state: { visible: true }, width: 150, format: util.text.formater.compactDate,
            editor: {
              type: 'date',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let vehicleExpense = dRecord.record;
                let cssClass = field.cssClass;
                return (
                  <input.BBDateInputMask className={cssClass}
                    bean={vehicleExpense} field={field.name} tabIndex={tabIndex} focus={focus} format='DD/MM/YYYY'
                    onInputChange={onInputChange} />
                );
              },
            }
          },
          { name: 'confirmerFullName', label: T('Full Name'), state: { visible: true }, width: 150 },
          { name: 'quantity', label: T('Quantity'), state: { visible: false }, width: 150, editor: { type: 'string', onInputChange: onInputChange } },
          {
            name: 'quantityUnit', label: T('Q.Unit'), state: { visible: false }, hint: "Đơn Vị", width: 80,
            editor: {
              type: 'string',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let vehicleExpense = dRecord.record;
                const oldVal = vehicleExpense[field.name];
                return (
                  <module.settings.BBRefUnit
                    appContext={appContext} pageContext={pageContext}
                    minWidth={400}
                    placement="left"
                    placeholder="Enter Unit"
                    groupNames={['packing']}
                    bean={vehicleExpense} beanIdField={field.name}
                    tabIndex={tabIndex} autofocus={focus}
                    onPostUpdate={(inputUI, bean, selectOpt, userInput) => onInputChange(bean, field.name, oldVal, bean[field.name])} />
                )
              },
            }
          },
          {
            name: 'taxRate', label: T('Tax (%)'), hint: T('Tax Rate (%)'), state: { visible: false }, format: util.text.formater.percent,
            editor: {
              type: 'percent',
              onInputChange: onInputChange
            },
          },
          {
            name: 'price', label: T('Price'), state: { visible: false }, width: 150, format: util.text.formater.currency,
            editor: {
              type: 'currency',
              onInputChange: onInputChange
            }
          },
          {
            name: 'tax', label: T('Tax'), state: { visible: false }, width: 150, format: util.text.formater.currency,
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row == cell.getRow() && (event.field.name === 'taxRate' || event.field.name === 'price' || event.field.name === 'quantity')) {
                  cell.forceUpdate()
                }
              },
            },
          },
          {
            name: 'total', label: T('Total'), width: 150, format: util.text.formater.currency,
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row == cell.getRow() && (event.field.name === 'taxRate' || event.field.name === 'price' || event.field.name === 'quantity')) {
                  cell.forceUpdate()
                }
              },
            },
            editor: {
              onInputChange: onInputChange,
              enable: true,
              type: 'currency'
            }
          },
          {
            name: 'description', label: T('Description'), state: { visible: true }, width: 150,
            editor: { type: 'string', onInputChange: onInputChange }
          },
          {
            name: 'type', label: T('Type'), state: { visible: true }, width: 150,
            editor: {
              type: 'string',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let vehicleExpense = dRecord.record;
                return (
                  <input.BBSelectField
                    tabIndex={tabIndex} focus={focus}
                    bean={vehicleExpense} field={field.name}
                    options={[VehicleExpenseType.OIL, VehicleExpenseType.REPAIR, VehicleExpenseType.MAINTENANCE, VehicleExpenseType.VETC, VehicleExpenseType.OTHER]}
                    onInputChange={onInputChange} />
                )
              },
            }
          },
          {
            name: 'status', label: T('Status'), state: { visible: true }, width: 150, container: 'fixed-right',
            customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let vehicleExpense = dRecord.record;
              let status = vehicleExpense[field.name]
              let isNew = vehicleExpense.id === undefined ? true : false;
              if (status === VehicleExpenseStatus.NEED_CONFIRM) {
                return (
                  <div className={'flex-hbox flex-grow-0'} >
                    <bs.Button disabled={!writeCap || isNew} laf='success' className='my-5 p-1' style={{ width: 63, fontSize: 12 }} outline
                      onClick={() => this.updateStatus(vehicleExpense, VehicleExpenseStatus.CONFIRMED)} >
                      {T('Confirm')} <FeatherIcon.Check size={10} />
                    </bs.Button>
                    <bs.Button disabled={!writeCap || isNew} laf='danger' className='mx-1 my-5 p-1' style={{ width: 63, fontSize: 12 }} outline
                      onClick={() => this.updateStatus(vehicleExpense, VehicleExpenseStatus.REJECT)} >
                      {T('Reject')} <FeatherIcon.X size={10} />
                    </bs.Button>
                  </div>
                )
              }
              else {
                return (<bs.Badge className='w-100 m-0' style={{ padding: 1 }} laf={this.getStatusColor(vehicleExpense)}>
                  <bs.BadgeLabel>
                    {modCap ? <input.BBSelectField disable={!writeCap} className='p-0' style={{ height: 16, fontSize: 12 }} bean={vehicleExpense} field={field.name}
                      options={[VehicleExpenseStatus.CONFIRMED, VehicleExpenseStatus.REJECT]}
                      onInputChange={
                        (_bean: any, _field: any, newVal: any, oldVal: any) => {
                          this.updateStatus(vehicleExpense, _bean[_field]);
                        }
                      } /> : status}
                  </bs.BadgeLabel>
                </bs.Badge>)
              }
            }
          },
          ...entity.DbEntityListConfigTool.FIELD_ENTITY,
        ],
        fieldGroups: {
          'Request': {
            label: T('Request'),
            visible: false,
            fields: [
              'requestDate',
              'requesterFullName'
            ]
          },
          'Confirmed': {
            label: T('Confirmed'),
            visible: false,
            fields: [
              'confirmedDate', 'confirmerFullName',
            ]
          }
        },
        editor: {
          enable: true,
          supportViewMode: ['table', 'aggregation']
        }
      },
      toolbar: {
        actions: [
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION(!writeCap, {
            name: 'add', label: T('Add'), icon: FeatherIcon.List,
            createComponent: function (ctx: grid.VGridContext) {
              let uiRoot = ctx.uiRoot as UIVehicleExpenseList;
              let types = [VehicleExpenseType.OIL, VehicleExpenseType.REPAIR, VehicleExpenseType.MAINTENANCE, VehicleExpenseType.VETC, VehicleExpenseType.OTHER];
              let options = []
              for (let type of types) {
                options.push(
                  <bs.Button laf='secondary' outline className={buttonStyle} onClick={() => uiRoot.onAdd(type)} >
                    <FeatherIcon.ArrowRightCircle size={12} /> {T(type)}
                  </bs.Button>
                )
              }
              return (<bs.Popover className="flex-hbox-grow-0" title={'Vehicle Expense Type'} closeOnTrigger=".btn" >
                <bs.PopoverToggle laf='primary' >
                  <FeatherIcon.Plus size={12} /> {'Add'}
                </bs.PopoverToggle>
                <bs.PopoverContent >
                  <div className='flex-vbox'>
                    {options}
                  </div>
                </bs.PopoverContent>
              </bs.Popover>)
            }
          }),
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_DELETE(!writeCap, T('Del')),
        ],
        filters: entity.DbEntityListConfigTool.TOOLBAR_FILTERS(true),
      },

      footer: {
        page: {
          render: (ctx: grid.VGridContext) => {
            return <bs.Toolbar className='border' hide={!writeCap}>
              <entity.WButtonEntityWrite appContext={appContext} pageContext={pageContext} icon={FeatherIcon.Save}
                label={T('Save')} onClick={this.onSave} />
            </bs.Toolbar >
          }
        },
      },

      view: {
        currentViewName: 'aggregation',
        availables: {
          table: {
            viewMode: 'table',
          },
          aggregation: {
            viewMode: 'aggregation',
            treeWidth: 160,
            createAggregationModel(_ctx: grid.VGridContext) {
              let model = new grid.AggregationDisplayModel('All', false);
              model.addAggregation(new grid.DateValueAggregation(T("Request Month"), "requestDate", "MM/YYYY", true)
                .withSortBucket('desc'));
              if (!pluginVehileExpense.hasVehicle()) {
                model.addAggregation(new grid.ValueAggregation(T('License Plate'), 'licensePlate', true));
              }
              return model;
            },
          },
        },
      },
    };
    return config;
  }

  getStatusColor = (vehicleExpense: any) => {
    switch (vehicleExpense.status) {
      case VehicleExpenseStatus.NEED_CONFIRM: return 'warning';
      case VehicleExpenseStatus.CONFIRMED: return 'success';
      case VehicleExpenseStatus.REJECT: return 'danger';
      default:
        return 'secondary';
    }
  }

  updateStatus(vehicleExpense: any, status: VehicleExpenseStatus) {
    let { appContext } = this.props;

    appContext.createHttpBackendCall('VehicleService', 'updateStatus', { vehicleExpense: vehicleExpense, status: status })
      .withSuccessData((data: any) => {
        appContext.addOSNotification("success", T('Success'));
        let vehicleExpenseRes = data
        if (vehicleExpenseRes) {
          vehicleExpense.status = vehicleExpenseRes.status;
          vehicleExpense.confirmedDate = vehicleExpenseRes.confirmedDate;
          vehicleExpense.confirmerAccountId = vehicleExpenseRes.confirmerAccountId;
          vehicleExpense.confirmerFullName = vehicleExpenseRes.confirmerFullName;
          this.getVGridContext().getVGrid().forceUpdateView();
        }
      })
      .call();
  }


  onAdd(type: VehicleExpenseType) {
    let { pageContext } = this.props;
    let modCap = pageContext.hasUserModeratorCapability();
    let { plugin } = this.props;
    let pluginVehileExpense = plugin as UIVehicleExpenseListPlugin;
    let newRecord
    if (pluginVehileExpense.hasVehicle()) {
      newRecord = { vehicleId: pluginVehileExpense.vehicleId, licensePlate: pluginVehileExpense.licensePlate, status: VehicleExpenseStatus.NEED_CONFIRM, type: type, quantity: 1 }
    } else {
      newRecord = { status: VehicleExpenseStatus.NEED_CONFIRM, type: type, quantity: 1 }
    }
    if (plugin)
      if (modCap) {
        newRecord = { ...newRecord, status: VehicleExpenseStatus.CONFIRMED }
      }
    plugin.getListModel().addRecord(newRecord);
    let state = grid.getRecordState(newRecord);
    state.markModified();
    this.getVGridContext().getVGrid().forceUpdateView();
  }

  onDeleteAction() {
    let { plugin, appContext } = this.props;
    const ids = plugin.getListModel().getSelectedRecordIds();

    let callbackConfirm = () => {
      appContext.createHttpBackendCall('VehicleService', 'deleteVehicleExpenses', { ids: ids })
        .withSuccessData((_data: any) => {
          appContext.addOSNotification("success", T('Delete VehicleExpenses Success'));
          this.reloadData();
        })
        .call();
    }
    let message = (<div className="text-danger">Do you want to delete these Vehicle Expenses?</div>);
    bs.dialogConfirmMessage('Confirm Message', message, callbackConfirm);
  }

  onSave = () => {
    let { appContext, pageContext, plugin } = this.props;
    let modifiedRecords = plugin.getListModel().getMarkModifiedRecords();
    appContext.createHttpBackendCall('VehicleService', 'saveVehicleExpenses', { vehicleExpenses: modifiedRecords })
      .withSuccessData((_data: any) => {
        appContext.addOSNotification("success", T('Success'));
        this.reloadData();
      })
      .call();
  }
}