import React, { Component } from "react";
import * as FeatherIcon from 'react-feather'
import { server, bs, grid, app, util, entity, input } from "@datatp-ui/lib";

import { T } from "../backend";
import { XLSXCustomButton } from "../XLSXButton";
import { UITMSBillSummary } from "../bill/TMSBillSummary";
import { TMSBillTransportationModeTools } from "../utils";
import { UITMSBillUtils } from "../bill/UITMSBillUtils";
import { TMSBillBFSOneFeeTemplate, TMSBillBFSOneVehicleInfoTemplate } from "../bill/UITMSBillBfsOneTemplate";
import { BBRefVehicleFleet } from "../vehicle/BBRefVehicleFleet";
import { UITMSBillMessage } from "../bill/UITMSBillMessage";
import { UITMSHouseBillList, UITMSHouseBillListPlugin } from "./TMSHouseBillGeneralList";
import { TMSHouseBillGeneralEditor } from "./UITMSHouseBillEditor";

function onSaveRows(context: grid.VGridContext, callBack?: (response: any) => void) {
  let uiRoot = context.uiRoot as UITMSHouseBillList;
  let { appContext, plugin } = uiRoot.props;
  let modifiedRecords = context.model.getMarkModifiedRecords();
  let deleteRecords = context.model.getMarkDeletedRecords();
  let recordChanges = [...modifiedRecords, ...deleteRecords];

  if (recordChanges.length === 0) {
    let selectedBills = plugin.getListModel().getSelectedRecords();
    selectedBills.forEach(sel => {
      let state = grid.getRecordState(sel);
      sel['_state'] = new grid.RecordState(state.row);
    })
    appContext.addOSNotification("success", T("No Bills were Edited"));
    if (callBack) callBack(null);
    return;
  }

  let failCB = (response: server.BackendResponse) => {
    server.rest.handleResponseError('', response);
    if (callBack) callBack(response)
  }
  let successCB = (data: any) => {
    let results: Array<any> = data;
    for (let sel of deleteRecords) {
      plugin.getListModel().removeRecord(sel);
    }
    for (let modifiedRecord of modifiedRecords) {
      let result = results.find((result) => modifiedRecord['uikey'] == result['uikey']);
      if (result) modifiedRecord['id'] = result['id'];
    }

    updateTMSBillData(context, modifiedRecords, (_records) => {
      appContext.addOSNotification("success", T("Save Bills Success"));
      context.getVGrid().forceUpdateView();
    });
    if (callBack) callBack(data);
  }
  context.getVGrid().forceUpdateView();
  appContext
    .createHttpBackendCall('TMSRestCallService', 'saveTMSBillModels', { records: recordChanges })
    .withSuccessData(successCB)
    .withFail(failCB)
    .call();
}

function updateTMSBillData(context: grid.VGridContext, updateBills: Array<any>, callback?: (records: any) => void) {
  let uiRoot = context.uiRoot as entity.DbEntityList;
  let { appContext } = uiRoot.props;
  let ids: Array<any> = updateBills.map(bill => bill.id);
  if (ids.length == 0) {
    if (callback) callback(updateBills);
    return;
  }
  let successCB = (data: Array<any>) => {
    for (let bill of updateBills) {
      let find = data.find((db) => bill['id'] == db['id']);
      if (find) {
        for (const propertyName in find) bill[propertyName] = find[propertyName];
      }
      let state = grid.getRecordState(bill);
      bill['_state'] = new grid.RecordState(state.row);
    }
    if (callback) callback(updateBills);
  }

  let searchParams = {
    params: {
      ids: ids,
      dataScope: app.AppDataScope.COMPANY
    }
  }
  appContext
    .createHttpBackendCall('TMSHouseBillService', 'searchTMSBills', { params: searchParams })
    .withSuccessData(successCB)
    .call();
}

export class UITMSHouseBillGeneralListPageControl extends Component<grid.VGridContextProps> {
  saving = false;
  onSave = () => {
    let { context } = this.props;
    let uiRoot = context.uiRoot as entity.DbEntityList;
    let { onModifyBean } = uiRoot.props;
    this.saving = true;
    this.forceUpdate();
    onSaveRows(context, (data) => {
      if (onModifyBean) onModifyBean(data);
      this.saving = false;
      this.forceUpdate();
    });
  }

  onShowSummary = () => {
    let { context } = this.props;
    let uiRoot = context.uiRoot as app.AppComponent;
    let { pageContext } = uiRoot.props;
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <UITMSBillSummary appContext={appCtx} pageContext={pageCtx} context={context} />
      );
    }
    pageContext.createPopupPage('summary', T('TMS Bill Summary'), createAppPage, { size: 'xl' })
  }

  requestRoundUsed = () => {
    let { context } = this.props;
    let uiRoot = context.uiRoot as entity.DbEntityList;
    let { plugin, appContext } = uiRoot.props;
    let ids = plugin.getListModel().getSelectedRecordIds();
    if (!ids || ids.length === 0) {
      bs.notificationShow("danger", T("Bill is not selected"), T("You should select at least 1 Record!!!"));
      return;
    }
    let successCB = (_data: any) => {
      updateTMSBillData(context, plugin.getListModel().getSelectedRecords(), (_records) => {
        appContext.addOSNotification("success", T("Request Round Used Success"));
        context.getVGrid().forceUpdateView();
        this.forceUpdate();
      });
    }
    appContext
      .createHttpBackendCall('TMSRoundUsedService', 'requestRoundUsedBill', { ids: ids })
      .withSuccessData(successCB)
      .call();
  }

  requestOPS = () => {
    let { context } = this.props;
    let uiRoot = context.uiRoot as entity.DbEntityList;
    let { plugin, appContext } = uiRoot.props;
    let ids = plugin.getListModel().getSelectedRecordIds();
    if (!ids || ids.length === 0) {
      bs.notificationShow("danger", T("Bill is not selected"), T("You should select at least 1 Record!!!"));
      return;
    }
    let successCB = (_data: any) => {
      updateTMSBillData(context, plugin.getListModel().getSelectedRecords(), (_records) => {
        appContext.addOSNotification("success", T("Request OPS Success"));
        context.getVGrid().forceUpdateView();
        this.forceUpdate();
      });
    }
    appContext
      .createHttpBackendCall('TMSOperationsService', 'requestOPS', { ids: ids })
      .withSuccessData(successCB)
      .call();
  }

  requestVendorBill = () => {
    let { context } = this.props;
    let uiRoot = context.uiRoot as entity.DbEntityList;
    let { plugin, appContext } = uiRoot.props;
    let ids = plugin.getListModel().getSelectedRecordIds();
    if (!ids || ids.length === 0) {
      bs.notificationShow("danger", T("Bill is not selected"), T("You should select at least 1 Record!!!"));
      return;
    }
    let successCB = (_data: any) => {
      appContext.addOSNotification("success", T("Request Success"));
    }
    appContext
      .createHttpBackendCall('TMSVendorBillService', 'requestVendorBill', { billIds: ids })
      .withSuccessData(successCB)
      .call();
  }

  onReportVendor = () => {
    const { context } = this.props;
    const uiRoot = context.uiRoot as entity.DbEntityList;
    const { pageContext, plugin } = uiRoot.props;
    let selectedRecords = plugin.getListModel().getSelectedRecords();
    if (selectedRecords.length === 0) {
      const createContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
        let bean = {
          'notPaid': true,
          'from': util.TimeUtil.javaCompactDateTimeFormat(new Date()),
          'to': util.TimeUtil.javaCompactDateTimeFormat(new Date())
        }
        return (
          <TMSReportVendorBill appContext={appCtx} pageContext={pageCtx}
            observer={new entity.BeanObserver(bean)} />
        )
      }
      pageContext.createPopupPage('create-report-vendor-bill', T('Create Report Vendor Bill'), createContent);
    } else {
      const createContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
        return (
          <TMSBillAccountingList appContext={appCtx} pageContext={pageCtx} plugin={new entity.DbEntityListPlugin(selectedRecords)} />
        )
      }
      pageContext.createPopupPage('tms-report-bill', T('TMS Bill'), createContent, { size: 'xl' })
    }
  }

  processingGoodsRequest = () => {
    let { context } = this.props;
    let uiRoot = context.uiRoot as entity.DbEntityList;
    let { appContext, pageContext, plugin } = uiRoot.props;
    let pluginImpl = plugin as UITMSHouseBillListPlugin;
    let ids = pluginImpl.getListModel().getSelectedRecordIds();
    if (!ids || ids.length === 0) {
      bs.notificationShow("danger", T("Bill is not selected"), T("You should select at least 1 Record!!!"));
      return;
    }
    let newRecords = plugin.getListModel().getRecords().find(sel => grid.getRecordState(sel).isMarkNew());
    if (newRecords) {
      bs.notificationShow("danger", "You need to save changes");
      return;
    }

    const requestVendorBill = (ids: Array<number>) => {
      let onRequestSuccess = (_data: any) => {
        let records = plugin.getListModel().getSelectedRecords();
        updateTMSBillData(context, records, () => {
          context.getVGrid().forceUpdateView();
        });
      }
      appContext
        .createHttpBackendCall('TMSVendorBillService', 'requestVendorBill', { ids: ids })
        .withSuccessData(onRequestSuccess)
        .call();
    }

    const updateMessage = (message: any, allowCreateVendorBill: boolean) => {
      let selected = plugin.getListModel().getSelectedRecords();
      let billIds = plugin.getListModel().getSelectedRecordIds();
      let successCb = (_data: any) => {
        if (allowCreateVendorBill) {
          requestVendorBill(billIds);
        } else {
          selected.forEach(sel => {
            sel['messageId'] = message.id;
            sel['messageStatus'] = message.status;
            context.getVGrid().forceUpdateView();
          })
        }
      }
      const params = {
        messageId: message.id,
        billIds: billIds
      }
      appContext
        .createHttpBackendCall('TMSBillService', 'updateMessageId', params)
        .withSuccessData(successCb)
        .call();
    }

    const callback = (data: any) => {
      let message = data.message;
      let vendor = data.vendor;
      let ownerAccount = data.owner;
      let createPopup = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
        return (<UITMSBillMessage appContext={appCtx} pageContext={pageCtx} context={context}
          plugin={plugin} observer={new entity.ComplexBeanObserver(message)} vendor={vendor} ownerAccount={ownerAccount}
          onSendMessageSuccessCallBack={updateMessage} />)
      }
      pageContext.createPopupPage('processing-goods-request', T('Processing Goods Request'), createPopup, { size: 'xl' });
    }
    appContext.createHttpBackendCall('TMSPrintService', 'createCustomerServiceEmailv2', { ids: ids }).withSuccessData(callback).call();
  }

  requestContainerDeposit = () => {
    let { context } = this.props;
    let uiRoot = context.uiRoot as entity.DbEntityList;
    let { plugin, appContext } = uiRoot.props;
    let ids = plugin.getListModel().getSelectedRecordIds();
    let records = plugin.getListModel().getSelectedRecords();
    if (!ids || ids.length === 0) {
      bs.notificationShow("danger", T("Bill is not selected"), T("You should select at least 1 Record!!!"));
      return;
    }
    let labels = [];
    for (let record of records) {
      if (!TMSBillTransportationModeTools.isImport(record.mode)) {
        labels.push(record['label']);
      }
    }
    if (labels.length > 0) {
      let error: string = "";
      if (labels.length === 1) {
        error = `Bill ${labels.join(", ")} is not for imported goods.`
      } else {
        error = `Bill ${labels.join(", ")} are not for imported goods.`
      }
      bs.notificationShow("danger", T('Error'), T(error));
      return;
    }
    let successCB = (_data: any) => {
      appContext.addOSNotification("success", T("Request Success"));
    }
    appContext
      .createHttpBackendCall('TMSRoundUsedService', 'requestTMSBillsToContainerDeposits', { tmsBillIds: ids })
      .withSuccessData(successCB)
      .call();
  }
  billDeliveredFails: Array<any> = [];
  render() {
    let { context } = this.props;
    let uiRoot = context.uiRoot as UITMSHouseBillList;
    let { appContext, pageContext, hbl, readOnly, plugin } = uiRoot.props;
    let writeCap = pageContext.hasUserWriteCapability();
    let readCap = pageContext.hasUserReadCapability();
    return (
      <bs.Toolbar className='border' hide={!readCap || readOnly}>
        {hbl && hbl.id &&
          <bs.Button laf='primary' onClick={() => {
            const createAppPage = (_appCtx: app.AppContext, _pageCtx: app.PageContext) => {
              let cloneBill = plugin.getRecords().map(rec => UITMSBillUtils.cloneBill(rec));
              return (
                <TMSHouseBillGeneralEditor
                  appContext={_appCtx} pageContext={_pageCtx}
                  houseBillPlugin={new entity.DbEntityListPlugin(cloneBill)}
                  observer={new entity.ComplexBeanObserver(entity.EntityUtil.clone(hbl, true))} />
              )
            }
            pageContext.createPopupPage('tms-house-bill-editor', T('New House Bill'), createAppPage, { size: 'xl' });
          }}>
            <FeatherIcon.Copy size={14} /> {T('Clone')}
          </bs.Button>
        }
        <entity.WButtonEntityWrite icon={FeatherIcon.Send}
          appContext={appContext} pageContext={pageContext}
          disable={!writeCap} hide={!writeCap}
          label={T('Send Vendor')} onClick={this.processingGoodsRequest} />
        <bs.ButtonGroup label="Print & Email" className="flex-grow-0" smallScreenLaf="popover">
          <bs.Popover className="flex-hbox-grow-0" title={'Export Data'} closeOnTrigger=".btn" >
            <bs.PopoverToggle laf='primary' >
              <FeatherIcon.Layers size={12} /> {'Email & Print'}
            </bs.PopoverToggle>
            <bs.PopoverContent >
              <div className='flex-vbox'>
                {/* <entity.WButtonEntityWrite className="my-1" icon={FeatherIcon.Mail}
                  appContext={appContext} pageContext={pageContext}
                  disable={!writeCap} hide={!writeCap}
                  label={T('Send Customer(FCL)')} onClick={uiRoot.onShowUIMessageCustomerFCL} /> */}
                <entity.WButtonEntityWrite icon={FeatherIcon.Printer}
                  appContext={appContext} pageContext={pageContext}
                  disable={!pageContext.hasUserReadCapability()} hide={!pageContext.hasUserReadCapability()}
                  label={T('Print POD')} onClick={() => UITMSBillUtils.onPrintReceiptOfDelivery(context)} />
              </div>
            </bs.PopoverContent>
          </bs.Popover>

        </bs.ButtonGroup>
        <bs.ButtonGroup label="Tools" className="flex-grow-0" smallScreenLaf="popover" >
          <bs.Popover className="flex-hbox-grow-0" title={'Export Data'} closeOnTrigger=".btn" >
            <bs.PopoverToggle laf='primary' >
              <FeatherIcon.Layers size={12} /> {'Export'}
            </bs.PopoverToggle>
            <bs.PopoverContent >
              <div className='flex-vbox'>
                <XLSXCustomButton className="p-2 my-1" tableName="tms-bill" context={context}
                  options={{ fileName: `Tms Bill ${util.TimeUtil.toCompactDateFormat(new Date())}.xlsx`, modelName: 'tms-bill' }}
                  appContext={appContext} pageContext={pageContext} fieldSelect={['label', 'code']} />
                <bs.Button laf="primary" className="p-2 my-1" onClick={this.onReportVendor}>
                  <FeatherIcon.FileText size={12} /> {T('Report Bill')}
                </bs.Button>
              </div>
            </bs.PopoverContent>
          </bs.Popover>
        </bs.ButtonGroup>
        {writeCap ?
          <bs.Button laf='primary' disabled={this.saving || !writeCap || (hbl && !hbl.id)} onClick={this.onSave}>
            {this.saving ?
              <FeatherIcon.Loader size={12} style={{ animation: '0.75s linear infinite spinner-border' }} />
              :
              <FeatherIcon.Save size={12} />
            }
            <span style={{ marginLeft: 4 }}>{T('Save Rows')}</span>
          </bs.Button>
          :
          null
        }
      </bs.Toolbar>
    )
  }
}

class TMSReportVendorBill extends entity.DbEntityEditor {
  onLoadBill = () => {
    let { pageContext, observer } = this.props;
    let params = observer.getMutableBean();
    let vendorId = params['vendorId'];
    const createContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      let dateRange = new util.TimeRange(util.TimeUtil.parseCompactDateTimeFormat(params['from']), util.TimeUtil.parseCompactDateTimeFormat(params['to']));
      let plugin = new UITMSHouseBillListPlugin(dateRange); //.withTMSPartnerId('vendorId', vendorId);
      return (
        <TMSBillAccountingList appContext={appCtx} pageContext={pageCtx} plugin={plugin} />
      )
    }
    pageContext.createPopupPage('tms-report-bill', T('TMS Bill'), createContent, { size: 'xl' })
  }

  render(): React.ReactNode {
    const { appContext, pageContext, observer } = this.props;
    const params = observer.getMutableBean();
    return (
      <div className="flex-vbox">
        <div className="flex-grow-1">
          <input.BBCheckboxField bean={params} label="Not Paid" field="notPaid" value={false} />
          <bs.Row>
            <bs.Col span={6}>
              <input.BBDateTimeField bean={params} label="From" field="from" timeFormat={false} />
            </bs.Col>
            <bs.Col span={6}>
              <input.BBDateTimeField bean={params} label="To" field="to" timeFormat={false} />
            </bs.Col>
          </bs.Row>
          <BBRefVehicleFleet minWidth={400}
            appContext={appContext} pageContext={pageContext}
            bean={params} beanIdField={'vendorId'} beanLabelField={'vendorFullName'} label={T('Vendor')} placeholder={'Vendor'} />
        </div>
        <bs.Toolbar>
          <bs.Button laf="info" onClick={this.onLoadBill}>
            <FeatherIcon.Search size={12} /> {T('Search')}
          </bs.Button>
        </bs.Toolbar>
      </div>
    )
  }
}

class TMSBillAccountingList extends entity.DbEntityList {
  onCreateBFSOneTemplate = () => {
    const { appContext, pageContext, plugin } = this.props;

    const successCB = (bills: any[]) => {
      try {
        bs.dialogHideById(dialogId);
      } catch (e) {

      }
      let filteredBills = plugin.getListModel().getFilterRecords();
      for (let bill of bills) {
        let billId = bill['id'];
        let findBill = filteredBills.find((rec) => rec['id'] === billId);
        if (findBill) {
          findBill['tmsBillFee'] = bill['tmsBillFee'] ? bill['tmsBillFee'] : {};
          findBill['verifyHblNo'] = bill['tmsBillForwarderTransport']['verifyHblNo'];
        }
      }
      const createContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
        let config: bs.TabPaneConfig = {
          tabs: [
            {
              name: 'fee-template', label: T('Fee Template'), active: true,
              renderContent: (_ctx: bs.UIContext) =>
                < TMSBillBFSOneFeeTemplate appContext={appCtx} pageContext={pageCtx}
                  plugin={TMSBillBFSOneFeeTemplate.createPlugin(filteredBills)} />
            },
            {
              name: 'vehicle-info-template', label: T('Vehicle Info Template'),
              renderContent: (_ctx: bs.UIContext) =>
                <TMSBillBFSOneVehicleInfoTemplate appContext={appContext} pageContext={pageContext} plugin={TMSBillBFSOneVehicleInfoTemplate.createPlugin(filteredBills)} />
            },
          ]
        }

        return (
          <bs.DefaultTabPane className="flex-vbox" config={config} />
        )

      }
      pageContext.createPopupPage('bfs-one-template', T('BFSOne Template'), createContent, { size: 'xl' });
    }

    appContext
      .createHttpBackendCall('TMSBillService', 'verifyTMSBillHblNos', { ids: plugin.getListModel().getFilteredRecordIds() })
      .withSuccessData(successCB)
      .call();
    let html = (
      <h4>
        {'Loading... Please wait.'}
      </h4>
    )
    let dialogId = bs.dialogShow(T(`Loading`), html, { backdrop: 'static' })
  }

  createVGridConfig() {
    let { appContext, pageContext, plugin } = this.props;
    let config: grid.VGridConfig = {
      title: T("TMS Report Bill"),
      record: {
        fields: [
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          { name: 'code', label: T('Code') },
          { name: 'label', label: T('File No.') },
          {
            name: 'dateTime', label: T('Date'), width: 100, cssClass: 'flex-grow-1 text-end', dataType: 'date',
            sortable: true, filterableType: 'date', filterable: true, format: util.text.formater.compactDate,
          },
          {
            name: 'hwbNo', label: T('HBL No'),
          },
          {
            name: "responsibleFullName", label: T('PIC.'), width: 120, cssClass: 'pe-1', filterableType: 'options', filterable: true,
          },
          {
            name: 'office', label: T('Office'), width: 80, hint: 'Office', filterableType: 'Options', filterable: true,
          },
          {
            name: 'customerFullName', label: T('Customer'), width: 170, cssClass: 'px-1', sortable: true,
            dataTooltip: true, filterableType: 'Options', filterable: true,
          },
          {
            name: 'mode', label: T('Mode'), width: 100, filterableType: 'options', filterable: true, sortable: true,
          },
          { name: 'bookingCode', label: T('Booking/Bill'), width: 150 },
          {
            name: 'truckType', label: T('Truck Type'), cssClass: 'justify-content-center ',
            filterableType: 'options', filterable: true,
          },
          { name: 'containerNo', label: T('Container No') },
          {
            name: 'licensePlate', label: T('License Plate'), width: 120,
            filterableType: 'options', filterable: true,
            fieldDataGetter: (record) => {
              let vendorBills: Array<any> = record['vendorBills'];
              if (vendorBills) {
                return vendorBills.map(vendorBill => vendorBill.licensePlate).join(', ');
              }
              return null;
            },
          },
          {
            name: 'address', label: T('Address'), width: 250, dataTooltip: true,
            fieldDataGetter(record) {
              let mode = record['mode'];
              if (TMSBillTransportationModeTools.isExport(mode)) {
                return record['senderAddress'];
              }
              if (TMSBillTransportationModeTools.isImport(mode)) {
                return record['receiverAddress'];
              }
              let senderAddress = record['senderAddress'] ? `Pickup: ${record['senderAddress']}\n` : '';
              let receiverAddress = record['receiverAddress'] ? `Delivery: ${record['receiverAddress']}` : '';
              return `${senderAddress} ${receiverAddress}`;
            },
          },
          {
            name: 'fixedPayment', label: T(`Bill Fixed`), dataType: 'double'
          },
          {
            name: 'extraPayment', label: T(`Bill Extra`), dataType: 'double'
          },
          {
            name: 'totalPayment', label: T('Bill Total'), dataType: 'double'
          },
          {
            name: 'vendorFixed', label: T('Vendor Fixed'), dataType: 'double'
          },
          {
            name: 'vendorExtra', label: T('Vendor Extra'), dataType: 'double'
          },
          {
            name: 'vendorCost', label: T('Vendor Total Cost'), dataType: 'double'
          },
        ],

      },

      toolbar: {
        actions: [
        ],
        filters: entity.DbEntityListConfigTool.TOOLBAR_FILTERS(!!plugin.searchParams),
      },
      footer: {
        page: {
          render: (ctx: grid.VGridContext) => {
            return (
              <bs.Toolbar className='border' >
                <bs.Button laf="info" onClick={this.onCreateBFSOneTemplate}>
                  <FeatherIcon.Check size={12} /> {T('Create BFS Template')}
                </bs.Button>
                <XLSXCustomButton className="p-2 my-1" tableName="tms-bill" context={ctx}
                  options={{ fileName: `Vendor Bill ${util.TimeUtil.toCompactDateFormat(new Date())}.xlsx`, modelName: 'tms-bill' }}
                  appContext={appContext} pageContext={pageContext} fieldSelect={'all'} selectMode="all" />
              </bs.Toolbar>
            )
          }
        },
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          },
        }
      }
    }
    return config;
  }
}