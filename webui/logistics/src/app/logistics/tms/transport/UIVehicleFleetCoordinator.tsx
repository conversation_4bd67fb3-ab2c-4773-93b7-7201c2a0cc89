import React from 'react';
import { bs, input, entity } from '@datatp-ui/lib';
import { module } from '@datatp-ui/erp';

import { T } from '../backend';
import { VehicleFleetURL } from '../RestURL';

export class UIVehicleFleetCoordinatorForm extends entity.AppDbEntity {
  render() {
    let { appContext, pageContext, observer } = this.props;
    let writeCap = pageContext.hasUserWriteCapability();
    let vehicleFleet = observer.getMutableBean();
    return (
      <div className='flex-vbox form'>
        <label className='form-label'>{T('Coordinator Account')}</label>
        <module.account.BBRefAccount
          appContext={appContext} pageContext={pageContext}
          placeholder='Enter Coordinator Account'
          bean={vehicleFleet} accountIdField={'accountId'} accountLabelField={'accountFullName'}
          onPostUpdate={(inputUI: React.Component, bean: any, selectOpt: any, userInput: string) => {
            vehicleFleet['code'] = selectOpt['loginId'];
            vehicleFleet['label'] = selectOpt['fullName'];
            this.forceUpdate();
          }} />
        <bs.Row>
          <bs.Col span={12}>
            <input.BBStringField label={T('Label')} bean={vehicleFleet} field="label" disable={!writeCap}
              inputObserver={observer} required />
          </bs.Col>
        </bs.Row>
        <bs.Row>
          <bs.Col span={12}>
            <input.BBStringField label={T('Code')} inputObserver={observer} required
              bean={vehicleFleet} field={"code"} disable={!writeCap || !observer.isNewBean()} />
          </bs.Col>
        </bs.Row>
        <bs.Row>
          <bs.Col span={12}>
            <input.BBStringField label={T('Fleet Label')} bean={vehicleFleet} field={"fleetLabel"} disable={!writeCap} />
          </bs.Col>
        </bs.Row>

        <bs.Row>
          <bs.Col span={12}>
            <input.BBTextField label={T('Description')} style={{ height: '15em' }} bean={vehicleFleet} field={'description'} disable={!writeCap} />
          </bs.Col>
        </bs.Row>
      </div>
    )
  }
}

export class UIVehicleFleetCoordinatorEditor extends entity.AppDbComplexEntityEditor {
  render() {
    let { appContext, pageContext, observer } = this.props;
    let writeCap = pageContext.hasUserWriteCapability();
    return (
      <div className='flex-vbox'>
        <UIVehicleFleetCoordinatorForm {...this.props} />
        <bs.Toolbar className='border' hide={!writeCap}>
          <entity.WButtonEntityCommit appContext={appContext} pageContext={pageContext} observer={observer}
            onPostCommit={this.onPostCommit} label={T('Transport Fleet Coordinator')}
            commitURL={VehicleFleetURL.coordinator.save} />
          <entity.WButtonEntityReset
            appContext={appContext} pageContext={pageContext} observer={observer}
            onPostRollback={this.onPostRollback} />
        </bs.Toolbar>
      </div>
    );
  }
}