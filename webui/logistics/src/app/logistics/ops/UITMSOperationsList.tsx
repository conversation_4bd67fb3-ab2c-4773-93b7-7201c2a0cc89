import React from 'react';
import * as FeatherIcon from 'react-feather';

import { grid, sql, bs, entity, input, util, server, app } from '@datatp-ui/lib';
import { module } from "@datatp-ui/erp";
import { T } from '../tms/backend';
import { TMSBillTransportationMode, TMSOperationsStatus } from '../tms/models';
import { TMSBillTransportationModeTools, TMSUtils, TMSVGridConfigTool, UIVendorAttachments, WButtonListenDataChange } from '../tms/utils';
import { UITMSReceiptOfDeliveryPrint } from '../tms/bill/UITMSBillPrint';
import { UITMSOperationsReport } from './UITMSOperationsReport';
import { ManagementRestURL } from '../tms/RestURL';
import { BBRefTMSCustomer } from '../tms/partner/BBRefTMSCustomer';
import { BBRefWarehouseLocationConfig } from './BBRefWarehouseLocationConfig';

function isValidTimeFormat(input: string) {
  const regex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
  return regex.test(input);
}
export class UITMSOperationsListPlugin extends entity.DbEntityListPlugin {
  constructor(dateRange?: util.TimeRange, maxReturn: number = 1000) {
    super();

    this.backend = {
      context: 'company',
      service: 'TMSOperationsService',
      searchMethod: 'searchOperations'
    }

    this.searchParams = {
      params: {},
      filters: [
        ...sql.createSearchFilter()
      ],
      optionFilters: [
        sql.createStorageStateFilter([entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED])
      ],
      "rangeFilters": [
        ...sql.createDateTimeFilter("delivery_plan", "Date", dateRange),
      ],
      maxReturn: maxReturn,
    }
  }

  loadData(uiList: entity.DbEntityList<any>) {
    let params = { 'params': this.searchParams }
    this.createBackendSearch(uiList, params).call();
  }

  withDataScope = (dataScope?: app.AppDataScope) => {
    this.addSearchParam("dataScope", dataScope?.scope);
    return this;
  }

  withIds(ids: Array<number>) {
    this.addSearchParam('ids', ids);
    return this;
  }
}
const compactDate = (val: string) => {
  if (!val) return;
  return util.text.formater.compactDate(val);
}

export class UITMSOperationsList extends entity.DbEntityList {
  idTracker: number = 0;
  idTrackerNext() { return ++this.idTracker; };
  opsCopyField = {
    currentRow: 0,
    count: 0
  }

  onClearCopyField = () => {
    this.opsCopyField = {
      currentRow: 0,
      count: 0
    }
  }

  createVGridConfig() {
    let { appContext, pageContext, plugin } = this.props;
    let thisUI = this;
    let writeCap = pageContext.hasUserWriteCapability();
    const opsFields = ['opsAccountFullName', 'opsAccountMobile', 'identificationNo', 'status'];
    const onInputChange = (fieldCtx: grid.FieldContext, oldVal: any, newVal: any) => {
      let { displayRecord, fieldConfig, gridContext } = fieldCtx;
      let event: grid.VGridCellEvent = {
        row: displayRecord.row, field: fieldConfig, event: 'Modified', data: displayRecord
      }

      if (opsFields.includes(fieldConfig.name)) {
        let isOpsUpdated = true;
        for (let field of opsFields) {
          if (!displayRecord.getValue(field)) {
            isOpsUpdated = false;
            break;
          };
        };
        displayRecord.record['isOpsUpdated'] = isOpsUpdated;
      }
      gridContext.broadcastCellEvent(event);
    };

    const onCtrlD = (event: any, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
      let { plugin } = this.props;
      let fields = ['opsAccountFullName', 'opsAccountMobile', 'identificationNo'];
      if (this.opsCopyField.currentRow != dRecord.row) {
        this.opsCopyField.currentRow = dRecord.row;
        this.opsCopyField.count = 0;
      }
      let key: string = event.key;
      if (key) key = key.toLowerCase();
      if (event.ctrlKey && key === 'c') {
        this.opsCopyField.count++;
        let row = dRecord.row;
        let nextDRec = plugin.getListModel().getDisplayRecordList().getDisplayRecordAt(row + this.opsCopyField.count);
        while (!nextDRec.isDataRecord()) {
          this.opsCopyField.count++;
          nextDRec = plugin.getListModel().getDisplayRecordList().getDisplayRecordAt(row + this.opsCopyField.count);
        }
        for (let field of fields) {
          nextDRec.record[field] = dRecord.record[field];
        }
        nextDRec.getRecordState().markModified(true);
        let fieldCtx: grid.FieldContext = {
          displayRecord: nextDRec,
          gridContext: this.getVGridContext(),
          fieldConfig: field,
          tabIndex: 0,
          focus: false
        }
        onInputChange(fieldCtx, '', '');
      }
    }

    let config: grid.VGridConfig = {
      record: {
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          {
            name: 'label', label: T('File No.'), width: 150, filterableType: 'string', filterable: true,
            state: { showRecordState: true }, container: 'fixed-left',
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row === cell.getRow()) {
                  cell.forceUpdate()
                }
              },
            },
            editor: {
              type: 'string', onInputChange: onInputChange,
              renderCustom: (fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
                let { displayRecord, fieldConfig, tabIndex, focus } = fieldCtx;
                let ops = displayRecord.record;
                let readOnly = ops['tmsBillId'] ? true : false;
                return (
                  <input.BBStringField tabIndex={tabIndex} focus={focus} disable={readOnly}
                    bean={ops} field={fieldConfig.name} onInputChange={onInputChange} />
                )
              },
            }
          },
          { name: 'code', label: T('Code'), state: { visible: false } },
          { name: 'tmsFileTrucking', label: T('File Trucking'), dataTooltip: true, width: 140 },
          {
            name: 'customerFullName', label: T('Customer'), width: 170, sortable: true,
            dataTooltip: true, filterableType: 'Options', filterable: true,
            editor: {
              type: 'string',
              onInputChange: onInputChange,
              renderCustom: (fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
                let { displayRecord, fieldConfig, tabIndex, focus } = fieldCtx;
                let ops = displayRecord.record;
                const oldVal = ops[fieldConfig.name];
                let readOnly = ops['tmsBillId'] ? true : false;
                return (
                  <BBRefTMSCustomer minWidth={400} disable={readOnly || !writeCap} allowUserInput
                    appContext={appContext} pageContext={pageContext} tabIndex={tabIndex} autofocus={focus}
                    bean={ops} beanIdField={'customerId'} beanLabelField={'customerFullName'} placeholder={'Customer'}
                    onPostUpdate={(_inputUI: React.Component, bean: any, _selectOpt: any, _userInput: string) => {
                      onInputChange(bean, fieldConfig.name, oldVal, bean[fieldConfig.name])
                    }}
                  />
                )
              },
            }
          },
          {
            name: 'deliveryPlan', label: T('Delivery Plan'), width: 150, cssClass: 'flex-grow-1 text-end',
            sortable: true, filterableType: 'date', filterable: true, format: compactDate,
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row == cell.getRow() && event.field.name === 'deliveryPlan') {
                  cell.forceUpdate()
                }
              },
            },
            editor: {
              type: 'date',
              onInputChange: onInputChange,
              renderCustom: (fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
                let { displayRecord, fieldConfig, tabIndex, focus } = fieldCtx;

                let ops = displayRecord.record;
                let time = ops['time'];
                let deliveryPlan: String = ops['deliveryPlan'];
                if (isValidTimeFormat(time) && deliveryPlan) {
                  ops['deliveryPlan'] = deliveryPlan.replace("00:00:00", time + ":00");
                }
                let cssClass = fieldConfig.cssClass;
                let readOnly = ops['tmsBillId'] ? true : false;
                return (
                  <input.BBDateInputMask className={cssClass}
                    bean={ops} field={fieldConfig.name} tabIndex={tabIndex} focus={focus} format='DD/MM/YYYY' timeFormat
                    onInputChange={onInputChange} disabled={readOnly} />
                );
              },
            }
          },
          {
            name: "responsibleFullName", label: T('PIC.'), filterableType: 'options', filterable: true,
            customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let uiList = ctx.uiRoot as entity.DbEntityList
              const { appContext, pageContext } = uiList.props;
              return (
                <div className='flex-hbox justify-content-center align-items-center' >
                  <module.account.WAvatars className='px-2'
                    appContext={appContext} pageContext={pageContext} avatarIds={[record['responsibleAccountId']]}
                    avatarIdType='AccountId' width={20} height={20} borderRadius={10} />
                  <bs.Tooltip tooltip={record['responsibleFullName']} className="flex-hbox">
                    {record['userName']}
                  </bs.Tooltip>
                </div>
              )
            }
          },
          {
            name: 'mode', label: T('Mode'), width: 100, filterableType: 'options', filterable: true, sortable: true,
            computeCssClasses(ctx, dRecord) {
              return TMSBillTransportationModeTools.getColor(dRecord.record.mode);
            },
            fieldDataGetter(record) {
              return TMSBillTransportationModeTools.getLabel(record.mode);
            },
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row == cell.getRow() && event.field.name === 'mode') {
                  cell.forceUpdate()
                }
              },
            },
            editor: {
              type: 'string',
              onInputChange: onInputChange,
              renderCustom: (fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
                let { displayRecord, fieldConfig, tabIndex, focus } = fieldCtx;
                let ops = displayRecord.record;
                let cssClass = fieldConfig.cssClass;
                let readOnly = ops['tmsBillId'] ? true : false;
                if (fieldConfig.computeCssClasses) cssClass = `${cssClass} ${fieldConfig.computeCssClasses(thisUI.getVGridContext(), displayRecord)}`;
                if (readOnly) {
                  return (
                    <div className={cssClass}>
                      {fieldConfig.fieldDataGetter ? fieldConfig.fieldDataGetter(ops) : ops[fieldConfig.name]}
                    </div>
                  )
                }
                return (
                  <input.BBSelectField className={cssClass}
                    bean={ops} field={fieldConfig.name} tabIndex={tabIndex} focus={focus}
                    options={[
                      TMSBillTransportationMode.ExportFcl, TMSBillTransportationMode.ExportLcl, TMSBillTransportationMode.ExportAir,
                      TMSBillTransportationMode.ImportFcl, TMSBillTransportationMode.ImportLcl, TMSBillTransportationMode.ImportAir,
                    ]}
                    optionLabels={[
                      'Export Fcl', 'Export Lcl', 'Export Air',
                      'Import Fcl', 'Import Lcl', 'Import Air',
                    ]} onInputChange={onInputChange} />
                )
              },
            }
          },
          {
            name: 'quantity', label: T('Quantity'), width: 80, cssClass: 'px-1',
            editor: {
              type: 'double', onInputChange: onInputChange,
              renderCustom: (fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
                let { displayRecord, fieldConfig, tabIndex, focus } = fieldCtx;

                let ops = displayRecord.record;
                let cssClass = fieldConfig.cssClass;
                let readOnly = ops['tmsBillId'] ? true : false;
                return <input.BBNumberField className={cssClass} bean={ops} field={fieldConfig.name} tabIndex={tabIndex} focus={focus}
                  disable={readOnly} onInputChange={onInputChange} />
              },
            },
          },
          {
            name: 'truckNo', label: T('Truck No'), dataTooltip: true, width: 350,
            editor: {
              type: 'string', onInputChange: onInputChange,
              renderCustom: (fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
                let { displayRecord, fieldConfig, tabIndex, focus } = fieldCtx;
                let ops = displayRecord.record;
                let readOnly = ops['tmsBillId'] ? true : false;
                if (readOnly) {
                  return (
                    <bs.CssTooltip>
                      <bs.CssTooltipToggle style={{ textOverflow: 'ellipsis', overflow: 'hidden' }}>
                        {ops[fieldConfig.name]}
                      </bs.CssTooltipToggle>
                      <bs.CssTooltipContent
                        className="d-flex align-items-end"
                        style={{ whiteSpace: 'break-spaces', transform: 'translate(-5px, -75px)' }}>
                        <div className='p-2'>
                          {ops[fieldConfig.name]}
                        </div>
                      </bs.CssTooltipContent>
                    </bs.CssTooltip>
                  )
                }

                return (
                  <input.BBStringField
                    disable={readOnly}
                    bean={ops} field={fieldConfig.name} tabIndex={tabIndex} focus={focus} onInputChange={onInputChange} />
                )
              },
            }
          },
          {
            name: 'warehouseLabel', label: T('Full Return WH'), width: 175, state: { visible: true },
            editor: {
              type: 'string',
              onInputChange: onInputChange,
              renderCustom: (fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
                let { displayRecord, fieldConfig, tabIndex, focus } = fieldCtx;
                let ops = displayRecord.record;
                const oldVal = ops[fieldConfig.name];
                let readOnly = ops['tmsBillId'] ? true : false;
                return (
                  <BBRefWarehouseLocationConfig minWidth={500}
                    appContext={appContext} pageContext={pageContext} bean={ops}
                    autofocus={focus} tabIndex={tabIndex} allowUserInput
                    disable={readOnly}
                    beanIdField='warehouseId' beanLabelField='warehouseLabel'
                    placeholder='Full Return WH' onPostUpdate={
                      (inputUI: React.Component, bean: any, selectOpt: any, userInput: string) => onInputChange(bean, fieldConfig.name, oldVal, bean[fieldConfig.name])
                    }
                  />
                )
              },
            }
          },
          {
            name: 'opsAccountFullName', label: T('OPS'), width: 170, sortable: true,
            dataTooltip: true, filterableType: 'Options', filterable: true,
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row == cell.getRow() && event.field.name === 'opsAccountFullName') {
                  cell.forceUpdate()
                }
              },
            },
            editor: {
              type: 'string',
              onInputChange: onInputChange,
              renderCustom: (fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
                let { displayRecord, fieldConfig, tabIndex, focus } = fieldCtx;
                let ops = displayRecord.record;
                return (
                  <div className={`flex-hbox`} onKeyDown={(event) => onCtrlD(event, fieldConfig, displayRecord)}>
                    <module.company.hr.BBRefEmployee
                      appContext={appContext} pageContext={pageContext} bean={ops}
                      autofocus={focus} tabIndex={tabIndex} allowUserInput
                      disable={!writeCap} selectedId='accountId' department={'ops'}
                      beanIdField='opsAccountId' beanLabelField='opsAccountFullName'
                      placeholder='OPS' onPostUpdate={
                        (_inputUI, _bean, selectOpt, _userInput) => {
                          if (selectOpt) {
                            ops['opsAccountMobile'] = selectOpt['mobile'];
                            ops['identificationNo'] = selectOpt['identificationNo'];
                          }
                          if (onInputChange) onInputChange(ops, fieldConfig.name, null, ops[fieldConfig.name]);
                        }}
                    />
                  </div>
                )
              },
            },
          },
          {
            name: 'opsAccountMobile', label: 'OPS Mobile',
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row == cell.getRow() && event.field.name === 'opsAccountFullName') {
                  cell.forceUpdate()
                }
              },
            },
            editor: {
              type: 'string',
              onInputChange: onInputChange
            }
          },
          {
            name: 'identificationNo', label: 'Identities',
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row == cell.getRow() && event.field.name === 'opsAccountFullName') {
                  cell.forceUpdate()
                }
              },
            },
            editor: {
              type: 'string',
              onInputChange: onInputChange
            }
          },
          {
            name: 'note', label: T('Note'), width: 200,
            editor: { type: 'text', onInputChange: onInputChange, }
          },
          {
            name: 'vendorAttachFileTotal', label: T('ATT'), width: 40, container: 'fixed-right',
            hint: T('VendorFileAttachment'),
            customRender: (_ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let { pageContext } = this.props
              let ops = dRecord.record;
              //TODO: clean originVendorBillId, only use vendorBillId
              let vendorBillId = ops['originVendorBillId'] ? ops['originVendorBillId'] : ops['vendorBillId'];
              let tmsBillId = ops['tmsBillId'];
              if (!tmsBillId) {
                return (
                  <div className={'flex-hbox'}>
                    {this.renderAttachments(dRecord.record)}
                  </div>
                );
              };

              const onShow = () => {
                const createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
                  return (
                    <bs.TabPane>
                      <bs.Tab name='attachments' label={T('Attachments')} active={true}>
                        {vendorBillId ?
                          <UIVendorAttachments readOnly={!writeCap}
                            appContext={appCtx} pageContext={pageCtx}
                            loadMethod={{ component: 'TMSVendorBillService', method: 'findTMSVendorAttachments', param: { id: vendorBillId } }}
                            commitMethod={{ component: 'TMSVendorBillService', method: 'saveTMSVendorAttachments', param: { id: vendorBillId } }}
                          />
                          :
                          <div>
                            <bs.Button laf='link' onClick={() => { }}>
                              {T('+ Create Vendor Bill')}
                            </bs.Button>
                          </div>
                        }
                      </bs.Tab>
                    </bs.TabPane>
                  );
                }
                pageContext.createPopupPage('attachments', 'Attachments', createAppPage, { size: 'lg' })
              }
              let vendorAttachFileTotal = ops['vendorAttachFileTotal'] ? ops['vendorAttachFileTotal'] : 0;
              let originTotalVendorAttachFiles = ops['originTotalVendorAttachFiles'] ? ops['originTotalVendorAttachFiles'] : 0;
              return (
                <div className={'flex-hbox justify-content-center'}>
                  {TMSUtils.renderFileAttachmentsIcon(
                    (vendorAttachFileTotal + originTotalVendorAttachFiles), onShow
                  )}
                </div>
              )
            }
          },
          {
            name: 'status', label: T('Status'), container: 'fixed-right', width: 100, hint: 'Status',
            filterable: true, filterableType: 'options',
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row == cell.getRow() && (event.field.name === 'status')) {
                  cell.forceUpdate();
                }
              },
            },
            customRender: (ctx: grid.VGridContext, fieldConfig: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let ops = dRecord.record;
              let status = ops['status'];
              let updateStatus = (status: TMSOperationsStatus) => {
                appContext.createHttpBackendCall('TMSOperationsService', 'updateStatusOperations', { id: ops.id, status: status })
                  .withSuccessData((_data: any) => {
                    appContext.addOSNotification("success", T(`Update Success`));
                    ops['status'] = status;
                    this.getVGridContext().getVGrid().forceUpdateView();
                    this.onSyncVendorBill(appContext, [ops]);
                  })
                  .call();
              }
              if (status === TMSOperationsStatus.NEED_CONFIRM) {
                return (
                  <div className={'flex-hbox justify-content-center'} >
                    <bs.Button disabled={!writeCap || !ops.id} laf='warning' className='my-5 p-1' style={{ width: '100%', fontSize: 12 }} outline
                      onClick={() => updateStatus(TMSOperationsStatus.PROCESSING)} >
                      {T('Confirm')} <FeatherIcon.Check size={10} />
                    </bs.Button>
                  </div>
                )
              }
              let laf: 'primary' | 'secondary' | 'info' | 'success' | 'danger' | 'warning' = 'warning'
              if (status === TMSOperationsStatus.PROCESSING) {
                laf = 'primary';
              } else if (status === TMSOperationsStatus.DONE) {
                laf = 'success';
              }
              return <bs.Badge className='w-100 m-0' style={{ padding: 1 }} laf={laf}>
                <bs.BadgeLabel>
                  <input.BBSelectField className='p-0' style={{ height: 16, fontSize: 12 }} bean={ops} field={'status'}
                    options={[TMSOperationsStatus.PROCESSING, TMSOperationsStatus.DONE]}
                    onInputChange={(bean: any, field: string, oldVal: any, newVal: any) => {
                      updateStatus(newVal);
                    }} />
                </bs.BadgeLabel>
              </bs.Badge>
            }
          },
          ...TMSVGridConfigTool.ENTITY_COLUMNS
        ],
        computeDataRowHeight: (_ctx: grid.VGridContext, dRec: grid.DisplayRecord) => {
          if (dRec.isDataRecord()) return 35;
          return 20;
        },
        editor: {
          supportViewMode: ['aggregation', 'table'],
          enable: true
        },
        control: {
          width: 40,
          items: [
            {
              name: 'del', hint: 'Delete', icon: FeatherIcon.Trash2,
              customRender: (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
                if (dRecord.record['storageState'] === entity.StorageState.ACTIVE) {
                  let button = (
                    <button key={'del'} type='button' className='btn btn-link' disabled={false} onClick={() => this.updateStorageStateRecord(ctx, dRecord)}>
                      <FeatherIcon.Trash2 className='me-1' size={12} />
                    </button>
                  );
                  return button
                }
              },
            },
            {
              name: 'copy', hint: 'Copy', icon: FeatherIcon.Copy,
              onClick: (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
                let ops = dRecord.record;
                ops = { ...ops, id: null, tmsBillId: null };
                ctx.model.insertDisplayRecordAt(dRecord.row, ops);
                grid.getRecordState(ops).markModified(true);
                ctx.getVGrid().forceUpdateView();
              },
            }
          ],
        },
        summary: {
          dataCellHeight: 265,
          render: (ctx: grid.VGridContext, dRecord: grid.DisplayRecord, _viewMode: grid.ViewMode) => {
            let ops = dRecord.record;
            let updateStatus = (status: TMSOperationsStatus) => {
              if ((status === TMSOperationsStatus.DONE && ops.status === TMSOperationsStatus.PROCESSING) || (status === TMSOperationsStatus.PROCESSING && ops.status === TMSOperationsStatus.NEED_CONFIRM)) {
                let callbackConfirm = () => {
                  appContext.createHttpBackendCall('TMSOperationsService', 'updateStatusOperations', { id: ops.id, status: status })
                    .withSuccessData((_data: any) => {
                      appContext.addOSNotification("success", T(`Update Success`));
                      ops.status = status;
                      this.getVGridContext().getVGrid().forceUpdateView();
                    })
                    .call();

                }
                bs.dialogConfirmMessage('Confirm Message', T(`You want to change the status [${status}] of the ops`), callbackConfirm);
              }
            }
            let html = (
              <grid.SummaryCell className='flex-hbox' context={ctx} record={dRecord}>
                <div className={'flex-vbox'}>
                  <div className='flex-hbox align-items-center'>
                    <label className="fw-lighter pe-1">{T('File No')}: </label>
                    <span className={`px-1`}>{ops.label}</span>
                    <span className='px-1'>{TMSBillTransportationModeTools.getLabel(ops.mode)}</span>
                  </div>
                  <div className='flex-hbox'>
                    <label className="fw-lighter pe-1">{T('Customer')}: </label>
                    <span className="text-info">{ops.customerFullName}</span>
                  </div>
                  <div className='flex-hbox'>
                    <label className="fw-lighter pe-1">{T('Delivery Plan')}: </label>
                    <span className="text-info">{compactDate(ops.deliveryPlan)}</span>
                  </div>
                  <div className='flex-hbox'>
                    <label className="fw-lighter pe-1">{T('Truck No')}: </label>
                    <span className="text-info" style={{ whiteSpace: 'break-spaces' }}>{ops.truckNo}</span>
                  </div>
                  <div className='flex-hbox'>
                    <label className="fw-lighter pe-1">{T('Quantity')}: </label>
                    <span className="text-info">{ops.quantity}</span>
                  </div>
                  <div className='flex-hbox align-items-center'>
                    <label className="fw-lighter pe-1">{T('PIC')}: </label>
                    <span className="text-info">
                      <div className='flex-hbox justify-content-center align-items-center' >
                        <module.account.WAvatars className='px-2'
                          appContext={appContext} pageContext={pageContext} avatarIds={[ops['responsibleAccountId']]}
                          avatarIdType='AccountId' width={20} height={20} borderRadius={10} />
                        <bs.Tooltip tooltip={ops['responsibleFullName']} className="flex-hbox">
                          {`${ops['userName']}(${ops['responsibleFullName']})`}
                        </bs.Tooltip>
                      </div>
                    </span>
                  </div>
                  <div className='flex-hbox'>
                    <label className="fw-lighter pe-1">{T('Full Return WH')}: </label>
                    <span className="text-info" style={{ whiteSpace: 'break-spaces' }}>{ops.warehouseLabel}</span>
                  </div>
                  <div className='flex-hbox'>
                    <label className="fw-lighter pe-1">{T('Ops')}: </label>
                    <span className="text-info">{ops.opsAccountFullName}</span>
                    <span className="px-1">{ops.opsAccountMobile}</span>
                  </div>
                  <div className='flex-hbox'>
                    <label className="fw-lighter pe-1">{T('Note')}: </label>
                    <span className="text-info">{ops.note}</span>
                  </div>
                  <div className='flex-hbox'>
                    <bs.Button style={{ height: 25, width: 70 }} className='m-1 p-1' laf='warning'
                      disabled={ops.status == TMSOperationsStatus.DONE || ops.status == TMSOperationsStatus.PROCESSING}
                      outline={ops.status != TMSOperationsStatus.NEED_CONFIRM} >
                      {T('Confirm')}
                    </bs.Button>
                    <bs.Button style={{ height: 25, width: 70 }} className='m-1 p-1' laf='primary'
                      disabled={ops.status == TMSOperationsStatus.DONE}
                      outline={ops.status != TMSOperationsStatus.PROCESSING}
                      onClick={() => updateStatus(TMSOperationsStatus.PROCESSING)}>
                      {T('Process')}
                    </bs.Button>
                    <bs.Button style={{ height: 25, width: 70 }} className='m-1 p-1' laf='success'
                      disabled={ops.status == TMSOperationsStatus.NEED_CONFIRM}
                      outline={ops.status != TMSOperationsStatus.DONE}
                      onClick={() => updateStatus(TMSOperationsStatus.DONE)}>
                      {T('Done')}
                    </bs.Button>
                  </div>
                </div>
              </grid.SummaryCell >
            );
            return html;
          }
        },
      },

      toolbar: {
        actions: [
          ...this.toolbarChangeStorageStates(!writeCap),
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_ADD(!writeCap, T("Add")),
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_DELETE(!pageContext.hasUserAdminCapability(), "Del"),
        ],
        filters: entity.DbEntityListConfigTool.TOOLBAR_FILTERS(plugin.searchParams ? true : false, 'filter'),
        filterActions: [
          ...entity.DbEntityListConfigTool.TOOLBAR_AUTO_REFRESH('auto-refresh-bill', T('Refresh')),
        ],
      },

      footer: {
        page: {
          render: (ctx: grid.VGridContext) => {
            let listenSearchParams: sql.SqlSearchParams = {
              filters: [
                ...sql.createSearchFilter()
              ],
              optionFilters: [
                sql.createStorageStateFilter([entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED])
              ],
              maxReturn: 50,
            }
            return <bs.Toolbar className='border' hide={!writeCap}>
              <WButtonListenDataChange
                ctx={ctx} label="Waiting" searchParams={listenSearchParams}
                severCallComponent="TMSOperationsService" severCallMethod="searchOperationsRecentlyChange"
                recordFilter={new entity.ExcludeRecordFilter(plugin.getRecords(), 'id')}
                onClick={this.onShowRecordChanged} />
              <entity.WButtonEntityWrite icon={FeatherIcon.File}
                appContext={appContext} pageContext={pageContext} disable={!writeCap} hide={!pageContext.hasUserWriteCapability() || bs.ScreenUtil.isSmallScreen()}
                label={T('Report')} onClick={this.onReport} />
              <entity.WButtonEntityWrite appContext={appContext} pageContext={pageContext} icon={FeatherIcon.Printer}
                label={T('Print POD')} onClick={this.onPrintProofOfDelivery} />
              <entity.WButtonEntityWrite appContext={appContext} pageContext={pageContext} icon={FeatherIcon.Save}
                label={T('Save Changes')} onClick={this.onSave} />
            </bs.Toolbar>
          }
        },
      },
      view: {
        currentViewName: bs.ScreenUtil.isSmallScreen() ? 'table' : 'aggregation',
        availables: {
          table: {
            viewMode: 'table'
          },
          aggregation: {
            viewMode: 'aggregation',
            treeWidth: 150,
            createAggregationModel(_ctx: grid.VGridContext) {
              let model = new grid.AggregationDisplayModel(T('All'), false);
              model.addAggregation(new grid.DateValueAggregation(T("Date"), "deliveryPlan", "YYYY/MM/DD", true).withSortBucket('desc'));
              model.addAggregation(new grid.ValueAggregation(T("Full Return WH"), "warehouseLabel", false).withSortBucket('desc'));
              return model;
            }
          }
        }
      }
    }
    if (bs.ScreenUtil.isSmallScreen()) delete config.record.control;
    return config;
  }

  onShowRecordChanged = (uiSource: WButtonListenDataChange) => {
    UILoadOperations
    let { pageContext } = this.props;
    let createPageContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (<UILoadOperations ctx={this.getVGridContext()} uiListenData={uiSource} appContext={appCtx} pageContext={pageCtx} />
      )
    }
    pageContext.createPopupPage('new-request', 'New Request', createPageContent, { size: 'xl' })
  }

  renderAttachments(ops: any) {
    let { pageContext } = this.props;
    let commitURL = ManagementRestURL.ops.saveAttachments(ops['id']);
    let loadURL = ManagementRestURL.ops.loadAttachments(ops['id']);
    if (ops['tmsBillId']) {
      commitURL = ManagementRestURL.tmsBill.saveAttachments(ops['tmsBillId']);
      loadURL = ManagementRestURL.tmsBill.loadAttachments(ops['tmsBillId'])
    }
    const onShow = () => {
      const createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
        return (
          <module.storage.UIAttachments readOnly={!pageContext.hasUserWriteCapability()}
            appContext={appCtx} pageContext={pageCtx}
            commitURL={commitURL}
            loadURL={loadURL}
            onChange={(plugin) => {
              if (ops['billAttachmentCount'] != plugin.getModel().getRecords().length) {
                ops['billAttachmentCount'] = plugin.getModel().getRecords().length;
                this.getVGridContext().getVGrid().forceUpdateView();
              }
            }}
          />)
      }
      pageContext.createPopupPage('attachments', T("Attachments"), createAppPage, { size: "lg", backdrop: "static" });
    }
    let opsAttachmentCount = ops['billAttachmentCount'];
    let countIcon = (
      <div className={'align-items-center justify-content-center text-center'}
        style={{
          height: 12, width: 12,
          marginTop: -20, marginLeft: 5
        }}>
      </div>
    );
    if (opsAttachmentCount) {
      countIcon = (
        <div className={'align-items-center justify-content-center text-center'}
          style={{
            height: 12, width: 12, borderRadius: 50, fontSize: 12,
            backgroundColor: 'red', color: 'white',
            marginTop: -20, marginLeft: 5
          }}>
          {opsAttachmentCount}
        </div>
      );
    }
    return (
      <bs.Button className='flex-hbox flex-grow-0 text-warning align-items-left' style={{ width: 12, fontSize: 12, marginLeft: 5, marginBottom: 8 }}
        onClick={onShow} laf='link'>
        <FeatherIcon.FileText size={12} />
        {countIcon}
      </bs.Button>
    )
  }

  toolbarChangeStorageStates(readOnly: boolean = false) {
    if (readOnly) {
      return []
    }
    let actions: Array<grid.VGridActionConfig> = [
      {
        name: 'storage-actions', label: 'Storage Actions', icon: FeatherIcon.Database,
        supportViewMode: ['table', 'aggregation', 'grid'],
        createComponent: function (ctx: grid.VGridContext) {
          let uiList = ctx.uiRoot as UITMSOperationsList;
          let html = (
            <bs.Popover flex-hbox-grow-0 closeOnTrigger=".btn">
              <bs.PopoverToggle laf='primary' outline>
                <FeatherIcon.Database size={12} /> <FeatherIcon.ChevronDown size={12} />
              </bs.PopoverToggle>
              <bs.PopoverContent>
                <div className='flex-vbox' style={{ width: 150 }}>
                  <bs.Button laf='primary' key={`storage-state-active`} className="mb-1 w-100 text-start px-2 py-1"
                    onClick={() => { uiList.updateStorageState(entity.StorageState.ACTIVE) }} >
                    {T(entity.StorageState.ACTIVE)}
                  </bs.Button>
                  <bs.Button laf='primary' key={`storage-state-archived`} className="mb-1 w-100 text-start px-2 py-1"
                    onClick={() => { uiList.updateStorageState(entity.StorageState.ARCHIVED) }} >
                    {T(entity.StorageState.ARCHIVED)}
                  </bs.Button>
                </div>
              </bs.PopoverContent>
            </bs.Popover>
          )
          return html;
        }
      },
    ]
    return actions
  }

  onReport = () => {
    let { appContext, pageContext } = this.props;
    let createPageContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <UITMSOperationsReport appContext={appCtx} pageContext={pageCtx} />
      );
    }
    pageContext.addPageContent('report', T('Report'), createPageContent);
  }

  updateStorageState(newStorageState: string) {
    let { appContext, pageContext, plugin } = this.props;
    let ids = plugin.getListModel().getSelectedRecordIds();

    appContext.createHttpBackendCall('TMSOperationsService', 'changeStorageState', { req: { entityIds: ids, newStorageState: newStorageState } })
      .withSuccessData((_data: any) => {
        appContext.addOSNotification("success", T(`Update Storage State Success`));
        plugin.getListModel().removeSelectedDisplayRecords();
        this.getVGridContext().getVGrid().forceUpdateView();
      })
      .call();

  }


  onPrintProofOfDelivery = () => {
    let { pageContext, plugin } = this.props;
    let selectRecs = plugin.getListModel().getSelectedRecords();
    let ids: Array<any> = [];
    for (let rec of selectRecs) {
      let tmsBillId = rec['tmsBillId'];
      if (tmsBillId == null) {
        bs.notificationShow("danger", `File ${rec['label']} is not TMS Bill!!!`);
        return;
      }
      if (tmsBillId) ids.push(tmsBillId);
    }
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <UITMSReceiptOfDeliveryPrint key={`print-${util.IDTracker.next()}`}
          appContext={appCtx} pageContext={pageCtx} billIds={ids} />
      )
    }
    pageContext.createPopupPage('proof-of-delivery', T('Proof Of Delivery'), createAppPage, { size: 'xl' });
  }


  updateStorageStateRecord(ctx: grid.VGridContext, dRecord: grid.DisplayRecord) {
    let ops = dRecord.record;
    ctx.model.getDisplayRecordList().toggleDeletedDisplayRecord(dRecord.row);
    if (dRecord.getRecordState().isMarkDeleted()) {
      dRecord.getRecordState().markModified();
      ops['editState'] = 'DELETED';
    } else {
      dRecord.getRecordState().markModified(false);
      delete ops['editState'];
    }
    ctx.getVGrid().forceUpdateView();
  }

  onNewAction() {
    let { plugin } = this.props;
    let newOps = { status: TMSOperationsStatus.NEED_CONFIRM, 'uikey': `new/${this.idTrackerNext()}` }
    plugin.getListModel().addRecord(newOps);
    grid.getRecordState(newOps).markModified();
    this.getVGridContext().getVGrid().forceUpdateView();
  }

  onDeleteAction() {
    let { plugin, appContext } = this.props;
    let callbackConfirm = () => {
      let ids = plugin.getListModel().getSelectedRecordIds();

      appContext.createHttpBackendCall('TMSOperationsService', 'deleteOperations', { ids: ids })
        .withSuccessData((_data: any) => {
          appContext.addOSNotification("success", T(`Delete Success`));
          plugin.getListModel().removeSelectedDisplayRecords();
          this.getVGridContext().getVGrid().forceUpdateView();
        })
        .call();

    }
    let message = (<div className="text-danger">Do you want to delete these ops?</div>);
    bs.dialogConfirmMessage('Confirm Message', message, callbackConfirm);
  }

  onSave = () => {
    let { plugin, appContext } = this.props;
    this.onClearCopyField();
    let records = plugin.getListModel().getMarkModifiedRecords();
    for (let rec of records) {
      if (!rec['id']) rec['uikey'] = `new-${util.IDTracker.next()}`;
    }
    appContext.createHttpBackendCall('TMSOperationsService', 'saveOperations', { operations: records })
      .withSuccessData((data: any) => {
        appContext.addOSNotification("success", T(`Save Success`));
        for (let record of records) {
          if (record['editState'] === 'DELETED') {
            plugin.getListModel().removeRecord(record);
          }
        }
        TMSUtils.reloadData(this, 'TMSOperationsService', 'searchOperations', data);
        this.onNoticePic(appContext, records);
        this.onSyncVendorBill(appContext, records);
      })
      .call();

  }

  onNoticePic = (appContext: app.AppContext, records: Array<any>) => {
    let holder: Array<any> = [];
    records.forEach(rec => {
      if (rec['isOpsUpdated']) holder.push(rec);
    });
    if (holder.length == 0) return;
    let params = {
      records: holder
    }
    appContext.createHttpBackendCall('TMSOperationsService', 'noticeMessageToPic', params)
      .withSuccessData((_data: any) => { })
      .withFailNotification('danger', T('Notice Pic Ops Change Info Fail!'))
      .call();
  }

  onSyncVendorBill = (appContext: app.AppContext, records: Array<any>) => {
    let ids: Array<any> = [];
    records.forEach(rec => {
      if (rec['id']) ids.push(rec['id']);
    });
    if (ids.length == 0) return;
    let params = {
      ids: ids
    }
    appContext.createHttpBackendCall('TMSRestCallService', 'syncVendorBillWithTMSOperations', params)
      .withSuccessNotification('success', T('Sync Vendor Bills Success'))
      .withFailNotification('danger', T('Sync Vendor Bills Fail!'))
      .call();
  }
}

interface UILoadOperationsProps extends app.AppComponentProps {
  uiListenData: WButtonListenDataChange;
  ctx: grid.VGridContext;
}
class UILoadOperations extends app.AppComponent<UILoadOperationsProps> {
  render(): React.ReactNode {
    let { ctx, appContext, pageContext, uiListenData } = this.props;
    let uiList = ctx.uiRoot as entity.DbEntityList;
    let { plugin } = uiList.props;
    uiListenData.onChange = (_updateRecords: Array<any>) => {
      this.forceUpdate();
    }
    let ids: Array<number> = [];
    for (let sel of uiListenData.filteredRecords) {
      ids.push(sel['id']);
    }

    let operationsPlugin = new entity.DbEntityListPlugin();
    if (ids.length > 0) operationsPlugin = new UITMSOperationsListPlugin().withDataScope(app.AppDataScope.COMPANY).withIds(ids);
    return (
      <div className="flex-vbox">
        <UITMSOperationsList key={`${util.IDTracker.next()}-operation-waiting`}
          appContext={appContext} pageContext={pageContext}
          plugin={operationsPlugin} readOnly />
        <bs.Toolbar>
          <bs.Button laf="info" onClick={() => {
            let recordModified = operationsPlugin.getRecords();
            if (recordModified.length == 0) {
              pageContext.back();
              return;
            }
            for (let rec of recordModified) {
              let findRec = plugin.getRecords().find(sel => rec['id'] === sel['id']);
              if (findRec) {
                for (let propertyName in rec) {
                  findRec[propertyName] = rec[propertyName];
                }
              } else {
                plugin.getRecords().unshift(rec);
                grid.initRecordStates([rec]);
              }
            }
            plugin.getListModel().filter();
            uiList.forceUpdate();
            pageContext.back();

            uiListenData.filteredRecords = [];
            uiListenData.notifiedRecords = [];
            this.forceUpdate();
          }}>
            {T('Take All')}
          </bs.Button>
        </bs.Toolbar>
      </div>
    )
  }
}

