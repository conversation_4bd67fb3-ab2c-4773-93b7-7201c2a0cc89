import React from "react";
import * as icon from 'react-feather';
import { app, bs } from '@datatp-ui/lib';
import { module } from "@datatp-ui/erp";

// import { T } from "../forwarder/price";
import { UIApiPluginTMSGoodsTracking } from "./api/TMSTrackingPlugin";
import { UIApiPluginVendorBillAttachments } from "./api/TMSVendorBillAttachmentsPlugin";
import { UITMSVendorBillList, UITMSVendorBillPlugin } from "../vendor/UITMSVendorBillList";

import space = app.space;
import UIApiPluginManager = module.security.UIApiPluginManager;
import { UIApiPluginTMSVendorBillTracking as UIApiPluginVendorBillTracking } from "./api/TMSVendorBillTrackingPlugin";
import { UIInvoiceReconcileListPlugin, UITMSLOLOInvoiceReconcileList } from "../tms/document/UIInvoiceReconcile";
import { T } from "../tms/backend";

class VendorBillSpacePlugin extends space.SpacePlugin {
  constructor() {
    super('tms/vendor-bill', 'VendorBill Navigation');
  }


  override createUserScreens(): space.ScreenConfig[] {
    return [
      {
        id: "vendor-bill", label: "Vendor Bill", icon: icon.Book,
        checkPermission: {
          feature: { module: 'tms', name: 'user-vendor-bill' },
          requiredCapability: app.WRITE,
        },
        renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return (
            <UITMSVendorBillList appContext={appCtx} pageContext={pageCtx}
              plugin={new UITMSVendorBillPlugin().withDataScope(pageCtx.getUserDataScope())} />
          );
        },
        screens: [
          {
            id: "tms-reconcile-vendor-bill", label: T("TMS Reconcile"),
            checkPermission: {
              feature: { module: 'tms', name: 'user-vendor-bill' },
              requiredCapability: app.WRITE,
            },
            renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              return (
                <bs.TabPane>
                  <bs.Tab key='fcl-invoice-reconcile' name="fcl-invoice-reconcile" label={'FCL Invoice Reconcile'} active>
                    <UITMSLOLOInvoiceReconcileList appContext={appCtx} pageContext={pageCtx} invoice_type={'FCL'}
                      plugin={new UIInvoiceReconcileListPlugin('FCL').withDataScope(pageCtx.getUserDataScope())} />
                  </bs.Tab>
                  <bs.Tab key='lcl-invoice-reconcile' name="lcl-invoice-reconcile" label={'LCL Invoice Reconcile'}>
                    <UITMSLOLOInvoiceReconcileList appContext={appCtx} pageContext={pageCtx} invoice_type={'LCL'}
                      plugin={new UIInvoiceReconcileListPlugin('LCL').withDataScope(pageCtx.getUserDataScope())} />
                  </bs.Tab>
                </bs.TabPane>
              )
            }
          }
        ]
      }
    ];
  }

  override createCompanyScreens(): space.ScreenConfig[] {
    return [
      {
        id: "vendor-bill", label: "Vendor Bill", icon: icon.Book,
        checkPermission: {
          feature: { module: 'tms', name: 'user-vendor-bill' },
          requiredCapability: app.WRITE,
        },
        renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return (
            <UITMSVendorBillList appContext={appCtx} pageContext={pageCtx}
              plugin={new UITMSVendorBillPlugin().withDataScope(app.AppDataScope.COMPANY)} />
          );
        },
        screens: [
          {
            id: "tms-reconcile-vendor-bill", label: T("TMS Reconcile"),
            checkPermission: {
              feature: { module: 'tms', name: 'user-vendor-bill' },
              requiredCapability: app.WRITE,
            },
            renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              return (
                <bs.TabPane>
                  <bs.Tab key='fcl-invoice-reconcile' name="fcl-invoice-reconcile" label={'FCL Invoice Reconcile'} active>
                    <UITMSLOLOInvoiceReconcileList appContext={appCtx} pageContext={pageCtx} invoice_type={'FCL'}
                      plugin={new UIInvoiceReconcileListPlugin('FCL').withDataScope(app.AppDataScope.COMPANY)} />
                  </bs.Tab>
                  <bs.Tab key='lcl-invoice-reconcile' name="lcl-invoice-reconcile" label={'LCL Invoice Reconcile'}>
                    <UITMSLOLOInvoiceReconcileList appContext={appCtx} pageContext={pageCtx} invoice_type={'LCL'}
                      plugin={new UIInvoiceReconcileListPlugin('LCL').withDataScope(app.AppDataScope.COMPANY)} />
                  </bs.Tab>
                </bs.TabPane>
              )
            }
          }
        ]
      }
    ];
  }

}

export function init() {
  space.SpacePluginManager.register(new VendorBillSpacePlugin());
  UIApiPluginManager.register(new UIApiPluginTMSGoodsTracking());
  UIApiPluginManager.register(new UIApiPluginVendorBillAttachments());
  UIApiPluginManager.register(new UIApiPluginVendorBillTracking());
}