import React from 'react';
import { util, bs, input, sql, app, entity } from '@datatp-ui/lib';
import { module } from '@datatp-ui/erp';

import * as FeatherIcon from 'react-feather'

import { T } from '../tms/backend';
import { VehicleFleetURL } from '../tms/RestURL';
import { UIVehicleTripRouteListEditor } from '../tms/vehicle/UIVehicleTripRouteList';
import { UIVehicleTripGoodsTrackingList, UIVehicleTripGoodsTrackingListPlugin } from '../tms/vehicle/tracking/UIVehicleTripGoodsTrackingList';
import { UIDssTrackingReportRouteList } from '../tms/vehicle/dss/UIDssTrackingReportRouteList';
import { BBRefTransporter } from '../tms/transport/BBRefTransporter';
import { LoadableVehicleMap } from './UIVehicleTripList';
import { BBRefVehicleFleet } from '../tms/vehicle/BBRefVehicleFleet';
import { BBRefVehicle } from '../tms/vehicle/BBRefVehicle';

export class UIVehicleTripProjectTaskForm extends entity.AppDbComplexEntityEditor {
  onChangeAddress = (_bean: any, fieldName: string, selectLocation: any) => {
    if (!selectLocation) return;
    let { observer } = this.props;
    observer.replaceBeanProperty(fieldName, selectLocation['address'])
  }

  render() {
    let { appContext, pageContext, observer } = this.props;
    const writeCap = pageContext.hasUserWriteCapability();
    let trip = observer.getMutableBean();
    return (
      <div className="flex-vbox" key={this.viewId}>
        <div className="form">
          <bs.Row>
            <bs.Col span={6}>
              <label className='form-label'>{T('Vehicle Fleet')}</label>
              <BBRefVehicleFleet placeholder='Enter Fleet'
                appContext={appContext} pageContext={pageContext}
                bean={trip} beanIdField={'fleetId'} beanLabelField={'fleetLabel'} disable={!writeCap}
                onPostUpdate={(inputUI: React.Component, bean: any, selectOpt: any, userInput: string) => {
                  this.forceUpdate()
                }} />
            </bs.Col>
            <bs.Col span={3}>
              <input.BBStringField label={T('Task Code')} bean={trip} field={'taskCode'} disable={true} />
            </bs.Col>
            <bs.Col span={3}>
              <input.BBSelectField label={T('Task Status')} bean={trip} field={'taskStatus'}
                options={['PLAN', 'APPROVE', 'TRANSPORTING', 'FINISH', 'CONFIRM', 'DONE']}
                disable={!writeCap} onInputChange={() => {
                  trip.updateStatus = true;
                }} />
            </bs.Col>
          </bs.Row>
          <bs.Row>
            <bs.Col span={6}>
              <label className='form-label'>{T('Coordinator')}</label>
              {/* <BBCoordinatorAutoComplete
                appContext={appContext} pageContext={pageContext}
                bean={trip} field={'coordinatorId'} labelField={'coordinatorFullName'} fleetId={trip.fleetId} disable={!writeCap} /> */}
            </bs.Col>
            <bs.Col span={3}>
              {/* TODO: Chien refactor BBLocationAutoComplete -> BBRefLocation */}
              <label className='form-label'>{T('From Location')}</label>
              {/* <module.settings.BBLocationAutoComplete
                appContext={appContext} pageContext={pageContext} bean={trip}
                field={'fromLocationCode'} labelField={'fromLocationLabel'}
                onChangeCallback={(_bean: any, selectCustomer: any) => this.onChangeAddress('fromAddress', _bean, selectCustomer)}
                locationTypes={['Address']} useSelectBean={false} disable={!writeCap} /> */}
            </bs.Col>
            <bs.Col span={3}>
              {/* TODO: Chien refactor BBLocationAutoComplete -> BBRefLocation */}
              <label className='form-label'>{T('To Location')}</label>
              {/* <module.settings.BBLocationAutoComplete
                appContext={appContext} pageContext={pageContext} bean={trip}
                field={'toLocationCode'} labelField={'toLocationLabel'}
                onChangeCallback={(_bean: any, selectCustomer: any) => this.onChangeAddress('toAddress', _bean, selectCustomer)}
                locationTypes={['Address']} useSelectBean={false} disable={!writeCap} /> */}
            </bs.Col>
          </bs.Row>
          <bs.Row>
            {/* <bs.Col span={3}>
              <label className='form-label'>{T('Vehicle')}</label>
              <BBVehicleAutoComplete appContext={appContext} pageContext={pageContext} disable={!writeCap}
                bean={trip} field={'vehicleId'} labelField={'vehicleLabel'} fleetId={trip.fleetId} />
            </bs.Col> */}
            <bs.Col span={3}>
              <label className='form-label'>{T('Driver')}</label>
              {/* <BBTransporterAutoComplete
                appContext={appContext} pageContext={pageContext} disable={!writeCap}
                bean={trip} field={'driverId'} labelField={'driverFullName'} fleetId={trip.fleetId} /> */}
            </bs.Col>
            <bs.Col span={3}>
              <input.BBStringField label={T('From Address')} bean={trip} field={"fromAddress"} disable={!writeCap} />
            </bs.Col>
            <bs.Col span={3}>
              <input.BBStringField label={T('To Address')} bean={trip} field={"toAddress"} disable={!writeCap} />
            </bs.Col>
          </bs.Row>
          <bs.Row>
            <bs.Col span={6}>
              <input.BBNumberField label={T('Estimate Time (Day)')}
                bean={trip} field={'estimateTimeInDay'} disable={!writeCap} validators={[util.validator.ZERO_AND_GREATER_VALIDATOR]} />
            </bs.Col>
            <bs.Col span={6}>
              <input.BBNumberField label={T('Actual Time (Day)')}
                bean={trip} field={'actualTimeInDay'} disable={!writeCap} validators={[util.validator.ZERO_AND_GREATER_VALIDATOR]} />
            </bs.Col>
          </bs.Row>
          <bs.Row>
            <bs.Col span={3}>
              <input.BBNumberField label={T('Estimate Distance (Km)')} bean={trip} field="estimateDistanceInKm" disable={!writeCap}
                validators={[util.validator.ZERO_AND_GREATER_VALIDATOR]} />
            </bs.Col>
            <bs.Col span={3}>
              <input.BBNumberField label={T('Actual Distance (Km)')} bean={trip} field="actualDistanceInKm" disable={!writeCap}
                validators={[util.validator.ZERO_AND_GREATER_VALIDATOR]} />
            </bs.Col>
            <bs.Col span={3}>
              <input.BBNumberField label={T('Start Odometer (Km)')} bean={trip} field="startOdometer" validators={[util.validator.ZERO_AND_GREATER_VALIDATOR]}
                disable={!writeCap} />
            </bs.Col>
            <bs.Col span={3}>
              <input.BBNumberField label={T('End Odometer (Km)')} bean={trip} field="endOdometer" validators={[util.validator.ZERO_AND_GREATER_VALIDATOR]}
                disable={!writeCap} />
            </bs.Col>
          </bs.Row>
          <bs.Row>
            <bs.Col span={3}>
              <input.BBNumberField label={T('Estimate Cost')} bean={trip} field="estimateCost" validators={[util.validator.ZERO_AND_GREATER_VALIDATOR]}
                disable={!writeCap} />
            </bs.Col>
            <bs.Col span={3}>
              <input.BBNumberField label={T('Actual Cost')} bean={trip} field="actualCost" validators={[util.validator.ZERO_AND_GREATER_VALIDATOR]}
                disable={!writeCap} />
            </bs.Col>
            <bs.Col span={6}>
              <module.settings.BBRefCurrency
                appContext={appContext} pageContext={pageContext} disable={!writeCap}
                bean={trip} beanIdField={'currency'} placeholder='Currency' label='Currency' />
            </bs.Col>
          </bs.Row>
        </div>

        <bs.Card header={T('Vehicle Trip Stop')}>
          <UIVehicleTripRouteListEditor appContext={appContext} pageContext={pageContext} readOnly={!writeCap}
            plugin={observer.createVGridEntityListEditorPlugin('stops')}
            dialogEditor={true} editorTitle={T('Vehicle Trip Stop')} />
        </bs.Card>
      </div>
    );
  }
}

export class UIVehicleTripForm extends entity.AppDbComplexEntityEditor {
  onGenerateCode(label: any) {
    let { observer } = this.props;
    if (!observer.isNewBean()) return;
    let code = entity.UUIDTool.generateWithAnyTokens(
      T("You need to enter the label"),
      [util.text.toFileName(label)],
      true
    );
    observer.getMutableBean().code = code;
    this.forceUpdate();
  }

  onComputeRoute = () => {
    let { observer } = this.props;
    observer.commitAndGet();
    let tripRoutes = observer.getComplexArrayProperty('tripRoutes', []);
    let route;
    for (let tripRoute of tripRoutes) {
      let fromLocation = tripRoute['fromLocationLabel'] ? tripRoute['fromLocationLabel'] : '...';
      let toLocation = tripRoute['toLocationLabel'] ? tripRoute['toLocationLabel'] : '...';
      if (!route) {
        route = fromLocation + '-' + toLocation;
      } else {
        route += "-" + fromLocation + '-' + toLocation;
      }
    }
    observer.replaceBeanProperty('route', route);
    this.forceUpdate();
  }

  render() {
    let { appContext, pageContext, observer } = this.props;
    const writeCap = pageContext.hasUserWriteCapability();
    let trip = observer.getMutableBean();

    return (
      <bs.TabPane key={this.viewId} className="flex-vbox">
        <bs.Tab label={T('Trip Info')} name={'trip-info'} active>
          <bs.Row>
            <bs.Col md={6} span={12}>
              <bs.FormLabel className='form-label'>{T('Fleet')}</bs.FormLabel>
              <BBRefVehicleFleet minWidth={400}
                appContext={appContext} pageContext={pageContext} disable={!writeCap}
                bean={trip} beanIdField={'fleetId'} beanLabelField={'fleetLabel'} placeholder={'Fleet'}
              />
            </bs.Col>
            <bs.Col md={6} span={12}>
              <input.BBSelectField label={T('Status')} bean={trip} field={'taskStatus'}
                options={['PLAN', 'TRANSPORTING', 'DONE']}
                onInputChange={() => trip.updateStatus = true}
                disable={!writeCap} />
            </bs.Col>
          </bs.Row>
          <bs.Row>
            <bs.Col md={3} span={6}>
              <BBRefVehicle
                appContext={appContext} pageContext={pageContext} disable={!writeCap}
                allowUserInput placeholder='License plate'
                placement="bottom-start" offset={[0, 5]} minWidth={500}
                bean={trip} label={T('License plate')} beanIdField={'vehicleId'} beanLabelField={'vehicleLabel'}
                loadParams={(searchParams: sql.SqlSearchParams) => {
                  searchParams.params = {
                    ...searchParams.params,
                    'vehicleFleetId': trip['fleetId']
                  };
                }}
                onPostUpdate={(_inputUI, _bean, selectOpt, _userInput) => {
                  if (selectOpt) {
                    if (selectOpt['vehicleFleetId']) {
                      trip['fleetId'] = selectOpt['vehicleFleetId'];
                      trip['fleetLabel'] = selectOpt['fleetName'];
                    }
                    trip['identificationNo'] = selectOpt['idCard'];
                    trip['mobile'] = selectOpt['mobile'];
                    trip['driverFullName'] = selectOpt['transporterFullName'];
                    trip['driverId'] = selectOpt['transporterId'];
                  }
                  this.forceUpdate();
                }}
              />
            </bs.Col>
            <bs.Col md={3} span={6}>
              <BBRefTransporter
                key={`driver-${trip.driverId}`}
                label={T('Driver')}
                appContext={appContext} pageContext={pageContext}
                minWidth={300} allowUserInput
                placement="left" placeholder="Enter Transporter"
                bean={trip} beanIdField={'driverId'}
                beanLabelField={'driverFullName'} refTransporterBy={'id'} onPostUpdate={(inputUI, bean, option, userInput) => {
                  if (option) {
                    bean.mobile = option.mobile;
                    bean['identificationNo'] = option['idCard']
                  }
                  this.forceUpdate();
                }} />
            </bs.Col>
            <bs.Col md={3} span={6}>
              <input.BBStringField bean={trip} label={T('Mobile')} field={'mobile'} disable={!writeCap} />
            </bs.Col>
            <bs.Col md={3} span={6}>
              <input.BBStringField bean={trip} label={T('ID')} field={'identificationNo'} disable={!writeCap} />
            </bs.Col>
          </bs.Row>
          <bs.Row>
            <bs.Col span={6}>
              <input.BBStringField bean={trip} field={'route'} label={T('Route')} disable={!writeCap} onRefresh={this.onComputeRoute} />
            </bs.Col>
            <bs.Col span={3}>
              <input.BBNumberField
                label={T('Estimate Distance (Km)')} bean={trip} field="estimateDistanceInKm" disable={!writeCap}
                validators={[util.validator.ZERO_AND_GREATER_VALIDATOR]}
                onInputChange={(_bean, _field, _oldVal, newVal) => {
                  trip['actualDistanceInKm'] = newVal;
                  this.forceUpdate();
                }} />
            </bs.Col>
            <bs.Col span={3}>
              <input.BBNumberField
                label={T('Actual Distance (Km)')} bean={trip} field="actualDistanceInKm" disable={!writeCap}
                validators={[util.validator.ZERO_AND_GREATER_VALIDATOR]} />
            </bs.Col>
          </bs.Row>
          <bs.Row>
            {/* <bs.Col lg={3} md={6} span={12}>
              <bs.FormLabel>{T('Delivery Plan')}</bs.FormLabel>
              <input.BBDateInputMask bean={trip} field={'planStartTime'} format={"DD/MM/YYYY"} disabled={!writeCap} />
            </bs.Col>
            <bs.Col lg={3} md={6} span={12}>
              <input.BBStringField bean={trip} field={'planTime'} label={T('Time')} disable={!writeCap} />
            </bs.Col> */}
            <bs.Col lg={3} md={6} span={12}>
              <bs.FormLabel>{T('Plan Start Time')}</bs.FormLabel>
              <input.BBDateInputMask bean={trip} field={'planStartTime'} format={"DD/MM/YYYY"} timeFormat disabled={!writeCap}
                onInputChange={() => this.forceUpdate()} />
            </bs.Col>
            <bs.Col lg={3} md={6} span={12}>
              <bs.FormLabel>{T('Plan End Time')}</bs.FormLabel>
              <input.BBDateInputMask bean={trip} field={'planEndTime'} format={"DD/MM/YYYY"} timeFormat disabled={!writeCap}
                onInputChange={() => this.forceUpdate()} />
            </bs.Col>
            <bs.Col lg={3} md={6} span={12}>
              <bs.FormLabel>{T('Actual Start Time')}</bs.FormLabel>
              <input.BBDateInputMask bean={trip} field={'actualStartTime'} format={"DD/MM/YYYY"} timeFormat disabled={true}
                onInputChange={() => {
                  trip.updateStatus = true;
                  this.forceUpdate()
                }} />
            </bs.Col>
            <bs.Col lg={3} md={6} span={12}>
              <bs.FormLabel>{T('Actual End Time')}</bs.FormLabel>
              <input.BBDateInputMask bean={trip} field={'actualEndTime'} format={"DD/MM/YYYY"} timeFormat disabled={true}
                onInputChange={() => {
                  trip.updateStatus = true;
                  this.forceUpdate()
                }} />
            </bs.Col>
          </bs.Row>
          {/* <input.BBTextField
            bean={trip} field={'description'} label={T('Description')} disable={!writeCap} /> */}
        </bs.Tab>
        <bs.Tab label={T('Technical')} name={'technical'}>
          <bs.Row>
            <bs.Col md={6} span={12}>
              <input.BBStringField
                label={T('Label')} bean={trip} field="label" disable={!writeCap} required inputObserver={observer}
                onInputChange={(bean, field, oldVal, newVal) => this.onGenerateCode(newVal)} />
            </bs.Col>
            <bs.Col md={6} span={12}>
              <input.BBStringField
                label={T('Code')} bean={trip} field="code" disable={!writeCap || !observer.isNewBean()}
                onRefresh={() => this.onGenerateCode(trip.label)} required inputObserver={observer} />
            </bs.Col>
          </bs.Row>
        </bs.Tab>
      </bs.TabPane>
    );
  }
}


interface UIVehicleTripEditorProps extends entity.AppComplexEntityEditorProps {
  trackings?: Array<any>;
  allowSaveAndUpdateTracking?: boolean;
}

export class UIVehicleTripEditor extends entity.AppDbComplexEntityEditor<UIVehicleTripEditorProps> {
  onCreateVehicleTrip = () => {
    let { appContext, trackings, observer } = this.props;

    const dataCallback = (trackingModel: any) => {
      if (trackings) {
        let trackingResults = trackingModel.trackingMapResults;
        for (let trackingResult of trackingResults) {
          let tracking = trackings?.find(sel => trackingResult.id === sel.id);
          for (const propertyName in trackingResult) {
            tracking[propertyName] = trackingResult[propertyName];
          }
        }
      }
      this.onPostCommit(trackingModel);
      appContext.addOSNotification("success", T('Save Vehicle Trip Bill Success!'));
    };
    let ids: Array<any> = [];
    trackings?.forEach(sel => ids.push(sel.id));
    let bean = observer.commitAndGet();
    let trackingModel = {
      vehicleTrip: bean,
      trackingIds: ids,
    }
    appContext
      .createHttpBackendCall('VehicleService', 'createVehicleTripForTracking', { model: trackingModel })
      .withSuccessData(dataCallback)
      .call();
  }

  onShowDssTrackingReportRoute = () => {
    let { appContext, pageContext, observer } = this.props;

    let trip = observer.getMutableBean();
    let params = {
      licensePlate: trip['vehicleLabel'],
      fromDate: trip['actualStartTime'] ? trip['actualStartTime'] : trip['planStartTime'] ? trip['planStartTime'] : util.TimeUtil.javaCompactDateTimeFormat(new Date()),
      toDate: trip['actualEndTime'] ? trip['actualEndTime'] : trip['planEndTime'] ? trip['planEndTime'] : util.TimeUtil.javaCompactDateTimeFormat(new Date())
    }
    appContext.createHttpBackendCall('DssTrackingService', 'findDssTrackingReportRoute', { params: params })
      .withSuccessData((data: any) => {
        let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return (
            <UIDssTrackingReportRouteList appContext={appContext} pageContext={pageContext}
              plugin={new entity.DbEntityListPlugin(data['data'])} />
          )
        }
        pageContext.createPopupPage('report-route', T('Report Route'), createAppPage, { size: 'xl' })
      })
      .call();
  }

  onShowMaps = () => {
    let { pageContext, observer } = this.props;
    let trip = observer.getMutableBean();
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <LoadableVehicleMap appContext={appCtx} pageContext={pageCtx} vehicleTrip={trip} />
      )
    }
    pageContext.createPopupPage('gps', T('GPS'), createAppPage, { size: 'xl' });
  }

  render() {
    let { appContext, pageContext, observer, allowSaveAndUpdateTracking } = this.props;
    const writeCap = pageContext.hasUserWriteCapability();
    return (
      <div className={'flex-vbox'}>
        <bs.ScrollableCards className='flex-vbox'>
          {<UIVehicleTripCard {...this.props} />}
        </bs.ScrollableCards>
        <bs.Toolbar className='border' hide={!writeCap}>
          <entity.WButtonEntityWrite
            appContext={appContext} pageContext={pageContext}
            label={T('Maps')}
            icon={FeatherIcon.Truck} onClick={() => this.onShowMaps()} />
          {allowSaveAndUpdateTracking ?
            <entity.WButtonEntityWrite
              appContext={appContext} pageContext={pageContext}
              label={observer.isNewBean() ? T('Create') : T('Save')}
              icon={FeatherIcon.Save} onClick={() => this.onCreateVehicleTrip()} />
            :
            <entity.WButtonEntityCommit
              appContext={appContext} pageContext={pageContext} observer={observer}
              label={T('Vehicle Trip')} onPostCommit={this.onPostCommit} commitURL={VehicleFleetURL.vehicleTrip.save} />
          }
        </bs.Toolbar>
      </div>
    );
  }
}

export class UIVehicleTripCard extends entity.AppDbComplexEntityEditor {

  render() {
    let { appContext, pageContext, observer } = this.props;
    const writeCap = pageContext.hasUserWriteCapability();
    return (
      <div className={'flex-vbox'}>
        <bs.Card header={T('Trip Info')}>
          <UIVehicleTripForm {...this.props} />
          <UIVehicleTripRouteListEditor
            appContext={appContext} pageContext={pageContext} readOnly={!writeCap}
            plugin={observer.createVGridEntityListEditorPlugin('tripRoutes')}
            dialogEditor={true} editorTitle={T('Vehicle Trip Route')} style={{ minHeight: 750 }} />
        </bs.Card>
        {observer.isNewBean() ? null :
          <bs.Card header={T('Attachments')} className='flex-vbox' style={{ minHeight: 400 }}>
            <module.storage.UIAttachments readOnly={!writeCap}
              appContext={appContext} pageContext={pageContext}
              commitURL={VehicleFleetURL.vehicleTrip.saveAttachments(observer.getBeanProperty('id'))}
              loadURL={VehicleFleetURL.vehicleTrip.loadAttachments(observer.getBeanProperty('id'))}
            />
          </bs.Card>
        }
      </div>
    );
  }
}
export class UIVehicleTrip extends entity.AppDbComplexEntityEditor<UIVehicleTripEditorProps> {
  render() {
    let { appContext, pageContext, observer, trackings } = this.props;
    const trackingPlugin =
      trackings ? new entity.DbEntityListPlugin(trackings)
        : new UIVehicleTripGoodsTrackingListPlugin().withVehicleTripId(observer.getBeanProperty("id"));
    return (
      <bs.VSplit>
        <bs.VSplitPane title={T('Trip Info')}>
          <UIVehicleTripEditor {...this.props} />
        </bs.VSplitPane>
        <bs.VSplitPane width={'35%'} title={T('Vehicle Trip Goods Trackings')}>
          <UIVehicleTripGoodsTrackingList type='page' summaryMode viewMode='table'
            plugin={trackingPlugin} appContext={appContext} pageContext={pageContext} readOnly />
        </bs.VSplitPane>
      </bs.VSplit>
    );
  }
}
//TODO: Chien - clean
// export interface WLoadableEntityProps extends app.AppComponentProps {
//   loadUrl: string;
//   onPostCommit?: (entity: any) => void;
// }
// export class UILoadableVehicleTripTab extends app.AppComponent<WLoadableEntityProps> {
//   observer: entity.ComplexBeanObserver | null = null;

//   constructor(props: WLoadableEntityProps) {
//     super(props);
//     let { appContext, loadUrl } = props;
//     let callBack = (response: server.BackendResponse) => {
//       let trip = response.data;
//       this.observer = new entity.ComplexBeanObserver(trip);
//       this.markLoading(false);
//       this.forceUpdate();
//     }
//     appContext.DO_NOT_USE_serverGET(loadUrl, null, callBack);
//   }

//   render() {
//     if (!this.observer) return this.renderLoading();
//     return this.renderEntity();
//   }

//   renderEntity(): React.ReactElement {
//     if (!this.observer) return this.renderLoading();
//     const { appContext, pageContext, readOnly, onPostCommit } = this.props;
//     return (
//       <bs.TabPane>
//         <bs.Tab name={'trip-info'} label={T('Trip Info')} active>
//           <UIVehicleTripEditor appContext={appContext} pageContext={pageContext} observer={this.observer}
//             onPostCommit={onPostCommit} readOnly={readOnly} />
//         </bs.Tab>
//         <bs.Tab name={'goods-tracking'} label={T('Trackings')} >
//           <UIVehicleTripGoodsTrackingList type='page' viewMode='table'
//             appContext={appContext} pageContext={pageContext} readOnly
//             plugin={new UIVehicleTripGoodsTrackingListPlugin().withVehicleTripId(this.observer.getBeanProperty("id"))} />
//         </bs.Tab>
//       </bs.TabPane>
//     )
//   }
// }