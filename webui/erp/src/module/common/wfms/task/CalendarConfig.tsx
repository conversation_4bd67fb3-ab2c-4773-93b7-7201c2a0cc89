import React from 'react';
import * as FeatherIcon from 'react-feather';

import { grid, calendar as cal, bs, calendar } from '@datatp-ui/lib';
import { DbEntityTaskList } from './DbEntityTaskList'
import { UserCustomCalendarPlugin, CompanyCustomCalendarPlugin } from './CalendarPlugin'
import { UserCalendarWeekCell, CompanyCalendarWeekCell } from './CalendarWeekCell'

import { T } from './backend';
import { CalendarMonthCell } from './CalendarMonthCell';

class CustomCalendarToolbar extends cal.CalendarToolbar {
  toggleGridView = () => {
    let gridContext = this._getGridContext();
    gridContext.config.toolbar.hide = !gridContext.config.toolbar.hide;
    gridContext.getVGrid().forceUpdateView();
  }

  renderCustomActions(): React.JSX.Element {
    let gridContext = this._getGridContext();
    let hideToolbar = gridContext.config.toolbar.hide;
    let EyeIcon = FeatherIcon.EyeOff;
    if (hideToolbar) EyeIcon = FeatherIcon.Eye;
    let html = (
      <bs.Button laf='secondary' outline onClick={this.toggleGridView} className="px-2 py-1 me-2">
        <EyeIcon size={12} /> Grid
      </bs.Button>
    );
    return html;
  }

  _getGridContext() {
    let { context } = this.props;
    let uiList = context.uiRoot as DbEntityTaskList;
    let gridContext = uiList.getVGridContext();
    return gridContext;
  }

  onChangePeriod(direction: 'prev' | 'next') {
    const { config } = this.props;
    const currentDate = config.selectedDate;
    const newDate = new Date(currentDate);

    if (config.view == cal.CalendarViewType.Month) {
      if (direction === 'prev') {
        newDate.setMonth(currentDate.getMonth() - 1);
      } else {
        newDate.setMonth(currentDate.getMonth() + 1);
      }
    } else {
      if (direction === 'prev') {
        newDate.setDate(currentDate.getDate() - 7);
      } else {
        newDate.setDate(currentDate.getDate() + 7);
      }
    }

    config.selectedDate = newDate;
    this._getGridContext().getVGrid().forceUpdateView();
  }

  onToday() {
    const { config } = this.props;
    config.selectedDate = new Date();
    this._getGridContext().getVGrid().forceUpdateView();
  }

  render(): React.JSX.Element {
    const { config } = this.props;
    const currentDate = config.selectedDate;
    const monthYear = currentDate.toLocaleString('default', { day: '2-digit', month: 'long', year: 'numeric' });
    let period = 'Month';
    if (config.view == cal.CalendarViewType.Week) period = 'Week';
    if (config.view == cal.CalendarViewType.Day) period = 'Day';
    if (config.view == cal.CalendarViewType.Year) period = 'Year';

    return (
      <div className="flex-hbox flex-grow-0 align-items-center px-2 py-1 border-bottom" >
        {/* Left section - Today button */}
        <div className="d-flex align-items-center">
          {this.renderCustomActions()}
          <bs.Button laf="primary" outline className="px-2 py-1"
            onClick={() => this.onToday()} title="Go to Today">
            Today
          </bs.Button>
        </div>

        {/* Center section - Month navigation and display */}
        <div className="flex-grow-1 d-flex align-items-center justify-content-center">
          <div className="d-flex align-items-center gap-2">
            <bs.Button
              laf="link"
              className="d-flex align-items-center justify-content-center p-1 text-secondary"
              style={{ width: '32px', height: '32px' }} hint={`Previous ${period}`}
              onClick={() => this.onChangePeriod('prev')}>
              <FeatherIcon.ChevronLeft size={16} />
            </bs.Button>

            <h5 className="mb-0 fw-semibold px-2" style={{ minWidth: '150px', textAlign: 'center' }}>
              {monthYear}
            </h5>

            <bs.Button
              laf="link"
              className="d-flex align-items-center justify-content-center p-1 text-secondary"
              style={{ width: '32px', height: '32px' }} hint={`Next ${period}`}
              onClick={() => this.onChangePeriod('next')}>
              <FeatherIcon.ChevronRight size={16} />
            </bs.Button>
          </div>
        </div>

        {/* Right section - View options */}
        <div className="d-flex align-items-center">
          <bs.ButtonGroup label="View Options" laf='group' smallScreenLaf="popover"
            popover={{ laf: 'primary', placement: 'bottom', outline: true }}>

            <bs.Button laf="secondary" outline className="px-2 py-1"
              active={config.view === calendar.CalendarViewType.Month}
              onClick={() => {
                config.view = calendar.CalendarViewType.Month;
                this._getGridContext().getVGrid().forceUpdateView();
              }}
              title="Month View">
              <FeatherIcon.Calendar size={14} />
              <span className="ms-1 d-none d-md-inline">Month</span>
            </bs.Button>

            <bs.Button laf="secondary" outline className="px-2 py-1"
              active={config.view === calendar.CalendarViewType.Week}
              onClick={() => {
                config.view = calendar.CalendarViewType.Week;
                this._getGridContext().getVGrid().forceUpdateView();
              }}
              title="Week View">
              <FeatherIcon.Calendar size={14} />
              <span className="ms-1 d-none d-md-inline">Week</span>
            </bs.Button>

            <bs.Button laf="secondary" outline className="px-2 py-1"
              active={config.view === calendar.CalendarViewType.Day}
              onClick={() => {
                console.log('TODO');

                // config.view = calendar.CalendarViewType.Day;
                // this._getGridContext().getVGrid().forceUpdateView();
              }}
              title="Day View">
              <FeatherIcon.Calendar size={14} />
              <span className="ms-1 d-none d-md-inline">Day</span>
            </bs.Button>
          </bs.ButtonGroup>
        </div>
      </div>
    );
  }
}

export function DbEntityTaskListCalendarConfig(calendarView?: cal.CalendarViewType) {
  let config: grid.VGridCalendarViewConfig = {
    viewMode: 'calendar',
    config: {
      view: calendarView ? calendarView : cal.CalendarViewType.Month,
      cellAction: cal.CellAction.All,
      cellMode: cal.HourCellMode.All,
      selectedDate: new Date(),
      record: {
        dateField: 'dueDate', dateFieldLabel: T('Due Date'), dateFieldSelect: true,
        toDateField: 'deadline', toDateFieldLabel: 'Deadline', toDateFieldSelect: false,
      },
      year: {},
      month: {
        renderCell(ctx: cal.CalendarContext, config: cal.CalendarConfig, date: Date) {
          let uiList = ctx.uiRoot as DbEntityTaskList;
          const { appContext, pageContext } = uiList.props;
          return (
            <CalendarMonthCell
              appContext={appContext} pageContext={pageContext} context={ctx} config={config} date={date} />
          );
        }
      },

      week: {
        renderCell(ctx: cal.CalendarContext, config: cal.CalendarConfig, date: Date) {
          let uiList = ctx.uiRoot as DbEntityTaskList;
          const { appContext, pageContext } = uiList.props;
          let space = uiList.props.space;
          if ('company' == space) {
            return (<CompanyCalendarWeekCell appContext={appContext} pageContext={pageContext} context={ctx} config={config} date={date} />);
          } else {
            return (<UserCalendarWeekCell appContext={appContext} pageContext={pageContext} context={ctx} config={config} date={date} />);
          }
        }
      },
      day: {
        renderCell(ctx: cal.CalendarContext, config: cal.CalendarConfig, date: Date) {
          //TODO: see CompanyCalendarWeekCell, UserCalendarWeekCell
          let hourRecordMap = ctx.getDateRecordMap().getByDay(date);
          if (!hourRecordMap) return null;
          let hourRecords = hourRecordMap.getRecordAtHour(config, date);
          if (!hourRecords || hourRecords.length == 0) return <></>;
          return (<div>{hourRecords.map(rec => rec.label)}</div>);
        }
      },

      toolbar: {
        render: (
          ctx: cal.CalendarContext, config: cal.CalendarConfig,
          onChangeCalendarConfig: () => void, onChangeHourCellMode?: (cellMode: cal.HourCellMode) => void) => {
          return (
            <CustomCalendarToolbar
              context={ctx} config={config} date={config.selectedDate}
              onChangeCalendarConfig={onChangeCalendarConfig} onChangeHourCellMode={onChangeHourCellMode} />
          );
        }
      },

      plugin: {
        label: 'Tasks', width: 320,
        render: (ctx: cal.CalendarContext, config: cal.CalendarConfig) => {
          let uiList = ctx.uiRoot as DbEntityTaskList;
          let space = uiList.props.space;
          if (space == 'company') {
            return <CompanyCustomCalendarPlugin context={ctx} config={config} />
          } else {
            return <UserCustomCalendarPlugin context={ctx} config={config} />
          }
        }
      }
    }
  };
  return config;
}