import React from 'react';
import * as FeatherIcon from 'react-feather';
import { calendar as cal, app, bs, util } from '@datatp-ui/lib';
import { DbEntityTaskCalendarPlugin, DbEntityTaskCalendarPluginManger } from './DbEntityTaskCalendarPlugin';
import { DbEntityTaskList, DbEntityUserTaskListPlugin } from './DbEntityTaskList';
import { T } from './backend';
import { WAvatars } from 'module/account';

const SESSION = app.host.DATATP_SESSION;


interface CalendarWeekCellProps extends app.AppComponentProps {
  context: cal.CalendarContext;
  config: cal.CalendarConfig;
  date: Date;
}

class CalendarWeekCell extends app.AppComponent<CalendarWeekCellProps> {
  render(): React.ReactElement {
    const { context, config, date } = this.props;
    let dayRecords = context.getDateRecordMap().getByDay(date);

    if (!dayRecords) return this.renderCell();
    let records = dayRecords.getAllRecords();
    if (records.length == 0) return this.renderCell();

    const events = records.map((record: any, index: number) => {
      let pluginName = record['entityRefType'];
      let taskPlugin = DbEntityTaskCalendarPluginManger.getPlugin(pluginName);

      if (taskPlugin.renderWeekCellTask) {
        return taskPlugin.renderWeekCellTask(context, record);
      } else {
        return <></>;
      }
    });

    return this.renderCell(events);
  }


  renderCell = (events?: any): React.ReactElement => {
    let { date } = this.props;
    const selectedDate = date;
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return (
      <div className="flex-vbox justify-content-between p-1 h-100" style={{
        overflow: 'hidden' // Prevent content from overflowing
      }}>
        <div className='flex-vbox'>
          <bs.GreedyScrollable className='flex-vbox flex-grow-1'>
            {events ? events : null}
          </bs.GreedyScrollable>
        </div>
        {
          selectedDate >= today ?
            <div className='flex-vbox flex-grow-0 justify-content-end align-items-end'>
              {this.renderAddButton(date)}
            </div>
            : null
        }
      </div>
    );
  }

  renderAddButton = (date: Date): React.ReactElement => {
    const { context } = this.props;
    let uiRoot = context.uiRoot as DbEntityTaskList;
    let { displayMode, plugin } = uiRoot.props;
    let actions = [];
    let pluginImpl = plugin as DbEntityUserTaskListPlugin;
    if (displayMode == 'custom' && pluginImpl.entityRefType) {
      let taskPlugin = DbEntityTaskCalendarPluginManger.getPlugin(pluginImpl.entityRefType);
      return (
        <div className="d-flex flex-end w-25">
          <div key={pluginImpl.entityRefType}
            className={`flex-hbox flex-center p-1 rounded-2 bg-primary-subtle text-primary w-100 btn cursor-pointer`}
            onClick={() => {
              taskPlugin.onAddTask(context, date);
            }}>
            <FeatherIcon.Plus size={12} className="me-1" />
            <span>Task</span>
          </div>
        </div>
      )
    } else {
      let pluginMap: Record<string, DbEntityTaskCalendarPlugin> = DbEntityTaskCalendarPluginManger.pluginMap;
      for (let pluginName in pluginMap) {
        let plugin = DbEntityTaskCalendarPluginManger.getPlugin(pluginName);
        if (plugin.showActions) {
          actions.push(
            <div key={pluginName}
              className={`btn d-flex flex-center px-2 py-1 rounded-2 bg-primary-subtle text-primary w-100 cursor-pointer`}
              onClick={() => {
                plugin.onAddTask(context, date);
              }}>
              <span>{plugin.label}</span>
            </div>
          )
        }
      }
      return (
        <bs.Popover className="d-flex flex-end w-25"
          title={T('New Task')} closeOnTrigger=".btn" >
          <bs.PopoverToggle
            className={`flex-hbox flex-center p-1 rounded-2 bg-primary-subtle text-primary w-100 `}>
            <FeatherIcon.Plus size={12} className="me-1" />
            <span>Task</span>
          </bs.PopoverToggle>

          <bs.PopoverContent>
            <div className='flex-vbox gap-2' style={{ width: '200px' }}>
              {actions}
            </div>
          </bs.PopoverContent>
        </bs.Popover>
      );
    }
  }
}


export class UserCalendarWeekCell extends CalendarWeekCell {
}

export class CompanyCalendarWeekCell extends CalendarWeekCell {
}
