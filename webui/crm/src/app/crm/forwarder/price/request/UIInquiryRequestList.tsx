import React from 'react';
import * as FeatherIcon from 'react-feather';
import { util, grid, entity, sql, bs, app, server, input } from '@datatp-ui/lib';
import { module } from '@datatp-ui/erp';
import { app as lgc_app } from '@datatp-ui/logistics'

import { BulkCargoInquiryStatusUtils, PricingRequestStatus, PricingRequestStatusUtils, T } from '../backend';
import { onMapEmail, onShowUIRequestPricing } from '../common/UIRequestPricing';
import { UIQuotationUtils } from '../../sales/quotation/QuotationUtils';
import {
  BULK_CARGO_INQUIRY_ENTITY_NAME,
  UIMailBulkCargoRequestPricing
} from './UIBulkCargoInquiryRequest';
import { buildTooltipValues, WRateFinderGridFilter } from '../common';

import _settings = lgc_app.logistics.settings;
import mapToTypeOfShipment = _settings.mapToTypeOfShipment;
import TransportationMode = _settings.TransportationMode;
import TransportationTool = _settings.TransportationTool;
import { ImportExportPurpose } from '../../model';

const SESSION = app.host.DATATP_HOST.session;

export class UIInquiryRequestReportPlugin extends entity.DbEntityListPlugin {

  constructor(space: 'User' | 'Company' | 'System') {
    super([]);

    this.backend = {
      context: 'company',
      service: 'TransportPriceMiscService',
      searchMethod: 'searchInquiryRequests',
    }

    let status = PricingRequestStatusUtils.getPricingRequestStatusList();
    let statusOpts = status.map(opt => opt.value);
    let statusOptLabels = status.map(opt => opt.label);

    this.searchParams = {
      params: {
        "space": space,
      },
      filters: [...sql.createSearchFilter()],
      rangeFilters: [
        ...sql.createDateTimeFilter("requestDate", T("Request Date")),
        ...sql.createDateTimeFilter("pricingDate", T("Pricing Date")),
      ],
      optionFilters: [
        {
          name: "status", "label": "Status", "type": "STRING", "required": true,
          options: statusOpts,
          optionLabels: statusOptLabels,
          multiple: true,
          selectOptions: statusOpts
        },
        sql.createStorageStateFilter(["ACTIVE", "ARCHIVED"])
      ],
      maxReturn: 1000
    }
  }

  withSearchPattern(pattern: string) {
    if (!this.searchParams) throw new Error("Need to config search params");
    const searchFilter = this.searchParams.filters?.find(sel => sel.name === 'search');
    if (searchFilter) {
      searchFilter.filterValue = pattern
    } else if (this.searchParams.filters) {
      this.searchParams.filters = [...sql.createSearchFilter(pattern)];
    }
    return this;
  }

  withRequestFilterMode(mode: string) {
    this.addSearchParam('filterMode', mode);
    return this;
  }

  withPricingAccountId(accountId: number) {
    this.addSearchParam('pricingAccountId', accountId);
    return this;
  }

  withSalemanAccountId(accountId: number) {
    this.addSearchParam('salemanAccountId', accountId);
    return this;
  }

  withCompanyCode(companyCode: string) {
    this.addSearchParam('companyCode', companyCode)
    return this;
  }

  withPurpose(purpose: ImportExportPurpose) {
    this.addSearchParam('groupType', purpose);
    return this;
  }

  withMode(mode: TransportationMode) {
    this.addSearchParam('mode', mode);
    return this;
  }

  updateStatusOptions(newOptions: string[]): UIInquiryRequestReportPlugin {
    this.searchParams?.optionFilters?.forEach(filter => {
      if (filter.name === "status" && filter.selectOptions) {
        filter.selectOptions = newOptions;
      }
    });
    return this;
  }

  getRangeFilter(): sql.RangeFilter {
    if (this.searchParams) {
      let filters = this.getSearchParams().rangeFilters;
      if (filters) {
        for (let i = 0; i < filters.length; i++) {
          let filter = filters[i];
          if (filter.name === 'requestDate') return filter;
        }
      }
      return sql.createDateTimeFilter('requestDate', 'Request Date')[0];
    }
    return sql.createDateTimeFilter('requestDate', 'Request Date')[0];
  }

  withPricingDate(fromValue: string, toValue: string) {
    let startOfFromDate: Date | null = null;
    let endOfToDate: Date | null = null;

    if ((fromValue || '').trim()) {
      let fromDate: Date = util.TimeUtil.parseCompactDateTimeFormat(fromValue);
      startOfFromDate = new Date(fromDate.getFullYear(), fromDate.getMonth(), fromDate.getDate());
    }

    if ((toValue || '').trim()) {
      let toDate: Date = util.TimeUtil.parseCompactDateTimeFormat(toValue);
      endOfToDate = new Date(toDate.getFullYear(), toDate.getMonth(), toDate.getDate(), 23, 59, 59, 999);
    }

    if (this.searchParams) {
      let rangeFilters = this.searchParams.rangeFilters;
      if (rangeFilters) {
        for (let i = 0; i < rangeFilters.length; i++) {
          let filter = rangeFilters[i];
          if (filter.name === 'pricingDate') {
            filter.fromValue = startOfFromDate ? util.TimeUtil.javaCompactDateTimeFormat(startOfFromDate) : fromValue;
            filter.toValue = endOfToDate ? util.TimeUtil.javaCompactDateTimeFormat(endOfToDate) : toValue;
            break;
          }
        }
      }
      this.searchParams.rangeFilters = rangeFilters;
    }
    return this;
  }

  withRequestDate(fromValue: string, toValue: string) {
    let startOfFromDate: Date | null = null;
    let endOfToDate: Date | null = null;

    if ((fromValue || '').trim()) {
      let fromDate: Date = util.TimeUtil.parseCompactDateTimeFormat(fromValue);
      startOfFromDate = new Date(fromDate.getFullYear(), fromDate.getMonth(), fromDate.getDate());
    }

    if ((toValue || '').trim()) {
      let toDate: Date = util.TimeUtil.parseCompactDateTimeFormat(toValue);
      endOfToDate = new Date(toDate.getFullYear(), toDate.getMonth(), toDate.getDate(), 23, 59, 59, 999);
    }

    if (this.searchParams) {
      let rangeFilters = this.searchParams.rangeFilters;
      if (rangeFilters) {
        for (let i = 0; i < rangeFilters.length; i++) {
          let filter = rangeFilters[i];
          if (filter.name === 'requestDate') {
            filter.fromValue = startOfFromDate ? util.TimeUtil.javaCompactDateTimeFormat(startOfFromDate) : fromValue;
            filter.toValue = endOfToDate ? util.TimeUtil.javaCompactDateTimeFormat(endOfToDate) : toValue;
            break;
          }
        }
      }
      this.searchParams.rangeFilters = rangeFilters;
    }
    return this;
  }

  withStatus(statuses: Array<typeof PricingRequestStatus[keyof typeof PricingRequestStatus]>) {
    if (this.searchParams) {
      let optionFilters = this.searchParams.optionFilters;
      if (optionFilters) {
        for (let i = 0; i < optionFilters.length; i++) {
          let filter = optionFilters[i];
          if (filter.name === 'status') {
            filter.selectOptions = statuses.map(status => status.value);
            break;
          }
        }
      }
      this.searchParams.optionFilters = optionFilters;
    }
    return this;
  }

  loadData(uiList: entity.DbEntityList<any>): void {

    const callback = (resp: server.BackendResponse) => {
      let records = resp.data;
      for (let request of records) {
        let jobTracking: any = typeof request['jobTrackingStatus'] === 'string'
          ? JSON.parse(request['jobTrackingStatus'])
          : (request['jobTrackingStatus'] || {});

        request['jobTrackingStatus'] = jobTracking;
      }

      uiList.onPostLoadData(records);
      this.update(records);
      uiList.markLoading(false);
      uiList.forceUpdate();
    }

    this.createBackendSearch(uiList, { params: this.searchParams }, callback).call();
  }

}

export class UIInquiryRequestSpacePlugin extends entity.DbEntityListPlugin {

  constructor(space: 'User' | 'Company' | 'System') {
    super([]);

    this.backend = {
      context: 'company',
      service: 'TransportPriceMiscService',
      searchMethod: 'searchInquiryRequestSpace',
    }

    let status = PricingRequestStatusUtils.getPricingRequestStatusList();

    let statusOpts = status.map(opt => opt.value);
    let statusOptLabels = status.map(opt => opt.label);

    this.searchParams = {
      params: {
        "space": space,
      },
      filters: [...sql.createSearchFilter()],
      rangeFilters: [
        ...sql.createDateTimeFilter("requestDate", T("Request Date")),
        ...sql.createDateTimeFilter("pricingDate", T("Pricing Date")),
      ],
      optionFilters: [
        {
          name: "status", "label": "Status", "type": "STRING", "required": true,
          options: statusOpts,
          optionLabels: statusOptLabels,
          multiple: true,
          selectOptions: statusOpts
        },
        sql.createStorageStateFilter(["ACTIVE", "ARCHIVED"])
      ],
      maxReturn: 1000
    }

  }

  withRequestIds(ids: Array<number>) {
    this.addSearchParam('requestIds', ids);
    return this;
  }

  loadData(uiList: entity.DbEntityList<any>): void {
    this.createBackendSearch(uiList, { params: this.searchParams }).call();
  }

}

export interface UIInquiryRequestListProps extends entity.DbEntityListProps {
  space: 'User' | 'Company' | 'System';
}
export class UIInquiryRequestList extends entity.DbEntityList<UIInquiryRequestListProps> {

  private checkRecordsPermission(records: any[]): { hasPermission: boolean, unauthorizedRecords: any[] } {
    const { space } = this.props;
    const moderatorCap = this.props.pageContext.hasUserModeratorCapability();
    const writeCap = this.props.pageContext.hasUserWriteCapability();
    const unauthorizedRecords = records.filter(record => {
      const salemanAccountId = record['salemanAccountId'];
      const hasWriteAccess = moderatorCap || (writeCap && (SESSION.getAccountId() === salemanAccountId) || space === 'User');
      return !hasWriteAccess;
    });

    if (unauthorizedRecords.length > 0) {
      bs.dialogShow(
        T("Permission Denied"),
        <div className="text-danger fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />
          {T("You don't have permission to modify some of the selected records")}
        </div>,
        { backdrop: 'static', size: 'md' }
      );
      return { hasPermission: false, unauthorizedRecords };
    }

    return { hasPermission: true, unauthorizedRecords: [] };
  }

  createVGridConfig(): grid.VGridConfig {
    const { pageContext, space } = this.props;
    // Build tooltip content from record fields
    const tooltipFields = [
      { key: 'note', label: 'Note' },
      { key: 'pickupAddress', label: 'Pickup Address' },
      { key: 'deliveryAddress', label: 'Delivery Address' },
      { key: 'descOfGoods', label: 'Desc Of Goods' },
      { key: 'mailTo', label: 'Mail To' },
      { key: 'mailSubject', label: 'Mail Subject' },
      { key: 'pricingNote', label: 'Pricing Note' },
    ];

    const renderTooltipAdvanced = (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
      const record = dRecord.record;
      const val = record[field.name] || 'N/A';

      let mailTo = (record['mailTo'] || '').split(",").map((item: string) => item.trim()).join("\n");
      record['mailTo'] = mailTo

      const { htmlFormat, textFormat } = buildTooltipValues(record, tooltipFields)

      const handleClick = () => {
        navigator.clipboard.writeText(textFormat);
        bs.toastShow('Copied to clipboard!', { type: 'success' });
      };

      return (
        <bs.CssTooltip width={400} position='bottom-right' offset={{ x: 380, y: 0 }}>
          <bs.CssTooltipToggle>
            <div className='flex-hbox' onClick={handleClick}>
              {field.fieldDataGetter ? field.fieldDataGetter(record) : field.format ? field.format(val) : val}
            </div>
          </bs.CssTooltipToggle>
          <bs.CssTooltipContent>
            {htmlFormat}
          </bs.CssTooltipContent>
        </bs.CssTooltip>
      );
    }

    let moderatorCap = pageContext.hasUserModeratorCapability();
    let writeCap = pageContext.hasUserWriteCapability();

    let control: any = undefined;
    if ((space === 'User' && writeCap) || moderatorCap) {
      control = {
        width: 30,
        items: [
          {
            name: 'resend', hint: 'Resend', icon: FeatherIcon.ExternalLink,
            onClick: (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
              let uiList = ctx.uiRoot as UIInquiryRequestList;
              uiList.onSelect(dRecord);
            },
          },
        ]
      }
    }

    let config: grid.VGridConfig = {
      record: {
        dataCellHeight: 40,
        editor: {
          supportViewMode: ['table'],
          enable: (space === 'User' && writeCap) || moderatorCap,
        },
        control: control,
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          { name: 'code', label: T(`Ref`), width: 120, container: 'fixed-left', filterable: true },
          {
            name: "salemanLabel", label: 'Saleman.', width: 180, filterable: true, state: { visible: space !== 'User' },
            customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let uiList = ctx.uiRoot as entity.DbEntityList
              const { appContext, pageContext } = uiList.props;

              let employeeName: string = record['salemanLabel'] || 'N/A';

              let parts = employeeName.trim().toLowerCase().split(' ');
              parts = parts.map(word => word.charAt(0).toUpperCase() + word.slice(1));
              if (parts.length > 3) {
                let initials = parts[0][0] + '.' + parts[1][0] + '.';
                employeeName = `${initials} ${parts.slice(2).join(' ')}`;
              }

              return (
                <div className='flex-hbox justify-content-center align-items-center' >
                  <module.account.WAvatars className='px-2'
                    appContext={appContext} pageContext={pageContext} avatarIds={[record['salemanAccountId']]}
                    avatarIdType='AccountId' width={20} height={20} borderRadius={10} />
                  <div className="flex-hbox">{employeeName}</div>
                </div>
              )
            },
          },
          {
            name: 'typeOfShipment', label: T(`Type of shpt`), width: 120, filterable: true, filterableType: 'string',
            fieldDataGetter(record: any) {
              return record['mode'] && record['purpose']
                ? `${mapToTypeOfShipment(record['purpose'], record['mode'])}`
                : 'Bulk Cargo';
            },
            customRender: renderTooltipAdvanced
          },
          {
            name: 'requestDate', label: T(`Request Date`), width: 150, format: util.text.formater.compactDate,
            filterable: true, filterableType: 'date', sortable: true,
            fieldDataGetter(record: any) {
              let val: string = record['requestDate']
              return `${val ? util.text.formater.compactDate(val) : ''}`
            },
            customRender: renderTooltipAdvanced
          },
          {
            name: 'termOfService', label: T(`Term.`), width: 90, filterable: true, filterableType: 'string',
            customRender: renderTooltipAdvanced
          },
          {
            name: 'fromLocationLabel', label: T('From Location'), width: 200, filterable: true, filterableType: 'string',
            customRender: renderTooltipAdvanced
          },
          {
            name: 'toLocationLabel', label: T('To Location'), width: 200, filterable: true, filterableType: 'string',
            customRender: renderTooltipAdvanced
          },
          {
            name: 'cargoReadyDate', label: T(`ETD/ CRD`), width: 120, format: util.text.formater.compactDate,
            fieldDataGetter(record: any) {
              let val: string = record['cargoReadyDate']
              return `${val ? util.text.formater.compactDate(val) : ''}`
            },
            customRender: renderTooltipAdvanced
          },
          {
            name: 'volumeInfo', label: T(`Vol`), width: 120,
            fieldDataGetter(record: any) {
              let val: string = record['volumeInfo']
              if (val) return val;
              let mode: TransportationMode = record['mode']
              if (TransportationTool.isAir(mode)) return record['grossWeightKg'] + ' KGS'
              if (TransportationTool.isSeaLCL(mode)) return record['volumeCbm'] + ' CBM'
            },
            customRender: renderTooltipAdvanced
          },
          {
            name: 'reportVolume', label: T(`Total Vol`), width: 120,
            fieldDataGetter(record: any) {
              let val: string = record['reportVolume']
              if (val) val += ` (${record['reportVolumeUnit']})`
              return val;
            },
            customRender: renderTooltipAdvanced
          },
          {
            name: 'feedback', label: T(`Feedback`), width: 270, style: { height: 40 },
            editor: {
              type: 'string',
              enable: (space === 'User' && writeCap) || moderatorCap,
              onInputChange: (ctx: grid.FieldContext, oldVal: any, newVal: any) => {
                let record: any = ctx.displayRecord.record;
                if (newVal && newVal !== oldVal) {
                  let modified = {
                    id: record['id'],
                    feedback: newVal,
                    status: record['status'],
                    pricingAccountId: record['pricingAccountId'],
                    salemanAccountId: record['salemanAccountId'],
                    entityName: record['entityName']
                  };
                  this.saveChanges([modified])
                }
              },
            }
          },

          {
            name: "pricingLabel", label: 'Pricing By.', width: 130, filterable: true,
            fieldDataGetter(record) {
              return record['pricingLabel'] || 'N/A';
            },
            customRender: (_ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let employeeName: string = record['pricingLabel'] || 'N/A';
              let parts = employeeName.trim().toLowerCase().split(' ');
              parts = parts.map(word => word.charAt(0).toUpperCase() + word.slice(1));
              if (parts.length >= 3) {
                let initials = parts[0][0] + '.' + parts[1][0] + '.';
                employeeName = `${initials} ${parts.slice(2).join(' ')}`;
              }

              return (
                <div className="flex-hbox">{employeeName}</div>
              )
            },
          },
          {
            name: 'pricingDate', label: T(`Pricing Date`), width: 150, sortable: true,
            filterable: true, filterableType: 'date',
            fieldDataGetter(record: any) {
              let val: string = record['pricingDate']
              return `${val ? util.text.formater.compactDate(val) : ''}`
            },
            customRender: renderTooltipAdvanced
          },
          { name: 'pricingNote', label: T(`Pricing Note`), width: 270 },
          { name: 'createdBy', label: T(`Created By`), width: 120 },
          {
            name: 'status', label: T('Status'), width: 120,
            filterable: true, container: 'fixed-right',
            customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let uiList = ctx.uiRoot as UIInquiryRequestList
              let entityName = record['entityName'];
              if (entityName === BULK_CARGO_INQUIRY_ENTITY_NAME) {
                return uiList.buildBulkCargoStatus(record);
              }
              let currentStatus = PricingRequestStatusUtils.getStatusInfo(record['status']);
              let StatusIcon = currentStatus.icon;
              let label = currentStatus.label;
              let color = currentStatus.color;

              const statusList = PricingRequestStatusUtils.getPricingRequestStatusList();
              const statusRemaining = statusList.filter(status =>
                status.value !== record['status'] &&
                status.value !== 'IN_PROGRESS' &&
                status.value !== 'DONE'
              );

              return (
                <bs.Popover className="d-flex flex-center w-100 h-100"
                  title={T('Status')} closeOnTrigger=".btn" >
                  <bs.PopoverToggle
                    className={`flex-hbox flex-center px-2 py-2 rounded-2 bg-${color}-subtle text-${color} w-100 `}>
                    <StatusIcon size={14} className="me-1" />
                    <span>{label}</span>
                  </bs.PopoverToggle>
                  <bs.PopoverContent>
                    <div className='flex-vbox gap-2' style={{ width: '200px' }}>
                      {statusRemaining.map(opt => {
                        let OptIcon = opt.icon;
                        return (
                          <div key={opt.value}
                            className={`d-flex flex-center px-2 py-1 rounded-2 bg-${opt.color}-subtle text-${opt.color} w-100 cursor-pointer`}
                            onClick={() => uiList.handleOnChangeStatus(opt.value, [
                              {
                                status: record['status'], feedback: record['feedback'], id: record['id'],
                                pricingAccountId: record['pricingAccountId'],
                                salemanAccountId: record['salemanAccountId']
                              }])
                            }>
                            <OptIcon size={14} className="me-1" />
                            <span>{opt.label}</span>
                          </div>
                        );
                      })}
                    </div>
                  </bs.PopoverContent>
                </bs.Popover>
              );
            }
          },
        ],
      },
      toolbar: {
        hide: true,
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          }
        }
      },
    }

    let fields: grid.FieldConfig[] = config.record.fields || [];

    const totalWidth = window.innerWidth || 1920;
    const totalControlWidth = config.record.control ? config.record.control.width : 0;
    let totalWidthRemaining = totalWidth - totalControlWidth - fields.reduce((sum, field) => {
      const isVisible =
        !field.state ||
        !field.state.hasOwnProperty('visible') ||
        field.state.visible === true;
      return isVisible ? sum + (field.width || 0) : sum;
    }, 0);

    if (totalWidthRemaining > 0) {
      fields[fields.length - 1].container ? fields[fields.length - 1].container = 'default' : null;
    }

    return config;
  }

  onDeleteAction() {
    const { appContext } = this.props;
    const selectedRecords = this.vgridContext.model.getSelectedRecords();
    if (selectedRecords.length == 0) {
      appContext.addOSNotification('warning', T("No records were selected"));
      return;
    }
    const selectedInquiryRequestIds: number[] = [];
    const selectedBulkCargoInquiryRequestIds: number[] = [];
    for (let rec of selectedRecords) {
      if (rec.entityName === BULK_CARGO_INQUIRY_ENTITY_NAME) {
        selectedBulkCargoInquiryRequestIds.push(rec.id);
      } else {
        selectedInquiryRequestIds.push(rec.id);
      }
    }

    const onConfirmDelete = () => {
      if (selectedInquiryRequestIds.length > 0) {
        appContext.createHttpBackendCall('TransportPriceMiscService', 'deleteInquiryRequests', { ids: selectedInquiryRequestIds })
          .withSuccessData((_data: any) => {
            appContext.addOSNotification('success', T('Delete Inquiry Request Success'))
            this.vgridContext.model.removeSelectedDisplayRecords();
            this.getVGridContext().getVGrid().forceUpdateView();
          })
          .call();
      }
      if (selectedBulkCargoInquiryRequestIds.length > 0) {
        appContext.createHttpBackendCall('TransportPriceMiscService', 'deleteBulkCargoInquiryRequests', { ids: selectedBulkCargoInquiryRequestIds })
          .withSuccessData((_data: any) => {
            appContext.addOSNotification('success', T('Delete Bulk Cargo Inquiry Request Success'))
            this.vgridContext.model.removeSelectedDisplayRecords();
            this.getVGridContext().getVGrid().forceUpdateView();
          })
          .call();
      }
    }
    let messageEle = (<div className="text-danger">Do you want to delete these records?</div>)
    bs.dialogConfirmMessage(T("Confirm Delete"), messageEle, onConfirmDelete);
  }

  onDefaultSelect(_dRecord: grid.DisplayRecord): void {
    const { appContext } = this.props;
    let entity: any = _dRecord.record;
    let entityName = entity['entityName'];

    if (entityName == BULK_CARGO_INQUIRY_ENTITY_NAME) {
      this.onLoadBulkCargoInquiryRequest(entity['id']);
      return;
    }
    const onPostCommit = (_entity: any) => {
      this.nextViewId();
      this.reloadData();
    }
    appContext
      .createHttpBackendCall('TransportPriceMiscService', 'getInquiryRequest', { 'requestId': entity['id'] })
      .withSuccessData((request: any) => {
        let newRequest: any = {
          ...request,
          id: undefined,
          code: undefined,
          mailSubject: undefined,
          mailMessage: undefined,
          pricingDate: undefined,
          pricingAccountId: undefined,
          pricingLabel: undefined,
          status: 'IN_PROGRESS',
          feedback: '',
          totalNewPricesCount: 0,
          totalAnalysisPricesCount: 0,
          stepTracking: 0,
          totalStepCounting: 0,
          pricingNote: undefined,
          laydaysDate: util.TimeUtil.javaCompactDateTimeFormat(new Date()),
          requestDate: util.TimeUtil.javaCompactDateTimeFormat(new Date())
        }
        newRequest = onMapEmail(newRequest);
        onShowUIRequestPricing(this, newRequest, onPostCommit);
      })
      .call();
  }

  onCreateQuote() {
    const { appContext } = this.props;
    const findCargoInquiry = this.vgridContext.model.getSelectedRecords().find(sel => sel.entityName === BULK_CARGO_INQUIRY_ENTITY_NAME);
    if (findCargoInquiry) {
      appContext.addOSNotification('warning', T('There are some records that are not inquiry requests!!!'));
      return;
    }
    const selectedRecordIds = this.vgridContext.model.getSelectedRecordIds();
    const selectedRecordId = selectedRecordIds[0];
    if (selectedRecordIds.length === 0) {
      appContext.addOSNotification('warning', T('No records were selected'));
      return;
    }
    appContext.createHttpBackendCall('QuotationService', 'createSpecificQuotation', { inquiryRequestId: selectedRecordId })
      .withSuccessData((data: any) => {
        appContext.addOSNotification('success', "Create quotation success!!!");
        UIQuotationUtils.showUISpecificQuotation(this, data);
      })
      .withFail((response: server.BackendResponse) => {
        let title = T('Create Quotation Failed!');
        appContext.addOSNotification('danger', title, response.error, response);
        let message = response.error.message;
        if (message) {
          bs.notificationShow("danger", title, <div className='text-danger'>{message}</div>);
        }
        return;
      })
      .call()
  }

  onLoadBulkCargoInquiryRequest = (inquiryId?: number) => {
    let { appContext } = this.props;

    if (!inquiryId) {
      let request = {
        cargoType: 'BULK',
        unit: 'TNE'
      }
      appContext.createHttpBackendCall('TransportPriceMiscService', 'initBulkCargoInquiryRequest', { request: request })
        .withSuccessData((data: any) => {
          let request: any = onMapEmail(data);
          this.onShowBulkCargoInquiryRequest(request);
        })
        .withFail(() => {
          let request: any = onMapEmail({});
          this.onShowBulkCargoInquiryRequest(request);
        })
        .call();
      return;
    }

    appContext
      .createHttpBackendCall('TransportPriceMiscService', 'getBulkCargoInquiryRequest', { 'requestId': inquiryId })
      .withSuccessData((request: any) => {
        let newRequest: any = {
          ...request,
          status: 'IN_PROGRESS',
          id: undefined,
          code: undefined,
          mailSubject: undefined,
          mailMessage: undefined,
          pricingDate: undefined,
          pricingAccountId: undefined,
          pricingLabel: undefined,
          feedback: '',
          pricingNote: undefined,
          shipmentDetail: {
            ...request.shipmentDetail,
            laydaysDate: util.TimeUtil.javaCompactDateTimeFormat(new Date()),
          },
          requestDate: util.TimeUtil.javaCompactDateTimeFormat(new Date())
        }
        newRequest = onMapEmail(newRequest);
        this.onShowBulkCargoInquiryRequest(newRequest);
      })
      .call();
  }

  onShowBulkCargoInquiryRequest = (record: any) => {
    const { pageContext } = this.props;
    let fullName = SESSION.getAccountAcl().getFullName();
    let companyLabel = SESSION.getAccountAcl().getCompanyAcl().companyLabel;
    let title = `Bulk Cargo Request (${fullName} - ${companyLabel})`;

    const createContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <UIMailBulkCargoRequestPricing appContext={appCtx} pageContext={pageCtx}
          observer={new entity.ComplexBeanObserver(record)} onPostCommit={(entity: any) => {
            pageCtx.back();
            this.nextViewId();
            this.reloadData();
          }} />
      )
    }
    pageContext.createPopupPage('create-cargo-inquiry-request', T(title), createContent, { size: 'flex-lg' })

  }

  saveChanges = (modified: any[]) => {
    let { appContext } = this.props;
    const { hasPermission } = this.checkRecordsPermission(modified);
    if (!hasPermission) return;

    let inquiryRequests: Array<any> = [];
    let bulkCargoInquiryRequests: Array<any> = [];
    modified.forEach(rec => {
      if (rec['entityName'] === BULK_CARGO_INQUIRY_ENTITY_NAME) {
        bulkCargoInquiryRequests.push(rec);
      } else {
        inquiryRequests.push(rec);
      }
    });

    const backendCall = (component: string, endpoint: string, requests: Array<any>) => {
      appContext.createHttpBackendCall(component, endpoint, { records: requests })
        .withSuccessData((data: any) => {
          appContext.addOSNotification("success", T(`Auto save modified records success!!!`));
          this.vgridContext.getVGrid().forceUpdateView();
        })
        .call();
    }
    if (inquiryRequests.length > 0) {
      backendCall('TransportPriceMiscService', 'saveInquiryRequestRecords', inquiryRequests);
    }
    if (bulkCargoInquiryRequests.length > 0) {
      backendCall('TransportPriceMiscService', 'saveBulkCargoInquiryRequestRecords', bulkCargoInquiryRequests);
    }

  }

  handleOnChangeStatus(status: string, requests: any[] = []) {
    if (!requests || requests.length === 0) return;

    const { hasPermission } = this.checkRecordsPermission(requests);
    if (!hasPermission) return;

    // Check if feedback is required for the new status
    if (status !== 'SUCCESS' && status !== 'NO_RESPONSE' && status !== 'DONE') {
      const recordsWithoutFeedback = requests.filter(record => !record.feedback);

      if (recordsWithoutFeedback.length > 0) {
        bs.dialogShow('Warning',
          <div className="text-warning fw-bold text-center py-3 border-bottom">
            <FeatherIcon.AlertCircle className="mx-2" />
            Please provide feedback before changing status
          </div>,
          { backdrop: 'static', size: 'md' }
        );
        return;
      }
    }

    let requestIds: number[] = requests.map(sel => sel['id']);
    let records = this.vgridContext.model.getRecords();
    let modified: any[] = [];

    // Map through records to update both status and status
    for (let record of records) {
      if (requestIds.includes(record['id']) && record['entityName'] !== BULK_CARGO_INQUIRY_ENTITY_NAME) {

        modified.push({
          id: record['id'],
          feedback: record['feedback'],
          pricingAccountId: record['pricingAccountId'],
          pricingDate: record['pricingDate'],
          pricingLabel: record['pricingLabel'],
          pricingNote: record['pricingNote'],
          salemanAccountId: record['salemanAccountId'],
          salemanLabel: record['salemanLabel'],
          status: status,
        });

        // Optimistic update
        record['status'] = status;
        grid.getRecordState(record).markModified();
      }
    }
    this.saveChanges(modified);
  }

  buildBulkCargoStatus(record: any) {
    let currentStatus = BulkCargoInquiryStatusUtils.getStatusInfo(record['status']);
    let StatusIcon = currentStatus.icon;
    let label = currentStatus.label;
    let color = currentStatus.color;
    const statusRemaining = BulkCargoInquiryStatusUtils.getStatus().filter(status =>
      status.value !== record['status'] &&
      status.value !== 'IN_PROGRESS'
    );

    const handleOnChangeStatus = (status: string, requests: any[] = []) => {
      if (!requests || requests.length === 0) return;
      const { hasPermission } = this.checkRecordsPermission(requests);
      if (!hasPermission) return;
      if (status !== 'FIXED' && status !== 'NO_RESPONSE') {
        const recordsWithoutFeedback = requests.filter(record => !record.feedback);
        if (recordsWithoutFeedback.length > 0) {
          bs.dialogShow('Warning',
            <div className="text-warning fw-bold text-center py-3 border-bottom">
              <FeatherIcon.AlertCircle className="mx-2" />
              Please provide feedback before changing status
            </div>,
            { backdrop: 'static', size: 'md' }
          );
          return;
        }
      }

      let requestIds: number[] = requests.map(sel => sel['id']);
      let records = this.vgridContext.model.getRecords();
      let modified: any[] = [];
      // Map through records to update both status and status
      for (let record of records) {
        if (requestIds.includes(record['id']) && record['entityName'] == BULK_CARGO_INQUIRY_ENTITY_NAME) {
          modified.push({
            id: record['id'],
            feedback: record['feedback'],
            status: status,
            entityName: record['entityName']
          });
          record['status'] = status;
          grid.getRecordState(record).markModified();
        }
      }
      this.saveChanges(modified);
    }

    return (
      <bs.Popover className="d-flex flex-center w-100 h-100"
        title={T('Status')} closeOnTrigger=".btn" >
        <bs.PopoverToggle
          className={`flex-hbox flex-center px-2 py-2 rounded-2 bg-${color}-subtle text-${color} w-100 `}>
          <StatusIcon size={14} className="me-1" />
          <span>{label}</span>
        </bs.PopoverToggle>
        <bs.PopoverContent>
          <div className='flex-vbox gap-2' style={{ width: '200px' }}>
            {statusRemaining.map(opt => {
              let OptIcon = opt.icon;
              return (
                <div key={opt.value}
                  className={`d-flex flex-center px-2 py-1 rounded-2 bg-${opt.color}-subtle text-${opt.color} w-100 cursor-pointer`}
                  onClick={() => handleOnChangeStatus(opt.value, [
                    {
                      id: record['id'],
                      status: record['status'],
                      feedback: record['feedback'],
                      pricingAccountId: record['pricingAccountId'],
                      salemanAccountId: record['salemanAccountId'],
                      entityName: record['entityName'],
                    }])
                  }>
                  <OptIcon size={14} className="me-1" />
                  <span>{opt.label}</span>
                </div>
              );
            })}
          </div>
        </bs.PopoverContent>
      </bs.Popover>
    )
  }

  onModify = (_bean: any, _field: string, _oldVal: any, _newVal: any): void => {
    const { plugin } = this.props;
    let searchParam = plugin.getSearchParams();
    if (_field === 'maxReturn') {
      searchParam.maxReturn = _newVal || 5000;
    } else {
      searchParam.params = _bean;
    }
    plugin.searchParams = searchParam;
    this.reloadData();
    this.nextViewId();
    // this.forceUpdate();
    this.vgridContext.getVGrid().forceUpdateView();
  };

  render() {
    if (this.isLoading()) return this.renderLoading();
    const { plugin, appContext, pageContext } = this.props;
    let searchParam = plugin.getSearchParams();
    let writeCap = pageContext.hasUserWriteCapability();
    let moderatorCap = pageContext.hasUserModeratorCapability();

    return (
      <div className='flex-vbox'>
        <div className='bg-white flex-hbox flex-grow-0 justify-content-between align-items-center mx-1 px-1 mt-1'>

          <div className='flex-hbox justify-content-start align-items-center'>
            <div className='me-2'>
              <input.BBSelectField className="fw-bold text-primary border-bottom text-center"
                style={{ width: 150 }} bean={searchParam} field={"maxReturn"}
                options={[1000, 2000, 5000, 10000]}
                optionLabels={['Show 1000 records', 'Show 2000 records', 'Show 5000 records', 'Show 10.000 records']}
                onInputChange={this.onModify} />
            </div>
            <WRateFinderGridFilter context={this.vgridContext} />
          </div>
          <div className="flex-hbox justify-content-end align-items-center flex-grow-1">

            <bs.Button laf='info' className="border-0 p-1 mx-1 border-end rounded-0" outline
              onClick={() => this.onLoadBulkCargoInquiryRequest()} hidden={bs.ScreenUtil.isMobileScreen() || !writeCap}>
              <FeatherIcon.Plus size={12} /> Bulk Cargo
            </bs.Button>

            {/* <bs.Button laf='info' className="border-0 p-1 mx-1 border-end rounded-0" outline
              onClick={() => this.onCreateQuote()} hidden={bs.ScreenUtil.isMobileScreen() || !writeCap}>
              <FeatherIcon.Plus size={12} /> Create a Quote
            </bs.Button> */}

            {/* <entity.XlsxExportButton
              context={this.vgridContext}
              appContext={appContext}
              pageContext={pageContext}
              className="border-0 p-1 mx-1 border-end rounded-0"
              laf="info" outline
              options={{
                fileName: `Request${util.TimeUtil.toCompactDateFormat(new Date())}.xlsx`,
                modelName: `Request${util.TimeUtil.toDateIdFormat(new Date())}`
              }}
            /> */}

            {/* <bs.Button laf='warning' className="border-0 p-1 mx-1 border-end rounded-0" outline
              onClick={() => this.onDeleteAction()} hidden={bs.ScreenUtil.isMobileScreen() || !moderatorCap}>
              <FeatherIcon.Trash size={12} /> Del
            </bs.Button> */}

          </div>

        </div>
        <div key={this.viewId} className='flex-vbox'>
          {this.renderUIGrid()}
        </div>
      </div>
    )
  }

}

