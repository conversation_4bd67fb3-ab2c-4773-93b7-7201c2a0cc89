import React from 'react'
import * as FeatherIcon from "react-feather";
import { bs, input, entity, app } from '@datatp-ui/lib';
import { module } from '@datatp-ui/erp';

import { T } from '../price';
import { PartnerType } from './BBRefBFSOneCustomer';

import BBRefLocation = module.settings.BBRefLocation;

//TODO: Dan - more to resource entity
const SOURCE_OPTIONS: string[] = [
  "WCA", "WPA", "GFFG", "CLC Projects",
  "A2B (not a member of associations in this list, Agent has one-way nominations to Bee)",
  "B2A (not a member of associations in this list, <PERSON> has one-way nominations to Agent)",
  "A2B2A (not a member of associations in this list, Agent and Bee have reciprocal nominations)",
  "FREEHAND", "BEE", "MLN (Millenium Logistics Network)",
  "FREIGHT MIDPOINT", "LOGNET", "GAA", "PCN", "CFN",
  "EAA", "DCS", "ISS", "FCUBE", "TALA",
  "FPS - FAMOUS PACIFIC SHIPPING", "JOINT SALES", "PANGEA NETWORK"
]
const SESSION = app.host.DATATP_HOST.session;
import BBRefCountry = module.settings.BBRefCountry;
import BBRefState = module.settings.BBRefState;
import { UIPartnerObligationList, UIPartnerObligationPlugin } from '../sales/partners/UIPartnerObligationList';
import { UIPartnerEventHistoryList, UIPartnerEventHistoryListPlugin } from '../sales/leads/UIPartnerEventHistoryList';



interface UIBFSOnePartnerEditorProps extends entity.AppComplexEntityEditorProps {
  partnerType: any
}
export class UIBFSOnePartnerForm extends entity.AppDbComplexEntityEditor<UIBFSOnePartnerEditorProps> {

  requestToBFSOne = () => {
    const { appContext, observer } = this.props;
    let partner = observer.getMutableBean();

    if (!partner['label'] || !partner['localizedLabel']) {
      bs.dialogShow('Error',
        <div className="text-danger fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />{T('Please enter both Label.')}
        </div>,
        { backdrop: 'static', size: 'sm' }
      );
      return;
    }

    if (!partner['address'] || !partner['localizedAddress']) {
      bs.dialogShow('Error',
        <div className="text-danger fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />{T('Please enter both Address..')}
        </div>,
        { backdrop: 'static', size: 'sm' }
      );
      return;
    }

    if (!partner['countryId']) {
      bs.dialogShow('Error',
        <div className="text-danger fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />{T('Please select a Country.')}
        </div>,
        { backdrop: 'static', size: 'sm' }
      );
      return;
    }

    if (!partner['provinceId']) {
      bs.dialogShow('Error',
        <div className="text-danger fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />{T('Please select a Province.')}
        </div>,
        { backdrop: 'static', size: 'sm' }
      );
      return;
    }

    if (!partner['cell']) {
      bs.dialogShow('Error',
        <div className="text-danger fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />{T('Please enter Cell phone.')}
        </div>,
        { backdrop: 'static', size: 'sm' }
      );
      return;
    }

    if (!partner['email']) {
      bs.dialogShow('Error',
        <div className="text-danger fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />{T('Please enter Email.')}
        </div>,
        { backdrop: 'static', size: 'sm' }
      );
      return;
    }

    if (!partner['industryCode']) {
      bs.dialogShow('Error',
        <div className="text-danger fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />{T('Please select an Industry.')}
        </div>,
        { backdrop: 'static', size: 'sm' }
      );
      return;
    }

    let id = observer.getBeanProperty('id', null);
    let param: any = { fwdPartnerId: id };
    let apiCommit: { service: string, commitMethod: string } = {
      service: 'BFSOneCRMService', commitMethod: 'requestToPartner'
    }
    if (observer.isNewBean()) {
      param = { bfsOnePartner: observer.getMutableBean() };
      apiCommit = {
        service: 'CustomerLeadsService', commitMethod: 'createAndRequestBFSOnePartner'
      }
    }

    appContext.createHttpBackendCall(apiCommit.service, apiCommit.commitMethod, param)
      .withSuccessData((forwarderPartner: any) => {
        observer.setMutableBean(forwarderPartner)
        this.forceUpdate();

        appContext.addOSNotification("success", T(`Create BFSOne Partner Success!`));
        if (!forwarderPartner.bfsonePartnerCode) {
          appContext.addOSNotification("danger", T(`Request To BFSOne Fail!`));
        }
      })
      .withEntityOpNotification('commit', "Request to BFSOne")
      .call();
  }

  onPreCommit = () => {
    const { observer } = this.props;
    let partner = observer.getMutableBean();

    if (!partner['label'] || !partner['localizedLabel']) {
      bs.dialogShow('Error',
        <div className="text-danger fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />{T('Please enter both Label.')}
        </div>,
        { backdrop: 'static', size: 'sm' }
      );
      throw new Error('Please enter both Label (English) and Label (Localized).');
    }

    if (!partner['address'] || !partner['localizedAddress']) {
      bs.dialogShow('Error',
        <div className="text-danger fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />{T('Please enter both Address..')}
        </div>,
        { backdrop: 'static', size: 'sm' }
      );
      throw new Error('Please enter both Address (English) and Address (Localized).');
    }

    if (!partner['countryId']) {
      bs.dialogShow('Error',
        <div className="text-danger fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />{T('Please select a Country.')}
        </div>,
        { backdrop: 'static', size: 'sm' }
      );
      throw new Error('Please select a Country.');
    }

    if (!partner['provinceId']) {
      bs.dialogShow('Error',
        <div className="text-danger fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />{T('Please select a Province.')}
        </div>,
        { backdrop: 'static', size: 'sm' }
      );
      throw new Error('Please select a Province.');
    }

    if (!partner['industryCode']) {
      bs.dialogShow('Error',
        <div className="text-danger fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />{T('Please select an Industry.')}
        </div>,
        { backdrop: 'static', size: 'sm' }
      );
      throw new Error('Please select an Industry.');
    }
  }

  render() {
    let { appContext, pageContext, observer, partnerType } = this.props;
    let partner = observer.getMutableBean();
    const writeCap = pageContext.hasUserWriteCapability();
    let title = partnerType ? partnerType : 'Customer Lead';

    let apiCommit: { service: string, commitMethod: string } = {
      service: 'CustomerLeadsService', commitMethod: 'createBFSOnePartner'
    }
    if (!observer.isNewBean()) {
      apiCommit = {
        service: 'BFSOnePartnerService', commitMethod: 'saveBFSOnePartner'
      }
    }
    return (
      <div className="flex-vbox">
        <bs.Row>
          <bs.Col span={6}>
            <input.BBStringField
              bean={partner} field='label' label={T("Label (English)")} disable={!writeCap} required />
          </bs.Col>

          <bs.Col span={6}>
            <input.BBStringField
              bean={partner} field='localizedLabel' label={T("Label (Localized)")} disable={!writeCap} required />
          </bs.Col>
        </bs.Row>

        <bs.Row>
          <bs.Col span={3}>
            <BBRefLocation required label='KCN'
              appContext={appContext} pageContext={pageContext} bean={partner}
              beanIdField={'kcnCode'} beanLabelField={'kcnLabel'} placeholder='KCN' hideMoreInfo
              disable={!writeCap} inputObserver={observer} refLocationBy='id' locationTypes={['KCN']}
              beanRefLabelField='label'
              onPostUpdate={(_inputUI, bean, selectOpt, _userInput) => {
                bean['countryId'] = selectOpt['countryId'];
                bean['countryLabel'] = selectOpt['countryLabel'];
                bean['provinceId'] = selectOpt['stateId'];
                bean['provinceLabel'] = selectOpt['stateLabel'];
                this.forceUpdate();
              }} />
          </bs.Col>
          <bs.Col span={3}>
            <BBRefCountry key={partner.kcnCode}
              appContext={appContext} pageContext={pageContext}
              placement="bottom-start" offset={[0, 5]} minWidth={350}
              disable={!writeCap} label={T('Country')} placeholder="Enter Country"
              required bean={partner} beanIdField={'countryId'} hideMoreInfo
              beanLabelField={'countryLabel'} refCountryBy='id'
              onPostUpdate={(_inputUI, bean, selectOpt, _userInput) => {
                bean['countryId'] = selectOpt['id'];
                bean['countryLabel'] = selectOpt['label'];
                this.forceUpdate();
              }}
            />
          </bs.Col>

          <bs.Col span={3}>
            <BBRefState key={partner.kcnCode}
              appContext={appContext} pageContext={pageContext}
              placement="bottom-start" offset={[0, 5]} minWidth={350}
              disable={!writeCap} label={T('Province')} placeholder="Enter Province"
              required bean={partner} beanIdField={'provinceId'} hideMoreInfo
              beanLabelField={'provinceLabel'} countryId={partner.countryId}
            />
          </bs.Col>
          <bs.Col span={3}>
            <input.BBStringField bean={partner} label={T('Investment Origin')} field="investmentOrigin" disable={!writeCap} />
          </bs.Col>
        </bs.Row>

        <bs.Row>
          <bs.Col span={6}>
            <input.BBTextField
              bean={partner} label={T('Address (English)')} field="address" disable={!writeCap} required
              style={{ height: '4em' }} />
          </bs.Col>
          <bs.Col span={6}>
            <input.BBTextField
              bean={partner} label={T('Address (Localized)')} field="localizedAddress" disable={!writeCap} required
              style={{ height: '4em' }} />
          </bs.Col>
        </bs.Row>

        <bs.Row>
          <bs.Col span={3}>
            <input.BBSelectField bean={partner} field="source" label={T('Source')}
              options={SOURCE_OPTIONS} disable={!writeCap} />
          </bs.Col>
          <bs.Col span={3}>
            <input.BBStringField
              bean={partner} field='swiftCode' label={T("Swift Code.")} disable={!writeCap} />
          </bs.Col>
          <bs.Col span={3}>
            <input.BBStringField
              bean={partner} field='personalContact' label={T("Personal Contact")} disable={!writeCap} />
          </bs.Col>
          <bs.Col span={3}>
            <input.BBStringField
              bean={partner} field='cell' label={T("Cell Phone")} disable={!writeCap} />
          </bs.Col>
        </bs.Row>

        <bs.Row>
          <bs.Col span={6}>
            <input.BBStringField
              bean={partner} field='fax' label={T("FAX.")} disable={!writeCap} />
          </bs.Col>

          <bs.Col span={6}>
            <input.BBStringField
              bean={partner} field='email' label={T("Email")} disable={!writeCap} />
          </bs.Col>
        </bs.Row>

        <bs.Row>
          <bs.Col span={6}>
            <input.BBStringField
              bean={partner} field='bankName' label={T("Bank Name.")} disable={!writeCap} />
          </bs.Col>

          <bs.Col span={6}>
            <input.BBStringField
              bean={partner} field='bankAddress' label={T("Bank Address.")} disable={!writeCap} />
          </bs.Col>
        </bs.Row>
        <bs.Col span={12}>
          <module.resource.BBRefResource
            appContext={appContext} pageContext={pageContext}
            placement="bottom-start" offset={[0, 5]} minWidth={350}
            disable={!writeCap} label={T('Industry')} placeholder="Enter Industry"
            required bean={partner} beanIdField={'industryCode'} hideMoreInfo
            beanLabelField={'industryLabel'} resourceType={"industry"} refResourceBy="identifier" />
        </bs.Col>

        <bs.Row>
          <bs.Col span={6}>
            <input.BBStringField
              bean={partner} field='routing' label={T("Route")} disable={!writeCap} placeholder='Luồng tuyến...'
            />
          </bs.Col>
          <bs.Col span={6}>
            <input.BBSelectField
              bean={partner} field='scope' label={T("Location")} disable={!writeCap || !observer.isNewBean()}
              options={['Domestic', 'Overseas']}
            />
          </bs.Col>
        </bs.Row>

        <input.BBTextField
          bean={partner} label={T('Note')} field="note" disable={!writeCap}
          style={{ height: '4em' }} />

        <bs.Toolbar className='border'>

          <bs.Button laf='warning' hidden={!writeCap || !observer.isNewBean()} onClick={this.requestToBFSOne} className='me-1'>
            <FeatherIcon.Globe className='me-2' size={12} />
            Request To BFSOne
          </bs.Button>

          <entity.ButtonEntityCommit
            appContext={appContext} pageContext={pageContext}
            observer={observer} hide={!writeCap || observer.isNewBean()}
            commit={{
              entityLabel: T(title), context: 'company',
              service: apiCommit.service, commitMethod: apiCommit.commitMethod
            }}
            onPreCommit={this.onPreCommit}
            onPostCommit={this.onPostCommit}
          />
        </bs.Toolbar>
      </div>
    )
  }
}

export class UIBFSOnePartnerEditor extends entity.AppDbComplexEntityEditor {

  onPreCommit = () => {
    const { observer } = this.props;
    let partner = observer.getMutableBean();

    if (!partner['label'] || !partner['localizedLabel']) {
      bs.dialogShow('Error',
        <div className="text-danger fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />{T('Please enter both Label.')}
        </div>,
        { backdrop: 'static', size: 'sm' }
      );
      throw new Error('Please enter both Label (English) and Label (Localized).');
    }

    if (!partner['address'] || !partner['localizedAddress']) {
      bs.dialogShow('Error',
        <div className="text-danger fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />{T('Please enter both Address..')}
        </div>,
        { backdrop: 'static', size: 'sm' }
      );
      throw new Error('Please enter both Address (English) and Address (Localized).');
    }

    if (!partner['countryId']) {
      bs.dialogShow('Error',
        <div className="text-danger fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />{T('Please select a Country.')}
        </div>,
        { backdrop: 'static', size: 'sm' }
      );
      throw new Error('Please select a Country.');
    }

    if (!partner['provinceId']) {
      bs.dialogShow('Error',
        <div className="text-danger fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />{T('Please select a Province.')}
        </div>,
        { backdrop: 'static', size: 'sm' }
      );
      throw new Error('Please select a Province.');
    }

    if (!partner['industryCode']) {
      bs.dialogShow('Error',
        <div className="text-danger fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />{T('Please select an Industry.')}
        </div>,
        { backdrop: 'static', size: 'sm' }
      );
      throw new Error('Please select an Industry.');
    }
  }

  render() {
    let { appContext, pageContext, observer, readOnly } = this.props;
    let writeCap = pageContext.hasUserWriteCapability();
    writeCap = true
    return (
      <div key={this.viewId} className='flex-vbox'>
        <UIBFSOnePartnerForm appContext={appContext} pageContext={pageContext}
          observer={observer} readOnly={readOnly} partnerType={'Customer'} />
      </div>
    )
  }
}
export interface UIBFSOnePartnerProps extends entity.AppComplexEntityEditorProps {
  partnerType: PartnerType,
}

export class UIBFSOneCustomer extends entity.AppDbComplexEntityEditor<UIBFSOnePartnerProps> {
  categories: string[] = [];
  partnerContractStoreId: string = `U55KJP4QF6SVBVVRYV3YW4GOZSZAN6OGCMHXWLMZKHE33BUI2A7QZC2ITFC3YXFHPHAX2EOSWOJOSFEO5CUCJLA7BLJJWWRIIG22DNJXQMTH6J76JWSTCMRS6FISRFUDN3OWPCTLU4E4ZZIIF7EXT6HWU2SQXID7WIIEFDJQ675247DR7XIUBI2RUOHIF2WDVU62MFDGYNYOI6GBZJXQVUIPTNURZ3XUYQJPYYI=`;

  constructor(props: UIBFSOnePartnerProps) {
    super(props);

    const { partnerType } = this.props;
    if (partnerType === 'Customer') {
      this.categories = ['CUSTOMER', 'COLOADER', 'SHIPPER', 'CONSIGNEE']
    } else if (partnerType === 'Agent') {
      this.categories = ['AGENT_DOMESTIC', 'AGENT_OVERSEAS']
    } else if (partnerType === 'Coloader') {
      this.categories = ['TRUCKER', 'CARRIER', 'AIRLINE', 'FORWARDER', 'OTHER']
    } else {
      this.categories = ['OTHER']
    }
  }

  componentDidMount(): void {
    const { appContext, observer } = this.props;
    let partnerId = observer.getBeanProperty('id', null);
    appContext.createHttpBackendCall('PartnerReportService', 'getPartnerContactById', { partnerId: partnerId })
      .withSuccessData((data: any = {}) => {
        if (data.storeId) {
          this.partnerContractStoreId = data.storeId;
          this.forceUpdate();
        }

      })
      .withFail((error: any) => {
        console.error(error);
        return;
      })
      .call();
  }

  previewContractDocument = () => {
    let { appContext } = this.props;
    let url = appContext.getServerContext().createServerURL(`/storage/company/get-encrypt/${this.partnerContractStoreId}`, { download: true });
    let src = 'https://view.officeapps.live.com/op/embed.aspx?src=' + url;
    return (
      <bs.Tab name='partner-contracts' label={T('Documents')}>
        <iframe src={src} className='flex-vbox' style={{ fontSize: "0.85rem" }} />
      </bs.Tab>
    )
  }

  render(): React.ReactNode {
    let { appContext, pageContext, observer } = this.props;
    // let writeCap = pageContext.hasUserModeratorCapability() && !readOnly
    let writeCap = true
    let partner = observer.getMutableBean();
    const referencePartnerType = 'bfsone_partner';
    return (
      <div className='flex-vbox'>

        <div className="flex-vbox flex-grow-0 rounded bg-white px-2 py-1" style={{
          padding: '1rem',
          // border: '1px solid var(--phoenix-border-color)',
          // borderRadius: '0.5rem',
          background: 'linear-gradient(to right, #fcfcff, #ffffff)',
          // boxShadow: '0 0 10px rgba(0,0,0,0.02)'
        }}>

          <bs.Row>
            <bs.Col span={3}>
              <input.BBStringField
                bean={partner} field='bfsonePartnerCode' label={T("Partner No.")} disable />
            </bs.Col>
            <bs.Col span={3}>
              <input.BBStringField
                bean={partner} field='taxCode' label={T("Tax code.")} disable />
            </bs.Col>
            <bs.Col span={6}>
              <input.BBStringField bean={partner} field='name' label={T("Name")} disable={!writeCap} />
            </bs.Col>
          </bs.Row>

        </div>
        <div className='flex-vbox' style={{ flex: 1, overflow: 'auto' }} key={this.viewId}>
          <bs.TabPane>

            <bs.Tab name='partner-detail' label={T('Detail')} active>
              <UIBFSOnePartnerEditor appContext={appContext} pageContext={pageContext}
                observer={observer} readOnly={!writeCap} />
            </bs.Tab>

            <bs.Tab name='partner-history' label={T('Event History')}>
              <UIPartnerEventHistoryList
                appContext={appContext} pageContext={pageContext}
                plugin={new UIPartnerEventHistoryListPlugin().withReferencePartner(partner.id, referencePartnerType)}
                referencePartnerId={partner.id} referencePartnerType={referencePartnerType}
              />
            </bs.Tab>

            <bs.Tab name='shipping-instruction' label={T('Shipping Instruction')}>
              <div>TODO</div>
            </bs.Tab>

            <bs.Tab name='partner-report' label={T('Reports')}>
              <div>TODO</div>
            </bs.Tab>

            <bs.Tab name='partner-obligation' label={T('Obligation')}>
              <UIPartnerObligationList appContext={appContext} pageContext={pageContext}
                plugin={new UIPartnerObligationPlugin().withBFSOnePartnerCode(partner.bfsonePartnerCode)}
                bfsonePartnerCode={partner.bfsonePartnerCode} partnerName={partner.name}
              />
            </bs.Tab>
            {this.previewContractDocument()}
          </bs.TabPane>
        </div>
      </div>
    )
  }
}

export class UINewBFSOnePartnerEditor extends entity.AppDbComplexEntityEditor<UIBFSOnePartnerProps> {
  categories: string[] = [];

  constructor(props: UIBFSOnePartnerProps) {
    super(props);

    const { partnerType } = this.props;
    if (partnerType === 'Customer') {
      this.categories = ['CUSTOMER', 'CO-LOADER', 'SHIPPER', 'CONSIGNEE']
    } else if (partnerType === 'Agent') {
      this.categories = ['AGENT_DOMESTIC', 'AGENT_OVERSEAS']
    } else if (partnerType === 'Coloader') {
      this.categories = ['TRUCKER', 'CARRIER', 'AIRLINE', 'FORWARDER', 'OTHER']
    } else {
      this.categories = ['OTHER']
    }
  }

  onCheckTaxCode = (_wInput: input.WInput, _bean: any, _field: string, _oldVal: any, newVal: any) => {
    const { appContext } = this.props;
    appContext.createHttpBackendCall('BFSOnePartnerService', 'findByTaxCode', { taxCode: newVal })
      .withSuccessData((partnerList: any[]) => {
        if (partnerList.length > 0) {
          let message = (
            <div className="ms-1 text-warning py-3 border-bottom">
              A partner with the tax code "{newVal}" already exists in the system.
            </div>
          );
          bs.dialogShow('Invalid Tax Code', message, { backdrop: 'static', size: 'sm' });
        }
      })

      .call();
  }

  render() {
    let { appContext, pageContext, observer, readOnly, partnerType } = this.props;
    let writeCap = pageContext.hasUserWriteCapability() && !readOnly
    let partner = observer.getMutableBean();

    let account = SESSION.getAccountAcl();
    let userFullName = account.getFullName();
    let accountId = account.getAccountId();
    partner.saleEmployeeId = accountId;
    partner.saleEmployeeLabel = userFullName;

    return (
      <div className="flex-vbox">
        <div className='flex-vbox shadow-sm rounded h-100 bg-white' style={{ overflow: 'hidden' }}>

          <bs.Row>
            <bs.Col span={6}>
              <input.BBStringField
                bean={partner} field='bfsonePartnerCode' label={T("Partner No.")} disable />
            </bs.Col>
            <bs.Col span={6}>
              <input.BBSelectField
                bean={partner} field='category' label={T("Category.")} disable={!writeCap || !observer.isNewBean()}
                options={this.categories} />
            </bs.Col>
          </bs.Row>

          <bs.Row>
            <bs.Col span={6}>
              <input.BBStringField
                bean={partner} field='name' label={T("Name (Short Label)")} disable={!writeCap} />
            </bs.Col>
            <bs.Col span={6}>
              <input.BBStringField
                bean={partner} field='taxCode' label={T("Tax Code")} disable={!writeCap} required
                onBgInputChange={this.onCheckTaxCode} />
            </bs.Col>
          </bs.Row>

          <UIBFSOnePartnerForm appContext={appContext} pageContext={pageContext}
            observer={observer} readOnly={readOnly} partnerType={partnerType} />
        </div>

      </div>
    )
  }
}

export class UISyncBFSOnePartnerEditor extends entity.AppDbComplexEntityEditor<UIBFSOnePartnerProps> {

  onChangeBFSOnePartnerCode = (bean: any, field: string, oldVal: any, newVal: any) => {
    if (!newVal) return;
    let { appContext, observer } = this.props;
    let param: any = { bfsonePartnerCode: newVal };
    appContext
      .createHttpBackendCall("BFSOnePartnerService", "fetchBFSOnePartnersByCode", param)
      .withSuccessData((data: any) => {
        observer.replaceWith(data);
        this.nextViewId();
        this.forceUpdate();
      })
      .call()
  }

  render() {
    let { appContext, pageContext, observer, readOnly } = this.props;
    let writeCap = pageContext.hasUserWriteCapability() && !readOnly
    let partner = observer.getMutableBean();

    return (
      <div className="flex-vbox">
        <input.BBStringField
          bean={partner} field='bfsonePartnerCode' label={T("Partner No.")}
          onInputChange={this.onChangeBFSOnePartnerCode} placeholder='Enter BFSOne Partner Code like CS011194' />

        <div className='flex-vbox shadow-sm rounded h-100 bg-white' style={{ overflow: 'hidden' }} key={this.viewId}>
          <bs.GreedyScrollable className='container-fluid'>
            <bs.Row>
              <bs.Col span={6}>
                <input.BBStringField
                  bean={partner} field='bfsonePartnerCode' label={T("Partner No.")} disable />
              </bs.Col>
              <bs.Col span={6}>
                <input.BBStringField
                  bean={partner} field='category' label={T("Category.")} disable />
              </bs.Col>
            </bs.Row>

            <input.BBStringField
              bean={partner} field='name' label={T("Name")} disable={!writeCap} />

            <input.BBStringField
              bean={partner} field='label' label={T("Label")} disable={!writeCap} />

            <input.BBStringField
              bean={partner} field='localizedLabel' label={T("Localized Label")} disable={!writeCap} />

            <input.BBTextField
              bean={partner} label={T('Address')} field="address" disable={!writeCap}
              style={{ height: '4em' }} />

            <input.BBTextField
              bean={partner} label={T('Address (Localized)')} field="localizedAddress" disable={!writeCap}
              style={{ height: '4em' }} />

            <bs.Row>
              <bs.Col span={6}>
                <input.BBSelectField bean={partner} field="source" label={T('Source')}
                  options={SOURCE_OPTIONS} disable={!writeCap} />
              </bs.Col>
              <bs.Col span={6}>
                <input.BBStringField
                  bean={partner} field='taxCode' label={T("Tax Code")} disable={!writeCap} />
              </bs.Col>
            </bs.Row>

            <bs.Row>
              <bs.Col span={6}>
                <input.BBStringField
                  bean={partner} field='personalContact' label={T("Personal Contact")} disable={!writeCap} />
              </bs.Col>
              <bs.Col span={6}>
                <input.BBStringField
                  bean={partner} field='cell' label={T("Cell Phone")} disable={!writeCap} />
              </bs.Col>
            </bs.Row>

            <bs.Row>
              <bs.Col span={6}>
                <input.BBStringField
                  bean={partner} field='fax' label={T("FAX.")} disable={!writeCap} />
              </bs.Col>

              <bs.Col span={6}>
                <input.BBStringField
                  bean={partner} field='email' label={T("Email")} disable={!writeCap} />
              </bs.Col>
            </bs.Row>

            <bs.Row>
              <bs.Col span={6}>
                <input.BBStringField
                  bean={partner} field='countryLabel' label={T("Country.")} disable={!writeCap} />
              </bs.Col>

              <bs.Col span={6}>
                <input.BBStringField
                  bean={partner} field='swiftCode' label={T("Swift Code.")} disable={!writeCap} />
              </bs.Col>
            </bs.Row>

            <bs.Row>
              <bs.Col span={6}>
                <input.BBStringField
                  bean={partner} field='bankName' label={T("Bank Name.")} disable={!writeCap} />
              </bs.Col>

              <bs.Col span={6}>
                <input.BBStringField
                  bean={partner} field='bankAddress' label={T("Bank Address.")} disable={!writeCap} />
              </bs.Col>
            </bs.Row>


            <bs.Row>
              <bs.Col span={6}>
                <input.BBStringField
                  bean={partner} field='requestUser' label={T("Request User.")} disable={!writeCap} />
              </bs.Col>

              <bs.Col span={6}>
                <div>
                  <module.company.hr.BBRefEmployee
                    appContext={appContext} pageContext={pageContext} bean={partner}
                    disable={!pageContext.hasUserModeratorCapability()}
                    beanIdField='saleEmployeeId' beanLabelField='saleEmployeeLabel'
                    label='Sale Man' placeholder='Sale Man'
                  />
                </div>
              </bs.Col>
            </bs.Row>

            <bs.Row>
              <bs.Col span={6}>
                {/* <input.BBStringField
                  bean={partner} field='industryCode' label={T("industryCode.")} disable={!writeCap} /> */}

                <module.resource.BBRefResource
                  appContext={appContext} pageContext={pageContext}
                  placement="bottom-start" offset={[0, 5]} minWidth={350}
                  disable={!writeCap} label={T('Industry')} placeholder="Enter Industry"
                  required bean={partner} beanIdField={'industryCode'}
                  beanLabelField={'industryLabel'} resourceType={"industry"} refResourceBy="identifier" />
              </bs.Col>

              <bs.Col span={6}>
                <input.BBSelectField
                  bean={partner} field='scope' label={T("Location")} disable={!writeCap || !observer.isNewBean()}
                  options={['Domestic', 'Overseas']}
                />
              </bs.Col>
            </bs.Row>

            <input.BBTextField
              bean={partner} label={T('Note')} field="note" disable={!writeCap}
              style={{ height: '4em' }} />

            <input.BBTextField
              bean={partner} label={T('Bill Ref (Doc)')} field={'printCustomConfirmBillInfo'}
              disable={!writeCap} style={{ height: '4em' }} />
          </bs.GreedyScrollable>
        </div>
        <bs.Toolbar className='border'>

          <entity.ButtonEntityCommit
            appContext={appContext} pageContext={pageContext}
            observer={observer} hide={!writeCap}
            commit={{
              entityLabel: T('BFSOne Partner'),
              context: 'company',
              service: 'BFSOnePartnerService', commitMethod: 'createBFSOnePartner'
            }}
            onPostCommit={this.onPostCommit}
          />
        </bs.Toolbar>

      </div>
    )
  }
}

