import React, { Component, createContext, ReactNode } from 'react';

import { module } from "@datatp-ui/erp";

import ExchangeRateConverter = module.settings.currency.ExchangeRateConverter;
import EXCHANGE_RATE_DB = module.settings.currency.EXCHANGE_RATE_DB;
import { ContainerType, ContainerTypeUnit } from '../../common/ContainerTypeUtil';

export const CONVERTER = new ExchangeRateConverter(Object.values(EXCHANGE_RATE_DB.currencyMap))

interface ICalculatorContext {
  index: number; // detect if context has change;
  grossWeightInKG: number;
  chargeableWeight: number;
  volumeInCbm: number;
  chargeableVolume: number;
  typeOfContainers: ContainerType[];
  chargeMargin: any; // It transport charge margin
  exchangeRateDB: ExchangeRateConverter;
}

type CalculatorContextType = {
  context: ICalculatorContext;
  setContext: (context: ICalculatorContext) => void;
}

const CalculatorContext = createContext<CalculatorContextType | undefined>(undefined);

interface ICalculatorProviderState {
  context: ICalculatorContext;
}

interface ICalculatorProviderProps {
  children: ReactNode;
  initContext?: ICalculatorContext
}

class CalculatorProvider extends Component<ICalculatorProviderProps, ICalculatorProviderState> {
  state: ICalculatorProviderState = {
    context: {
      index: 0,
      grossWeightInKG: 0,
      chargeableWeight: 0,
      volumeInCbm: 0,
      chargeableVolume: 0,
      typeOfContainers: [],
      chargeMargin: {},
      exchangeRateDB: CONVERTER
    },
  };

  componentDidMount(): void {
    const { initContext } = this.props;
    if (initContext) this.setContext(initContext);
  }

  setContext = (ctx: ICalculatorContext) => {
    let currentContext = this.state.context;
    let newIndex: number = currentContext.index + 1;
    this.setState({ context: { ...ctx, index: newIndex } });
  };

  render() {
    return (
      <CalculatorContext.Provider
        value={{ context: this.state.context, setContext: this.setContext }} >
        {this.props.children}
      </CalculatorContext.Provider>
    );
  }
}

function createCalculatorContext(chargeMargin: any, inquiry: any = {}) {
  let grossWeightInKG = inquiry['grossWeightKg'];
  let volumeInCbm = inquiry['volumeCbm'];
  let chargeableWeight = inquiry.chargeableWeight || 0
  let chargeableVolume = inquiry.chargeableVolume || 0;

  let containers: any[] = inquiry['containers'] || []
  let typeOfContainers = ContainerTypeUnit.computeFromContainer(containers)

  let exchangeRateDB: ExchangeRateConverter
  if (chargeMargin?.exchangeRates) {
    exchangeRateDB = new ExchangeRateConverter(chargeMargin?.exchangeRates)
  } else {
    exchangeRateDB = CONVERTER
  }

  let context: ICalculatorContext = {
    index: 0,
    grossWeightInKG,
    chargeableWeight,
    volumeInCbm,
    chargeableVolume,
    typeOfContainers,
    chargeMargin: chargeMargin?.transportChargeMargin || {},
    exchangeRateDB
  };
  return context;
}

function isContextHasChange(obj1: ICalculatorContext, obj2: ICalculatorContext): boolean {
  if (obj1.index != obj2.index) return true;
  if (
    obj1.grossWeightInKG !== obj2.grossWeightInKG ||
    obj1.chargeableWeight !== obj2.chargeableWeight ||
    obj1.volumeInCbm !== obj2.volumeInCbm ||
    obj1.chargeableVolume !== obj2.chargeableVolume ||
    obj1.chargeMargin !== obj2.chargeMargin
  ) {
    return true;
  }
  if (!obj1.typeOfContainers || !obj2.typeOfContainers || obj1.typeOfContainers.length !== obj2.typeOfContainers.length) {
    return true;
  }
  return false;
}

// ----------------------------------------------------------------
export {
  CalculatorProvider, CalculatorContext, CalculatorContextType,
  ICalculatorContext, createCalculatorContext, isContextHasChange
}