import React from "react";
import * as FeatherIcon from 'react-feather';
import { bs, app, util, input, entity } from '@datatp-ui/lib';
import { app as lgc_app } from '@datatp-ui/logistics';

import { ImportExportPurpose } from "../../model";

const SESSION = app.host.DATATP_HOST.session;

import _settings = lgc_app.logistics.settings;
import mapToTypeOfShipment = _settings.mapToTypeOfShipment;
import TransportationMode = _settings.TransportationMode;

export interface WQuickTimeRangeSelectorProps extends app.AppComponentProps {
  onModify?: entity.EntityOnModify;
  initBean?: { fromValue: string, toValue: string, label: string }
  disabled?: boolean
}

interface WQuickTimeRangeSelectorState {
  bean: { fromValue: string, toValue: string, label: string }
}
export class WQuickTimeRangeSelector extends app.AppComponent<WQuickTimeRangeSelectorProps, WQuickTimeRangeSelectorState> {

  constructor(props: WQuickTimeRangeSelectorProps) {
    super(props);

    const { initBean } = this.props;
    if (initBean) {
      this.state = { bean: initBean }
    } else {
      const today = new Date();
      const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
      const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

      let dateFilter = new util.TimeRange();
      dateFilter.fromSetDate(firstDayOfMonth);
      dateFilter.toSetDate(lastDayOfMonth);
      this.state = {
        bean: {
          fromValue: dateFilter.fromFormat(),
          toValue: dateFilter.toFormat(),
          label: 'This Month'
        }
      }
    }
  }

  onSelectTimeRange = (range: string, label: string): void => {
    const { onModify } = this.props;
    const { bean } = this.state;

    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    let fromDate: Date, toDate: Date;

    switch (range) {
      case 'today':
        // Today only
        fromDate = new Date(today);
        toDate = new Date(today);
        break;
      case 'ytd':
        // Year to date - from Jan 1 to today
        fromDate = new Date(today.getFullYear(), 0, 1);
        toDate = new Date(today);
        break;
      case 'week':
        // Start of current week (Sunday)
        fromDate = new Date(today);
        fromDate.setDate(today.getDate() - today.getDay());
        // End of current week (Saturday)
        toDate = new Date(fromDate);
        toDate.setDate(fromDate.getDate() + 6);
        break;
      case 'month':
        // Start of current month
        fromDate = new Date(today.getFullYear(), today.getMonth(), 1);
        // End of current month
        toDate = new Date(today.getFullYear(), today.getMonth() + 1, 0);
        break;
      case 'quarter':
        // Start of current quarter
        const currentQuarter = Math.floor(today.getMonth() / 3);
        fromDate = new Date(today.getFullYear(), currentQuarter * 3, 1);
        // End of current quarter
        toDate = new Date(today.getFullYear(), (currentQuarter + 1) * 3, 0);
        break;
      case 'lastWeek':
        // Start of last week
        fromDate = new Date(today);
        fromDate.setDate(today.getDate() - today.getDay() - 7);
        // End of last week
        toDate = new Date(fromDate);
        toDate.setDate(fromDate.getDate() + 6);
        break;
      case 'lastMonth':
        // Start of last month
        fromDate = new Date(today.getFullYear(), today.getMonth() - 1, 1);
        // End of last month
        toDate = new Date(today.getFullYear(), today.getMonth(), 0);
        break;
      case 'lastQuarter':
        // Start of last quarter
        const lastQuarter = Math.floor(today.getMonth() / 3) - 1;
        const year = lastQuarter < 0 ? today.getFullYear() - 1 : today.getFullYear();
        const quarter = lastQuarter < 0 ? 3 : lastQuarter;
        fromDate = new Date(year, quarter * 3, 1);
        // End of last quarter
        toDate = new Date(year, (quarter + 1) * 3, 0);
        break;
      case 'custom':
        this.setState({
          bean: {
            label: 'Custom',
            fromValue: bean.fromValue,
            toValue: bean.toValue
          }
        })
      default:
        return;
    }

    const updatedBean = {
      label: label,
      fromValue: util.TimeUtil.javaCompactDateTimeFormat(fromDate),
      toValue: util.TimeUtil.javaCompactDateTimeFormat(toDate)
    };

    if (onModify) onModify(updatedBean, 'label', null, label);
    this.setState({ bean: updatedBean });
  }

  onInputChange = (bean: any, _field: string, _oldVal: any, newVal: any) => {
    const { onModify } = this.props;
    bean[_field] = newVal;
    if (onModify) onModify(bean, _field, _oldVal, newVal);
    this.forceUpdate();
  }

  render(): React.ReactNode {
    const { disabled } = this.props;
    const { bean } = this.state;

    return (
      <bs.Popover className="flex-vbox flex-grow-0" title={('Quick Time Range')} placement="bottom-start" offset={[-400, 0]} closeOnTrigger=".btn" >
        <bs.PopoverToggle laf="secondary" outline className="me-1 p-1" style={{ color: '#6c757d' }} disabled={disabled}>
          <FeatherIcon.Calendar size={14} className="me-1" />
          {bean.label}
          <FeatherIcon.ChevronDown size={14} className="ms-1" />
        </bs.PopoverToggle>
        <bs.PopoverContent>
          <div className='flex-vbox' style={{ width: '500px' }}>

            <div className="flex-hbox gap-1">
              <div className="flex-grow-0 flex-vbox justify-content-center align-items-center px-1" style={{ width: 250 }}>

                <input.BBDateTimeField label="From" bean={bean} field={'fromValue'}
                  timeFormat={false} onInputChange={this.onInputChange} />
                <input.BBDateTimeField label="To" bean={bean} field={'toValue'}
                  timeFormat={false} onInputChange={this.onInputChange} />

              </div>

              <div className="flex-grow-1 flex-vbox align-items-center p-1">
                {/* Thêm nút Today */}
                <div className="btn-group w-100 mb-2">
                  <button className="btn btn-sm btn-outline-primary w-100 p-1" onClick={() => this.onSelectTimeRange('today', 'Today')}>
                    <FeatherIcon.Calendar size={12} className="me-1" /> Today
                  </button>
                </div>

                <div className="btn-group w-100">
                  <button className="btn btn-sm btn-outline-secondary p-1 flex-grow-1" onClick={() => this.onSelectTimeRange('week', 'This Week')}>This Week</button>
                  <button className="btn btn-sm btn-outline-secondary p-1 flex-grow-1" onClick={() => this.onSelectTimeRange('month', 'This Month')}>This Month</button>
                  <button className="btn btn-sm btn-outline-secondary p-1 flex-grow-1" onClick={() => this.onSelectTimeRange('quarter', 'This Quarter')}>This Quarter</button>
                </div>
                <div className="btn-group w-100 mt-2">
                  <button className="btn btn-sm btn-outline-secondary p-1 flex-grow-1" onClick={() => this.onSelectTimeRange('lastWeek', 'Last Week')}>Last Week</button>
                  <button className="btn btn-sm btn-outline-secondary p-1 flex-grow-1" onClick={() => this.onSelectTimeRange('lastMonth', 'Last Month')}>Last Month</button>
                  <button className="btn btn-sm btn-outline-secondary p-1 flex-grow-1" onClick={() => this.onSelectTimeRange('lastQuarter', 'Last Quarter')}>Last Quarter</button>
                </div>
              </div>
            </div>

            <div className="flex-hbox flex-grow-0 justify-content-between align-items-center mt-2 py-1 border-top">
              <div className="flex-grow-1 ps-2">
                <button className="btn btn-sm btn-link p-0" onClick={() => this.onSelectTimeRange('ytd', 'Year to Date')}>Year to Date</button>
              </div>
              <bs.Button laf='info' outline className="px-2 py-1 mx-1" style={{ width: 100 }}
                onClick={() => this.onSelectTimeRange('custom', 'Custom')}>
                <FeatherIcon.Check size={12} /> OK
              </bs.Button>
            </div>
          </div>
        </bs.PopoverContent>
      </bs.Popover>
    )

  }

}

export interface WCompanySelectorProps extends app.AppComponentProps {
  onModify?: entity.EntityOnModify;
  initCompany?: { code: string, label: string };
}

interface WCompanySelectorState {
  company: { code: string, label: string };
}
export class WCompanySelector extends app.AppComponent<WCompanySelectorProps, WCompanySelectorState> {

  constructor(props: WCompanySelectorProps) {
    super(props);
    const { initCompany } = this.props;
    if (initCompany) {
      this.state = { company: initCompany }
    } else {
      let companyContext = SESSION.getCurrentCompanyContext();
      this.state = {
        company: {
          code: companyContext['companyCode'],
          label: companyContext['companyLabel']
        }
      }
    }
  }

  onInputChange = (companyCode: string, _companyLabel: string) => {
    const { onModify } = this.props;
    const company: any = {
      code: companyCode,
      label: _companyLabel
    }
    this.setState({ company: company })
    if (onModify) onModify(company, 'companyCode', null, companyCode);
  }

  render(): React.ReactNode {
    const { company } = this.state;

    let availableCompanyAcls: Array<any> = SESSION.getAccountAcl().getAvailableCompanyAcls() || []

    const companyLabels: any[] = [];
    const companyCodes: string[] = [];
    for (let i = 0; i < availableCompanyAcls.length; i++) {
      let ctx = availableCompanyAcls[i];
      companyCodes.push(ctx.companyCode);
      companyLabels.push(ctx.companyLabel);
    }

    return (
      <bs.Popover className="flex-vbox flex-grow-0" placement="bottom-start" closeOnTrigger=".btn" >
        <bs.PopoverToggle laf="secondary" outline className="me-1 p-1" style={{ color: '#6c757d' }}>
          <FeatherIcon.Twitter size={14} className="me-1" />
          {company.label}
          <FeatherIcon.ChevronDown size={14} className="ms-1" />
        </bs.PopoverToggle>
        <bs.PopoverContent>
          <div className='flex-vbox align-items-center gap-1' style={{ width: '180px' }}>
            {
              availableCompanyAcls.map(sel => {
                return (
                  <bs.Button laf="secondary" outline size="sm" className="me-1 py-2 w-100 text-start"
                    onClick={() => this.onInputChange(sel.companyCode, sel.companyLabel)}>
                    <FeatherIcon.Twitter size={14} className="me-1" />
                    {sel.companyLabel}
                  </bs.Button>
                )
              })
            }
          </div>
        </bs.PopoverContent>
      </bs.Popover>
    )
  }
}

export interface WKeyAccountReportTypeProps extends app.AppComponentProps {
  onModify?: entity.EntityOnModify;
  reportType?: 'BD' | 'SALES';
}

interface WKeyAccountReportTypeState {
  reportType: 'BD' | 'SALES';
}
export class WKeyAccountReportTypeSelector extends app.AppComponent<WKeyAccountReportTypeProps, WKeyAccountReportTypeState> {

  constructor(props: WKeyAccountReportTypeProps) {
    super(props);
    const { reportType } = this.props;
    if (reportType) {
      this.state = { reportType }
    } else {
      this.state = {
        reportType: 'SALES'
      }
    }
  }

  onInputChange = (type?: 'BD' | 'SALES') => {
    const { onModify } = this.props;
    this.setState({ reportType: type || 'SALES' })
    if (onModify) onModify(type, 'reportType', null, type || 'SALES');
  }

  render(): React.ReactNode {
    const { reportType } = this.state;

    return (
      <bs.Popover className="flex-vbox flex-grow-0" placement="bottom-start" closeOnTrigger=".btn" >
        <bs.PopoverToggle laf="secondary" outline className="me-1 p-1" style={{ color: '#6c757d', width: 120 }}>
          <div className="flex-hbox align-items-center justify-content-between px-1">
            <FeatherIcon.TrendingUp size={14} className="me-1" />
            {reportType.toUpperCase()}
            <FeatherIcon.ChevronDown size={14} className="ms-1" />
          </div>
        </bs.PopoverToggle>
        <bs.PopoverContent>
          <div className='flex-vbox align-items-center gap-1' style={{ width: '150px' }}>
            <bs.Button laf="secondary" outline size="sm" className="me-1 py-2 w-100 text-start"
              onClick={() => this.onInputChange('SALES')}>
              <FeatherIcon.TrendingUp size={14} className="me-1" />
              {`SALES`}
            </bs.Button>
            <bs.Button laf="secondary" outline size="sm" className="me-1 py-2 w-100 text-start"
              onClick={() => this.onInputChange('BD')}>
              <FeatherIcon.TrendingUp size={14} className="me-1" />
              {`BD`}
            </bs.Button>
          </div>
        </bs.PopoverContent>
      </bs.Popover>
    )
  }
}

export interface WTypeOfServiceSelectorProps extends app.AppComponentProps {
  onModify?: entity.EntityOnModify;
  typeOfService?: { label: string, purpose?: ImportExportPurpose, mode?: TransportationMode };
}

interface WTypeOfServiceSelectorState {
  typeOfService: { label?: string, purpose?: ImportExportPurpose, mode?: TransportationMode };
}
export class WTypeOfServiceSelector extends app.AppComponent<WTypeOfServiceSelectorProps, WTypeOfServiceSelectorState> {

  constructor(props: WTypeOfServiceSelectorProps) {
    super(props);
    const { typeOfService } = this.props;
    if (typeOfService) {
      this.state = { typeOfService }
    } else {
      this.state = {
        typeOfService: {
          label: 'Type Of Service'
        }
      }
    }
  }

  onInputChange = (purpose?: ImportExportPurpose, mode?: TransportationMode) => {
    const { onModify } = this.props;
    const typeOfService: any = {
      purpose: purpose,
      mode: mode,
      label: (purpose && mode) ? mapToTypeOfShipment(purpose, mode) : 'Type Of Service'
    }
    this.setState({ typeOfService: typeOfService })
    if (onModify) onModify(typeOfService, 'typeOfService', null, purpose);
  }

  render(): React.ReactNode {
    const { typeOfService } = this.state;

    return (
      <bs.Popover className="flex-vbox flex-grow-0" placement="bottom-start" closeOnTrigger=".btn" >
        <bs.PopoverToggle laf="secondary" outline className="me-1 p-1" style={{ color: '#6c757d' }}>
          <FeatherIcon.Twitter size={14} className="me-1" />
          {typeOfService.label}
          <FeatherIcon.ChevronDown size={14} className="ms-1" />
        </bs.PopoverToggle>
        <bs.PopoverContent>
          <bs.Button laf="secondary" outline size="sm" className="me-1 py-2 w-100 text-start"
            onClick={() => this.onInputChange()}>
            <FeatherIcon.Twitter size={14} className="me-1" />
            {`Type Of Service`}
          </bs.Button>
          <div className='flex-vbox align-items-center gap-1' style={{ width: '180px' }}>
            <bs.Button laf="secondary" outline size="sm" className="me-1 py-2 w-100 text-start"
              onClick={() => this.onInputChange(ImportExportPurpose.IMPORT, TransportationMode.AIR)}>
              <FeatherIcon.Twitter size={14} className="me-1" />
              {`Air Import`}
            </bs.Button>
            <bs.Button laf="secondary" outline size="sm" className="me-1 py-2 w-100 text-start"
              onClick={() => this.onInputChange(ImportExportPurpose.IMPORT, TransportationMode.SEA_FCL)}>
              <FeatherIcon.Twitter size={14} className="me-1" />
              {`Sea FCL Import`}
            </bs.Button>
            <bs.Button laf="secondary" outline size="sm" className="me-1 py-2 w-100 text-start"
              onClick={() => this.onInputChange(ImportExportPurpose.IMPORT, TransportationMode.SEA_LCL)}>
              <FeatherIcon.Twitter size={14} className="me-1" />
              {`Sea LCL Import`}
            </bs.Button>
            <bs.Button laf="secondary" outline size="sm" className="me-1 py-2 w-100 text-start"
              onClick={() => this.onInputChange(ImportExportPurpose.EXPORT, TransportationMode.AIR)}>
              <FeatherIcon.Twitter size={14} className="me-1" />
              {`Air Export`}
            </bs.Button>
            <bs.Button laf="secondary" outline size="sm" className="me-1 py-2 w-100 text-start"
              onClick={() => this.onInputChange(ImportExportPurpose.EXPORT, TransportationMode.SEA_FCL)}>
              <FeatherIcon.Twitter size={14} className="me-1" />
              {`Sea FCL Export`}
            </bs.Button>
            <bs.Button laf="secondary" outline size="sm" className="me-1 py-2 w-100 text-start"
              onClick={() => this.onInputChange(ImportExportPurpose.EXPORT, TransportationMode.SEA_LCL)}>
              <FeatherIcon.Twitter size={14} className="me-1" />
              {`Sea LCL Export`}
            </bs.Button>
          </div>
        </bs.PopoverContent>
      </bs.Popover>
    )
  }
}

export interface WMultiTypeOfServiceSelectorProps extends app.AppComponentProps {
  onModify?: entity.EntityOnModify;
  selectedServices?: Array<{ label: string, purpose?: ImportExportPurpose, mode?: TransportationMode }>;
}

interface WMultiTypeOfServiceSelectorState {
  selectedServices: Array<{ label: string, purpose?: ImportExportPurpose, mode?: TransportationMode }>;
}

export class WMultiTypeOfServiceSelector extends app.AppComponent<WMultiTypeOfServiceSelectorProps, WMultiTypeOfServiceSelectorState> {

  constructor(props: WMultiTypeOfServiceSelectorProps) {
    super(props);
    const { selectedServices } = this.props;
    if (selectedServices && selectedServices.length > 0) {
      this.state = { selectedServices }
    } else {
      this.state = {
        selectedServices: []
      }
    }
  }

  isServiceSelected = (purpose?: ImportExportPurpose, mode?: TransportationMode): boolean => {
    const { selectedServices } = this.state;
    return selectedServices.some(service =>
      service.purpose === purpose && service.mode === mode
    );
  }

  toggleService = (purpose?: ImportExportPurpose, mode?: TransportationMode) => {
    const { onModify } = this.props;
    const { selectedServices } = this.state;

    const serviceLabel = (purpose && mode) ? mapToTypeOfShipment(purpose, mode) : '';
    const serviceExists = this.isServiceSelected(purpose, mode);

    let updatedServices;

    if (serviceExists) {
      updatedServices = selectedServices.filter(service =>
        !(service.purpose === purpose && service.mode === mode)
      );
    } else {
      const newService = {
        purpose,
        mode,
        label: serviceLabel
      };
      updatedServices = [...selectedServices, newService];
    }

    this.setState({ selectedServices: updatedServices });

    if (onModify) onModify(updatedServices, 'selectedServices', null, updatedServices);
  }

  clearAll = () => {
    const { onModify } = this.props;
    const updatedServices: any[] = [];

    this.setState({ selectedServices: updatedServices });

    if (onModify) onModify(updatedServices, 'selectedServices', null, updatedServices);
  }

  getDisplayLabel = (): string => {
    const { selectedServices } = this.state;

    if (selectedServices.length === 0) {
      return 'Type Of Service';
    } else if (selectedServices.length === 1) {
      return selectedServices[0].label;
    } else {
      return `${selectedServices.length} services selected`;
    }
  }

  render(): React.ReactNode {
    const { selectedServices } = this.state;
    const displayLabel = this.getDisplayLabel();

    return (
      <bs.Popover className="flex-vbox flex-grow-0" placement="bottom-start" closeOnTrigger=".btn-close" >
        <bs.PopoverToggle laf="secondary" outline className="me-1 p-1" style={{ color: '#6c757d' }}>
          <FeatherIcon.Layers size={14} className="me-1" />
          {displayLabel}
          <FeatherIcon.ChevronDown size={14} className="ms-1" />
        </bs.PopoverToggle>
        <bs.PopoverContent>
          <div className="flex-vbox" style={{ width: '220px' }}>
            <div className="flex-hbox justify-content-between align-items-center mb-2 pb-2 border-bottom">
              <span className="fw-bold">Services</span>
              <button className="btn-close" onClick={this.clearAll} aria-label="Clear all"></button>
            </div>

            <div className='flex-vbox align-items-start gap-1' style={{ maxHeight: '300px', overflowY: 'auto' }}>
              {/* Air Import */}
              <div className="form-check">
                <input
                  className="form-check-input"
                  type="checkbox"
                  id="airImport"
                  checked={this.isServiceSelected(ImportExportPurpose.IMPORT, TransportationMode.AIR)}
                  onChange={() => this.toggleService(ImportExportPurpose.IMPORT, TransportationMode.AIR)}
                />
                <label className="form-check-label" htmlFor="airImport">
                  Air Import
                </label>
              </div>

              {/* Sea FCL Import */}
              <div className="form-check">
                <input
                  className="form-check-input"
                  type="checkbox"
                  id="seaFclImport"
                  checked={this.isServiceSelected(ImportExportPurpose.IMPORT, TransportationMode.SEA_FCL)}
                  onChange={() => this.toggleService(ImportExportPurpose.IMPORT, TransportationMode.SEA_FCL)}
                />
                <label className="form-check-label" htmlFor="seaFclImport">
                  Sea FCL Import
                </label>
              </div>

              {/* Sea LCL Import */}
              <div className="form-check">
                <input
                  className="form-check-input"
                  type="checkbox"
                  id="seaLclImport"
                  checked={this.isServiceSelected(ImportExportPurpose.IMPORT, TransportationMode.SEA_LCL)}
                  onChange={() => this.toggleService(ImportExportPurpose.IMPORT, TransportationMode.SEA_LCL)}
                />
                <label className="form-check-label" htmlFor="seaLclImport">
                  Sea LCL Import
                </label>
              </div>

              {/* Air Export */}
              <div className="form-check">
                <input
                  className="form-check-input"
                  type="checkbox"
                  id="airExport"
                  checked={this.isServiceSelected(ImportExportPurpose.EXPORT, TransportationMode.AIR)}
                  onChange={() => this.toggleService(ImportExportPurpose.EXPORT, TransportationMode.AIR)}
                />
                <label className="form-check-label" htmlFor="airExport">
                  Air Export
                </label>
              </div>

              {/* Sea FCL Export */}
              <div className="form-check">
                <input
                  className="form-check-input"
                  type="checkbox"
                  id="seaFclExport"
                  checked={this.isServiceSelected(ImportExportPurpose.EXPORT, TransportationMode.SEA_FCL)}
                  onChange={() => this.toggleService(ImportExportPurpose.EXPORT, TransportationMode.SEA_FCL)}
                />
                <label className="form-check-label" htmlFor="seaFclExport">
                  Sea FCL Export
                </label>
              </div>

              {/* Sea LCL Export */}
              <div className="form-check">
                <input
                  className="form-check-input"
                  type="checkbox"
                  id="seaLclExport"
                  checked={this.isServiceSelected(ImportExportPurpose.EXPORT, TransportationMode.SEA_LCL)}
                  onChange={() => this.toggleService(ImportExportPurpose.EXPORT, TransportationMode.SEA_LCL)}
                />
                <label className="form-check-label" htmlFor="seaLclExport">
                  Sea LCL Export
                </label>
              </div>
            </div>

            {selectedServices.length > 0 && (
              <div className="flex-hbox justify-content-between align-items-center mt-2 pt-2 border-top">
                <span className="small text-muted">{selectedServices.length} services selected</span>
                <bs.Button laf='primary' size="sm" className="py-1"
                  onClick={() => { }}>
                  Apply
                </bs.Button>
              </div>
            )}
          </div>
        </bs.PopoverContent>
      </bs.Popover>
    )
  }
}
