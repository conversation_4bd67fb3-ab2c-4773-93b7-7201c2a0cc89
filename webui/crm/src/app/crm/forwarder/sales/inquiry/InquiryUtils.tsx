import React from "react";
import { app, entity, util } from '@datatp-ui/lib';
import { module } from '@datatp-ui/erp';
import { app as lgc_app } from '@datatp-ui/logistics';

import { T } from "../backend";
import { UIGenericInquiryEditor } from "./generic/UIGenericInquiry";


import TransportationMode = lgc_app.logistics.settings.TransportationMode;
import TransportationTool = lgc_app.logistics.settings.TransportationTool;

import ImportExportPurpose = lgc_app.logistics.settings.ImportExportPurpose;
import SeaType = lgc_app.logistics.settings.SeaType;
import TruckType = lgc_app.logistics.settings.TruckType;
import TransportationTermOfService = lgc_app.logistics.settings.TransportationTermOfService;
import CustomClearanceTool = lgc_app.logistics.settings.CustomClearanceTool;
import CustomClearanceType = lgc_app.logistics.settings.CustomClearanceType;

import MassUnit = module.settings.unit.MassUnit;
import VolumeUnit = module.settings.unit.VolumeUnit;
import TimeUtil = util.TimeUtil;

export class InquiryUtil {

  static cloneSpecificInquiry(inquiry: any, clearId: boolean) {
    if (!inquiry) return null;
    let clone = entity.EntityUtil.clone(inquiry, clearId);
    entity.EntityUtil.clearIds(clone.containers);
    clone.code = undefined;
    return clone;
  }

  static cloneGenericInquiry(inquiry: any, clearId: boolean) {
    if (!inquiry) return null;
    let clone = entity.EntityUtil.clone(inquiry, clearId);
    entity.EntityUtil.clearIds(clone.routes);
    entity.EntityUtil.clearIds(clone.commodities);
    entity.EntityUtil.clearIds(clone.cargos);
    entity.EntityUtil.clearIds(clone.containers);
    entity.EntityUtil.clearIds(clone.terminals);

    clone.code = `inquiry-${TimeUtil.toDateTimeIdFormat(new Date())}`;
    return clone;
  }

  static generateCodePrefix(mode?: TransportationMode) {
    let codePrefix = '';
    if (mode === TransportationMode.AIR) {
      codePrefix = 'A';
    } else if (mode === TransportationMode.SEA_FCL || mode === TransportationMode.SEA_LCL) {
      codePrefix = 'S';
    } else if (mode === TransportationMode.TRUCK_CONTAINER || mode === TransportationMode.TRUCK_REGULAR) {
      codePrefix = 'T';
    } else if (mode === TransportationMode.RAIL) {
      codePrefix = 'R';
    }
    return codePrefix;
  }

  static createSpecificInquiryModel(genericInquiry: any, route: any) {
    let genericInquiryClone = this.cloneGenericInquiry(genericInquiry, true);
    let mode = route.transportationMode;
    let inquiry = {
      code: `Convert to Generic Inquiry ${genericInquiryClone.code}`,
      referenceCode: genericInquiryClone.referenceCode,
      label: genericInquiry.label,
      fromLocationCode: route.fromLocationCode,
      fromLocationLabel: route.fromLocationLabel,
      toLocationCode: route.toLocationCode,
      toLocationLabel: route.toLocationLabel,
      clientPartnerId: genericInquiry.clientPartnerId,
      clientLabel: genericInquiry.clientLabel,
      assigneeEmployeeId: genericInquiry.assigneeEmployeeId,
      assigneeLabel: genericInquiry.assigneeLabel,
      mode: mode,
      customClearanceType: null,
      grossWeight: genericInquiry.grossWeight,
      grossWeightUnit: genericInquiry.grossWeightUnit,
      volume: genericInquiry.volume,
      volumeUnit: genericInquiry.volumeUnit,
      commodities: genericInquiryClone.commodities,
      cargos: [],
      containers: [],
    }

    if (TransportationTool.isSeaFCL(mode) || TransportationTool.isTruckContainer(mode)) {
      inquiry.cargos = genericInquiryClone.cargos;
    } else {
      inquiry.containers = genericInquiryClone.containers;
    }

    return inquiry;
  }

  //Create Custom Clearance Model from a generic inquiry
  static createCustomClearanceModel(genericInquiry: any, terminal: any) {
    let genericInquiryClone = this.cloneGenericInquiry(genericInquiry, true);
    let type = terminal.customClearanceType;

    let inquiry = {
      code: `Convert to Generic Inquiry ${genericInquiryClone.code}`,
      clientPartnerId: genericInquiry.clientPartnerId,
      clientLabel: genericInquiry.clientLabel,
      assigneeEmployeeId: genericInquiry.assigneeEmployeeId,
      assigneeLabel: genericInquiry.assigneeLabel,
      mode: null,
      customClearanceType: type,
      grossWeight: genericInquiry.grossWeight,
      grossWeightUnit: genericInquiry.grossWeightUnit,
      volume: genericInquiry.volume,
      volumeUnit: genericInquiry.volumeUnit,
      commodities: genericInquiryClone.commodities,
      cargos: [],
      containers: [],
    }

    if (CustomClearanceTool.isCustomClearanceSeaFCL(type)) {
      inquiry.containers = genericInquiryClone.containers;
    } else {
      inquiry.cargos = genericInquiryClone.cargos;
    }

    return inquiry;
  }

  static createBookingModel(specificInquiry: any) {
    const transportMode: TransportationMode = specificInquiry.mode;
    const customClearanceType: CustomClearanceType = specificInquiry.customClearanceType;
    const purpose: ImportExportPurpose = specificInquiry.purpose;

    let clientPartnerId = specificInquiry.clientPartnerId;
    let clientLabel = specificInquiry.clientLabel;

    let assigneeEmployeeId = specificInquiry.assigneeEmployeeId;
    let assigneeLabel = specificInquiry.assigneeLabel;

    let fromLocCode = specificInquiry.fromLocationCode;
    let fromLocLabel = specificInquiry.fromLocationLabel;

    let toLocCode = specificInquiry.toLocationCode;
    let toLocLabel = specificInquiry.toLocationLabel;

    let customerAirPrice = null as any;
    let customerSeaPrice = null as any;
    let customerRailPrice = null as any;
    let customerTruckPrice = [] as Array<any>;
    let customerCC = [] as Array<any>;
    let route = "";
    if (fromLocCode && toLocCode) route = `${fromLocCode}-${toLocCode}`.toLowerCase();
    let booking: any = {
      bookingDate: TimeUtil.javaCompactDateTimeFormat(new Date()),
      editMode: entity.EditMode.VALIDATED,
      storageState: entity.StorageState.ACTIVE,
      inquiry: { ...specificInquiry },
    };
    let dateAsString = TimeUtil.javaCompactDateTimeFormat(new Date()).substring(0, 10);
    if (transportMode) {
      booking.termAnd
      booking.label = `${transportMode} ${clientLabel ? clientLabel : 'Booking'} ${route} ${dateAsString}`
      if (TransportationTool.isSeaFCL(transportMode)) {
        customerSeaPrice = {
          code: `sea-fcl-booking-${TimeUtil.toDateTimeIdFormat(new Date())}`,
          label: `FCL Booking ${new Date().toLocaleDateString("en-US")}`,
          editMode: entity.EditMode.VALIDATED,
          type: SeaType.FCL,
          assigneeEmployeeId: assigneeEmployeeId,
          assigneeLabel: assigneeLabel,
          shipperPartnerId: ImportExportPurpose.EXPORT == purpose ? clientPartnerId : undefined,
          shipperLabel: ImportExportPurpose.EXPORT == purpose ? clientLabel : undefined,
          consigneePartnerId: ImportExportPurpose.IMPORT == purpose ? clientPartnerId : undefined,
          consigneeLabel: ImportExportPurpose.IMPORT == purpose ? clientLabel : undefined,
          payerFullName: clientLabel,
          payerPartnerId: clientPartnerId,
          fromLocationCode: fromLocCode,
          fromLocationLabel: fromLocLabel,
          toLocationCode: toLocCode,
          toLocationLabel: toLocLabel,
          currency: "VND",
          domesticCurrency: "VND",
          refCurrency: "VND"
        }
      } else if (TransportationTool.isSeaLCL(transportMode)) {
        customerSeaPrice = {
          code: `sea-lcl-booking-${TimeUtil.toDateTimeIdFormat(new Date())}`,
          label: `LCL Booking ${new Date().toLocaleDateString("en-US")}`,
          editMode: entity.EditMode.VALIDATED,
          type: SeaType.LCL,
          assigneeEmployeeId: assigneeEmployeeId,
          assigneeLabel: assigneeLabel,
          shipperPartnerId: ImportExportPurpose.EXPORT == purpose ? clientPartnerId : undefined,
          shipperLabel: ImportExportPurpose.EXPORT == purpose ? clientLabel : undefined,
          consigneePartnerId: ImportExportPurpose.IMPORT == purpose ? clientPartnerId : undefined,
          consigneeLabel: ImportExportPurpose.IMPORT == purpose ? clientLabel : undefined,
          payerFullName: clientLabel,
          payerPartnerId: clientPartnerId,
          fromLocationCode: fromLocCode,
          fromLocationLabel: fromLocLabel,
          toLocationCode: toLocCode,
          toLocationLabel: toLocLabel,
          currency: "VND",
          domesticCurrency: "VND",
          refCurrency: "VND"
        }
      } else if (TransportationTool.isAir(transportMode)) {
        customerAirPrice = {
          code: `air-booking-${TimeUtil.toDateTimeIdFormat(new Date())}`,
          label: `Air Booking ${new Date().toLocaleDateString("en-US")}`,
          editMode: entity.EditMode.VALIDATED,
          assigneeEmployeeId: assigneeEmployeeId,
          assigneeLabel: assigneeLabel,
          shipperPartnerId: ImportExportPurpose.EXPORT == purpose ? clientPartnerId : undefined,
          shipperLabel: ImportExportPurpose.EXPORT == purpose ? clientLabel : undefined,
          consigneePartnerId: ImportExportPurpose.IMPORT == purpose ? clientPartnerId : undefined,
          consigneeLabel: ImportExportPurpose.IMPORT == purpose ? clientLabel : undefined,
          payerFullName: clientLabel,
          payerPartnerId: clientPartnerId,
          fromLocationCode: fromLocCode,
          fromLocationLabel: fromLocLabel,
          toLocationCode: toLocCode,
          toLocationLabel: toLocLabel,
          currency: "VND",
          domesticCurrency: "VND",
          refCurrency: "VND",
        }
      } else if (TransportationTool.isRail(transportMode)) {
        customerRailPrice = {
          code: `rail-booking-${TimeUtil.toDateTimeIdFormat(new Date())}`,
          label: `Rail Booking ${new Date().toLocaleDateString("en-US")}`,
          editMode: entity.EditMode.VALIDATED,
          assigneeEmployeeId: assigneeEmployeeId,
          assigneeLabel: assigneeLabel,
          shipperPartnerId: ImportExportPurpose.EXPORT == purpose ? clientPartnerId : undefined,
          shipperLabel: ImportExportPurpose.EXPORT == purpose ? clientLabel : undefined,
          consigneePartnerId: ImportExportPurpose.IMPORT == purpose ? clientPartnerId : undefined,
          consigneeLabel: ImportExportPurpose.IMPORT == purpose ? clientLabel : undefined,
          payerFullName: clientLabel,
          payerPartnerId: clientPartnerId,
          fromLocationCode: fromLocCode,
          fromLocationLabel: fromLocLabel,
          toLocationCode: toLocCode,
          toLocationLabel: toLocLabel,
          currency: "VND",
          domesticCurrency: "VND",
          refCurrency: "VND",
        }
      } else if (TransportationTool.isTruck(transportMode)) {
        let pickupLocationCode = specificInquiry.deliveryServiceInquiry?.pickupLocationCode;
        let pickupLocationLabel = specificInquiry.deliveryServiceInquiry?.pickupLocationLabel;
        let pickupAddress = specificInquiry.deliveryServiceInquiry?.pickupAddress;

        let deliveryLocationCode = specificInquiry.deliveryServiceInquiry?.deliveryLocationCode;
        let deliveryLocationLabel = specificInquiry.deliveryServiceInquiry?.deliveryLocationLabel;
        let deliveryAddress = specificInquiry.deliveryServiceInquiry?.deliveryAddress;

        let route = '';
        if (pickupLocationCode && deliveryLocationCode) {
          route = `${pickupLocationCode}-${deliveryLocationCode}`.toLowerCase();
        }
        booking.label = `${transportMode} ${clientLabel ? clientLabel : 'Booking'}${route}${new Date().toLocaleDateString("en-US")}`;

        let customerTruckCharge = {
          editMode: entity.EditMode.VALIDATED,
          assigneeEmployeeId: assigneeEmployeeId,
          assigneeLabel: assigneeLabel,
          shipperPartnerId: ImportExportPurpose.EXPORT == purpose ? clientPartnerId : undefined,
          shipperLabel: ImportExportPurpose.EXPORT == purpose ? clientLabel : undefined,
          consigneePartnerId: ImportExportPurpose.IMPORT == purpose ? clientPartnerId : undefined,
          consigneeLabel: ImportExportPurpose.IMPORT == purpose ? clientLabel : undefined,
          payerFullName: clientLabel,
          payerPartnerId: clientPartnerId,
          pickupLocationCode: pickupLocationCode,
          pickupLocationLabel: pickupLocationLabel,
          pickupAddress: pickupAddress,
          deliveryLocationCode: deliveryLocationCode,
          deliveryLocationLabel: deliveryLocationLabel,
          deliveryAddress: deliveryAddress,
          currency: "VND",
          domesticCurrency: "VND",
          refCurrency: "VND",
        }

        if (TransportationTool.isTruckContainer(transportMode)) {
          customerTruckPrice.push({
            code: `truck-container-booking-${TimeUtil.toDateTimeIdFormat(new Date())}`,
            label: `Truck Container Booking ${new Date().toLocaleDateString("en-US")}`,
            truckType: TruckType.CONTAINER,
            ...customerTruckCharge
          });
        } else if (TransportationTool.isTruckRegular(transportMode)) {
          customerTruckPrice.push({
            code: `truck-regular-booking-${TimeUtil.toDateTimeIdFormat(new Date())}`,
            label: `Truck Regular Booking ${new Date().toLocaleDateString("en-US")}`,
            truckType: TruckType.REGULAR,
            ...customerTruckCharge
          });
        }
      }
    } else {
      let termLocCode = specificInquiry.terminalLocationCode;
      let termLocLabel = specificInquiry.terminalLocationLabel;
      booking.label = `${customClearanceType} ${clientLabel ? clientLabel : 'Booking'} ${termLocCode} ${dateAsString}`;
      customerCC.push({
        code: `custom-clearance-booking-${TimeUtil.toDateTimeIdFormat(new Date())}`,
        label: `Custom Clearance ${new Date().toLocaleDateString("en-US")}`,
        type: customClearanceType,
        editMode: entity.EditMode.VALIDATED,
        assigneeEmployeeId: assigneeEmployeeId,
        assigneeLabel: assigneeLabel,
        payerFullName: clientLabel,
        payerPartnerId: clientPartnerId,
        terminalLocationCode: termLocCode,
        terminalLocationLabel: termLocLabel,
        currency: "VND",
        domesticCurrency: "VND",
        refCurrency: "VND",
      });
    }

    booking = {
      ...booking,
      customerAirTransportCharge: customerAirPrice,
      customerRailTransportCharge: customerRailPrice,
      customerSeaTransportCharge: customerSeaPrice,
      customerTruckTransportCharges: customerTruckPrice,
      customerCustomClearances: customerCC
    };
    return booking;
  }

  static changeTermOfService(uiSource: entity.AppDbComplexEntity, newVal: any) {
    let { observer, onModify } = uiSource.props;
    let deliveryServiceInquiry = observer.getComplexBeanProperty('deliveryServiceInquiry', {});
    observer.replaceBeanProperty("termOfService", newVal)
    if (TransportationTermOfService.PortToPort === newVal) {
      deliveryServiceInquiry = null;
    } else if (TransportationTermOfService.PortToDoor === newVal) {
      deliveryServiceInquiry.pickupLocation = null;
    } else if (TransportationTermOfService.DoorToPort === newVal) {
      deliveryServiceInquiry.deliveryLocation = null;
    }
    observer.replaceBeanProperty('deliveryServiceInquiry', deliveryServiceInquiry);
    if (onModify) onModify(observer.getMutableBean(), 'termOfService', null, newVal);
    else uiSource.forceUpdate();
  }
}

export type ShowUISpecificInquiryProps = {
  ui: app.AppComponent,
  inquiry: any,
  popup?: boolean,
  onPostCommit?: entity.EntityOnPostCommit;
};

export type ShowUIGenericInquiryProps = {
  uiSource: app.AppComponent,
  isNewQuotation?: boolean;
  inquiry: any,
  popup?: boolean,
  onPostCommit?: entity.EntityOnPostCommit;
  isNew?: boolean;
};
//TODO: Dan - clean
export class UIInquiryUtils {
  static showUIGenericInquiry({ uiSource, inquiry, onPostCommit, popup }: ShowUIGenericInquiryProps) {
    let { pageContext, readOnly } = uiSource.props;
    let observer = new entity.ComplexBeanObserver(inquiry);
    const label = inquiry.label ? `${inquiry.label}` : T('New Generic Inquiry');
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      let canEditEntity = pageCtx.canEditEntityByStorageState(inquiry);
      return (
        <UIGenericInquiryEditor
          appContext={appCtx} pageContext={pageCtx}
          readOnly={!canEditEntity || readOnly} observer={observer} onPostCommit={onPostCommit} />
      );
    }
    pageContext.createPopupPage('inquiry', T(label), createAppPage, { size: 'xl', backdrop: 'static' });
  }
}

export class MeasureCalculator {
  static onGenerateChargeableWeight(observer: entity.ComplexBeanObserver): number {
    let inquiry = observer.getMutableBean();
    const transportMode = inquiry.mode;
    if (!inquiry.grossWeightUnit) return 0;
    if (transportMode !== TransportationMode.AIR || transportMode !== TransportationMode.SEA_LCL) return 0;
    let cargos: Array<any> = observer.getComplexArrayProperty('cargos', []);
    if (cargos.length === 0) return 0;

    let volumeWeight = 0;
    const grossWeight = inquiry.grossWeight;
    const conversionFactor = 6000;
    for (let cargo of cargos) {
      if (!cargo.dimensionUnit) continue;
      volumeWeight += (cargo.dimensionX * cargo.dimensionY * cargo.dimensionZ) / conversionFactor;
    }
    if (grossWeight > volumeWeight) {
      return grossWeight;
    } else return volumeWeight;
  }

  static onGenerateGrossWeight(observer: entity.ComplexBeanObserver): number {
    let grossWeightUnit = observer.getMutableBean().grossWeightUnit;
    if (!grossWeightUnit) return 0;
    let cargos = observer.getComplexArrayProperty('cargos', []);
    let containers = observer.getComplexArrayProperty('containers', []);
    let grossWeightKG = this.convertGrossWeightKG(cargos, containers);
    return MassUnit.convert(grossWeightKG, MassUnit.KG.unit, grossWeightUnit);
  }

  static onGenerateVolume(observer: entity.ComplexBeanObserver): number {
    let volumeUnit = observer.getMutableBean().volumeUnit;
    if (!volumeUnit) return 0;
    let cargos = observer.getComplexArrayProperty('cargos', []);
    let volumeOfCBM = this.convertVolumeCBM(cargos);
    return VolumeUnit.convert(volumeOfCBM, VolumeUnit.M3.unit, volumeUnit);
  }

  private static convertGrossWeightKG(cargos: Array<any>, containers: Array<any>): number {
    let totalGrossWeightInKG = 0;
    if (cargos.length > 0) {
      for (let cargo of cargos) {
        if (!cargo.grossWeightUnit) continue;
        totalGrossWeightInKG += MassUnit.convert(cargo.totalGrossWeight, cargo.grossWeightUnit, MassUnit.KG.unit);
      }
    }
    if (containers.length > 0) {
      for (let container of containers) {
        if (!container.cargoWeightUnit) continue;
        totalGrossWeightInKG += MassUnit.convert(container.totalCargoWeight, container.cargoWeightUnit, MassUnit.KG.unit);
      }
    };
    return totalGrossWeightInKG;
  }

  private static convertVolumeCBM(cargos: Array<any>) {
    let volumeCBM = 0;
    if (cargos.length > 0) {
      for (let cargo of cargos) {
        if (!cargo.volumeUnit) continue;
        volumeCBM += VolumeUnit.convert(cargo.totalVolume, cargo.volumeUnit, VolumeUnit.M3.unit);
      }
    }
    return volumeCBM;
  }
}