import React from 'react';
import * as FeatherIcon from 'react-feather'
import { server, grid, bs, entity, app, util } from '@datatp-ui/lib';
import { app as lgc_app } from '@datatp-ui/logistics';

import { T } from '../backend';
import {
  calculator, CalculatorContext,
  CalculatorContextType, isContextHasChange
} from '../calculator';
import { UICustomerLocalChargeList } from './UICustomerLocalChargeList';

import { UIQuoteListEditorProps } from './UIQuoteComponents';
import { GridConfigFactory } from './builder/QuoteGridConfigFactory';
import { PricePlugin, SearchParams, UITransportPriceList } from '../../price/common';
import { UISimpleMarginConfig } from './chargemodel/UICustomerChargeModel';
import { AddMethod } from '../../price';
import { CustomerEntityUtil } from './CustomerChargeUtils';

import TransportationMode = lgc_app.logistics.settings.TransportationMode;
import TransportationTool = lgc_app.logistics.settings.TransportationTool;
import { ContainerType } from '../../common/ContainerTypeUtil';

export class UIQuotationListEditor extends entity.VGridEntityListEditor<UIQuoteListEditorProps> {
  purpose: 'IMPORT' | 'EXPORT' = 'EXPORT'
  mode: TransportationMode;

  private scrollContainerRef: React.RefObject<HTMLDivElement> = React.createRef();

  getScrollContainerHeight = (): number => {
    return this.scrollContainerRef.current?.clientHeight || 0;
  }

  shouldComponentUpdate(_nextProps: Readonly<UIQuoteListEditorProps>, _nextState: Readonly<any>, nextContext: any): boolean {
    const calculatorContext = (this.context as CalculatorContextType)?.context || {}
    return isContextHasChange(calculatorContext, nextContext);
  }

  constructor(props: UIQuoteListEditorProps) {
    super(props);
    const { observer } = this.props;
    let inquiry = observer.getComplexBeanProperty('inquiry', {});
    this.purpose = inquiry['purpose'] || 'EXPORT';
    this.mode = inquiry['mode']
  }

  componentDidMount(): void {
    let records = this.vgridContext.model.getRecords();
    if (records.length > 0) {
      this.onSelect(0, records[0]);
    }
    this.forceUpdate();
  }

  onSelect(_row: number, record: any) {
    super.onSelect(_row, record)
    const { observer, onModifyBean } = this.props;
    let dRecords: Array<grid.DisplayRecord> = this.vgridContext.model.getDisplayRecordList().getDisplayRecords();
    for (let rec of dRecords) {
      grid.initRecordState(rec.record, rec.row)
      grid.getRecordState(rec.record).selected = false;
    }

    grid.getRecordState(record).markModified();
    grid.getRecordState(record).selected = true;

    if (TransportationTool.isSea(this.mode)) {
      observer.replaceBeanProperty('seaQuotesSelector', [record])
    } else if (TransportationTool.isAir(this.mode)) {
      observer.replaceBeanProperty('airQuotesSelector', [record])
    }
    if (onModifyBean) onModifyBean(this.vgridContext.model.getRecords(), entity.ModifyBeanActions.MODIFY);
    this.forceUpdate()
  }

  componentWillUnmount(): void {
    this.onModify();
  }

  onModify() {
    const { observer, onModifyBean } = this.props;
    let records: Array<any> = this.vgridContext.model.getRecords();
    if (TransportationTool.isSea(this.mode)) {
      observer.replaceBeanProperty('seaQuotes', records)
    } else if (TransportationTool.isAir(this.mode)) {
      observer.replaceBeanProperty('airQuotes', records)
    }
    observer.updateMutableBean();
    if (onModifyBean) onModifyBean(records, entity.ModifyBeanActions.MODIFY);
  }

  createVGridConfig(): grid.VGridConfig {
    const { observer, initContainers } = this.props;
    let inquiry = observer.getComplexBeanProperty('inquiry', {});
    this.mode = inquiry['mode']

    const fieldConfigs: grid.FieldConfig[] = TransportationTool.isSeaLCL(this.mode)
      ? GridConfigFactory.createLCLConfig(observer, this)
      : TransportationTool.isSeaFCL(this.mode)
        ? GridConfigFactory.createFCLConfig(observer, this, initContainers)
        : GridConfigFactory.createAirConfig(observer, this)

    let config: grid.VGridConfig = {
      record: {
        dataCellHeight: 40,
        editor: {
          supportViewMode: ['table'],
          enable: true
        },
        fields: fieldConfigs,
      },
      toolbar: {
        hide: true,
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          }
        }
      },
    }
    return config;
  }

  onDelete(records: Array<any>) {
    if (!records || records.length === 0) return;
    const { appContext } = this.props;
    let ids = records
      .map(record => record['id'])
      .filter(id => id !== undefined);

    let uniqueIds = [...new Set(ids)];
    if (!uniqueIds || uniqueIds.length === 0) {
      appContext.addOSNotification('success', T('Delete Quote Success!'));
      for (let record of records) {
        this.vgridContext.model.removeRecord(record);
      }
      this.nextEditorViewId();
      this.onModify();
      this.forceUpdate()
    } else {

      if (TransportationTool.isSea(this.mode)) {

        appContext.createHttpBackendCall('CustomerSeaChargeService', 'deleteQuotationQuoteByIds', { ids: uniqueIds })
          .withSuccessData((_data: any) => {
            appContext.addOSNotification('success', T('Delete Quote Success!'));
            for (let record of records) {
              this.vgridContext.model.removeRecord(record);
            }

            let filterRecords = this.vgridContext.model.getFilterRecords();

            if (filterRecords.length > 0) {
              this.onSelect(0, filterRecords[0])
            } else {
              this.selectBean.bean = {};
              this.currentBeanObserver = this.createBeanObserver('complex') as entity.ComplexBeanObserver;
            }

            this.nextEditorViewId();
            this.onModify();
            this.forceUpdate()
          })
          .call();

      } else if (TransportationTool.isAir(this.mode)) {
        appContext.createHttpBackendCall('CustomerAirChargeService', 'deleteQuotationQuoteByIds', { ids: uniqueIds })
          .withSuccessData((_data: any) => {
            appContext.addOSNotification('success', T('Delete Quote Success!'));
            for (let record of records) {
              this.vgridContext.model.removeRecord(record);
            }
            this.nextEditorViewId();
            this.selectBean.bean = {};

            let filterRecords = this.vgridContext.model.getFilterRecords();
            let modifiedRecords: Array<any> = this.vgridContext.model.getMarkModifiedRecords();
            if (modifiedRecords.length === 0) {
              if (filterRecords.length > 0) {
                this.onSelect(0, filterRecords[0])
              } else {
                this.onNewAction();
              }
            }
            this.onModify();
            this.forceUpdate()
          })
          .call();
      }
    }
  }

  override renderBeanEditor() {
    const { initContainers } = this.props;
    let currentObserver = this.createBeanObserver('complex') as entity.ComplexBeanObserver;

    const onModifyLocalCharge = (_beans: Array<any>, _action: any) => {
      let currentObserver = this.getCurrentBeanObserver() as entity.ComplexBeanObserver;
      currentObserver.replaceBeanProperty('localCharges', _beans);
      let bean: any = currentObserver.getMutableBean();

      let records: Array<any> = this.vgridContext.model.getRecords();

      let holder: Array<any> = [];
      for (let rec of records) {
        if (grid.getRecordState(rec).isMarkModified()) {
          holder.push(bean)
        } else {
          holder.push(rec);
        }
      }
      this.vgridContext.model.update(holder);
      this.onModify();
      this.forceUpdate();
    }

    return (
      <UICustomerLocalChargeList {...this.props} typeOfContainers={initContainers}
        plugin={currentObserver.createVGridEntityListEditorPlugin('localCharges', [])}
        onModifyBean={onModifyLocalCharge} />
    );
  }

  onNewCharge = () => {
    let { appContext, observer } = this.props;
    let quotationId: number = observer.getBeanProperty('id', null);
    if (!quotationId) {
      let message = (<div className="ms-1 text-info py-3 border-bottom">Save Quotation First!!!</div>);
      bs.dialogShow('Message', message, { backdrop: 'static', size: 'md' });
      return;
    }

    let creationReq: any = {
      mode: this.mode,
    }

    let inquiry: any = observer.getComplexBeanProperty('inquiry', {});
    if (inquiry['transportation']) {
      creationReq['genericQuotationId'] = quotationId;
    } else {
      creationReq['specificQuotationId'] = quotationId;
    }

    if (TransportationTool.isSea(this.mode)) {
      appContext.createHttpBackendCall('CustomerSeaChargeService', 'createQuotationSeaCharge', { creation: creationReq })
        .withSuccessData((data: Array<any>) => {
          this.vgridContext.model.addRecords(data);
          this.vgridContext.getVGrid().forceUpdateView();
          this.onModify();
        })
        .withFail((_response: server.BackendResponse) => {
          bs.notificationShow('danger', T('Add Price Failed!'));
          this.vgridContext.getVGrid().forceUpdateView();
        })
        .call();
    } else if (TransportationTool.isAir(this.mode)) {
      appContext.createHttpBackendCall('CustomerAirChargeService', 'createQuotationAirCharge', { creation: creationReq })
        .withSuccessData((airCharges: Array<any>) => {
          this.vgridContext.model.addRecords(airCharges);
          this.onModify();
          this.vgridContext.getVGrid().forceUpdateView();
        })
        .withFail((_response: server.BackendResponse) => {
          bs.notificationShow('danger', T('Add Price Failed!'));
          this.vgridContext.getVGrid().forceUpdateView();
        })
        .call();
    }

  }

  onClearPrice = () => {
    let selectedRecords: Array<any> = this.vgridContext.model.getSelectedRecords();
    if (selectedRecords.length > 0) {
      this.onDelete(selectedRecords);
    } else {
      bs.dialogConfirmMessage('Clear Prices', 'You want to delete all prices?', () => {
        let records: Array<grid.DisplayRecord> = this.vgridContext.model.getRecords();
        this.onDelete(records);
      });
    }
  }

  onMatchPrice = () => {
    const { appContext, pageContext, observer } = this.props;
    let quotationId: number = observer.getBeanProperty('id', null);
    if (!quotationId) {
      let message = (<div className="ms-1 text-info py-3 border-bottom">Save Quotation First!!!</div>);
      bs.dialogShow('Message', message, { backdrop: 'static', size: 'md' });
      return;
    }

    let inquiry: any = observer.getComplexBeanProperty('inquiry', {});

    const onMultiSelect = (_appCtx: app.AppContext, _pageCtx: app.PageContext, prices: Array<any>) => {
      let ids = prices
        .map(record => record['id'])
        .filter(id => id !== undefined);
      let uniqueIds = [...new Set(ids)];

      _pageCtx.back({ reload: true });

      let creationReq: any = {
        mode: this.mode,
        referenceIds: uniqueIds,
      }

      if (inquiry['transportation']) {
        creationReq['genericQuotationId'] = quotationId;
      } else {
        creationReq['specificQuotationId'] = quotationId;
      }

      appContext.createHttpBackendCall('CustomerSeaChargeService', 'createQuotationSeaCharge', { creation: creationReq })
        .withSuccessData((seaQuotes: Array<any>) => {
          this.vgridContext.model.addRecords(seaQuotes);
          this.nextEditorViewId();
          let records: Array<any> = this.vgridContext.model.getRecords();
          let modifiedRecords: Array<any> = this.vgridContext.model.getMarkModifiedRecords();
          if (modifiedRecords.length === 0) {
            if (records.length > 0) {
              this.onSelect(0, records[0])
            } else {
              this.onNewAction();
            }
          }
          this.onModify();
          this.forceUpdate()
        })
        .withFail((_response: server.BackendResponse) => {
          bs.notificationShow('danger', T('Match Prices Failed!'));
          this.vgridContext.getVGrid().forceUpdateView();
        })
        .call();
    }

    //TODO: Dan - review this code, replace hard code transportation mode for generic quotation.
    let searchParam: SearchParams = new SearchParams(inquiry['purpose'], inquiry['mode'] || TransportationMode.SEA_FCL);
    searchParam.fromLocationCode = inquiry['fromLocationCode'];
    searchParam.toLocationCode = inquiry['toLocationCode'];
    let plugin: PricePlugin = searchParam.pluginBuilder();

    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <div className='flex-vbox'>
          <UITransportPriceList plugin={plugin} appContext={appCtx} pageContext={pageCtx}
            onMultiSelect={onMultiSelect}
            onSelect={(appContext: app.AppContext, pageContext: app.PageContext, entity: any) => {
              onMultiSelect(appContext, pageContext, [entity])
            }} />
        </div>
      )
    }
    pageContext.createPopupPage('match-prices', T('Match Prices'), createAppPage, { size: 'xl', backdrop: 'static' });
  }

  onApplyMargin() {
    const { pageContext, observer } = this.props;
    const quotation: any = observer.getMutableBean();

    let chargeModel: any = observer.getComplexBeanProperty('customerChargeModel', {});
    if (!chargeModel || Object.keys(chargeModel).length === 0) {
      chargeModel = CustomerEntityUtil.createDefaultCustomerChargeModel();
    }

    let chargeMargin: any = chargeModel['transportChargeMargin'] || {};
    let marginConfig: any = { addMethod: AddMethod.Amount, margin: 0 }

    if (chargeMargin && TransportationTool.isSea(this.mode)) {
      marginConfig = chargeMargin['seaTransportChargeMargin'];
    } else if (chargeMargin && TransportationTool.isAir(this.mode)) {
      marginConfig = chargeMargin['airTransportChargeMargin'];
    }

    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {

      const onApplyMargin = (bean: any, targetContainerTypes: ContainerType[], reset: boolean = false) => {
        pageCtx.back();

        appCtx.addOSNotification("success", T('Apply Margin Success'));

        const calculatorContext = (this.context as CalculatorContextType)?.context || {}
        calculatorContext.typeOfContainers = targetContainerTypes;

        let selectedRecords: Array<any> = this.vgridContext.model.getSelectedRecords();
        if (selectedRecords.length === 0) {
          selectedRecords = this.vgridContext.model.getFilterRecords();
        }

        if (TransportationTool.isSeaLCL(this.mode)) {
          chargeMargin['seaTransportChargeMargin'] = bean;
          calculatorContext.chargeMargin = chargeMargin;
          for (let rec of selectedRecords) {
            calculator.seaLCL.calculatorQuote(rec, calculatorContext.chargeableVolume, reset);
            if (!reset) {
              calculator.seaLCL.applyMarginDep(calculatorContext, rec, true);
            }
          }
        } else if (TransportationTool.isSeaFCL(this.mode)) {
          chargeMargin['seaTransportChargeMargin'] = bean;
          calculatorContext.chargeMargin = chargeMargin;
          for (let rec of selectedRecords) {
            if (reset) {
              calculator.seaFCL.resetQuoteCosting(calculatorContext, rec);
            } else {
              calculator.seaFCL.applyMargin(calculatorContext, rec, true);
            }
          }

        } else if (TransportationTool.isAir(this.mode)) {
          chargeMargin['airTransportChargeMargin'] = bean;
          calculatorContext.chargeMargin = chargeMargin;
          for (let rec of selectedRecords) {
            calculator.air.calculatorQuote(rec, calculatorContext.chargeableWeight, reset);
            if (!reset) {
              calculator.air.applyMargin(calculatorContext, rec, true);
            }
          }
        }

        this.onModify()
        this.forceUpdate()
      }

      return (
        <div className='flex-vbox'>
          <UISimpleMarginConfig appContext={appCtx} pageContext={pageCtx}
            observer={new entity.ComplexBeanObserver(marginConfig)} quotation={quotation}
            onApplyMargin={onApplyMargin} />
        </div>
      );
    }
    pageContext.createPopupPage('margin-config', T('Margin Config'), createAppPage, { size: 'md', backdrop: 'static' });

  }

  render() {
    const { observer } = this.props;
    let inquiry: any = observer.getComplexBeanProperty('inquiry', {});
    let fromLocationLabel = inquiry['fromLocationLabel'] || 'POL'
    let toLocationlabel = inquiry['toLocationLabel'] || 'POD'

    let title = TransportationTool.isSea(this.mode) ? `Ocean Freight` : `Air Freight`;
    let route = `${fromLocationLabel} - ${toLocationlabel}`

    const quoteContainerH = this.getScrollContainerHeight();
    const columnH = Math.floor(quoteContainerH / 3);
    let minColumnH = Math.max(columnH, 250)

    return (
      <div className='flex-vbox border' ref={this.scrollContainerRef}>
        <div className='flex-vbox flex-grow-0' style={{ height: minColumnH }}>
          <div className="flex-hbox flex-grow-0 justify-content-between align-items-center mx-2 bg-body-highlight" >

            <div className="flex-hbox justify-content-start align-items-center">
              <h5 className="mb-0 mx-2" style={{ color: '#344767', fontWeight: 600 }}>{title}</h5>

              <div className="d-flex align-items-center">
                <div className="badge d-flex align-items-center gap-1"
                  style={{
                    backgroundColor: 'rgba(13, 110, 253, 0.08)',
                    color: '#0d6efd',
                    fontSize: '0.75rem',
                    padding: '0.15rem 0.5rem',
                    fontWeight: 600,
                    border: '1px solid rgba(13, 110, 253, 0.15)',
                    borderRadius: '4px'
                  }}>
                  <FeatherIcon.Navigation size={10} style={{ marginTop: '-1px' }} />
                  {route}
                </div>
              </div>

            </div>

            <div className="flex-hbox flex-grow-0">
              <bs.Button laf='info' className="text-decoration-none border-0 py-1 px-1" outline
                onClick={() => this.onApplyMargin()}>
                <FeatherIcon.Activity size={12} /> Apply Margin
              </bs.Button>

              {/* <bs.Button laf='info' className="text-decoration-none border-0 py-1 px-1" outline
                onClick={() => this.onMatchPrice()}>
                <FeatherIcon.Search size={12} /> Match Prices
              </bs.Button> */}

              <bs.Button laf='info' className="text-decoration-none border-0 py-1 px-1" outline
                onClick={this.onNewCharge}>
                <FeatherIcon.Plus size={12} /> Add Price
              </bs.Button>

              <bs.Button laf='info' className="text-decoration-none border-0 py-1 px-1" outline
                onClick={this.onClearPrice}>
                <FeatherIcon.Trash2 size={12} /> Clear Prices
              </bs.Button>
            </div>
          </div>
          <div className="flex-vbox bg-white">
            <grid.VGrid context={this.vgridContext} />
          </div>
        </div>
        <div className='flex-vbox' style={{ height: (minColumnH * 2) - 40 }} key={this.editorViewId}>
          {this.renderBeanEditor()}
        </div>
      </div>
    )
  }
}


