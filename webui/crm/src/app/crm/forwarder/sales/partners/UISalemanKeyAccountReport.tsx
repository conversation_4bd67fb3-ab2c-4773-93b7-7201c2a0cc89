import React from 'react';
import * as FeatherIcon from 'react-feather';

import { bs, app, input, server, util, entity } from '@datatp-ui/lib';

import {
  UISalemanKeyAccountReportList,
  UISalemanKeyAccountReportPlugin
} from './UISalemanKeyAccountReportList';

import { BBRefMultiCustomerLead } from '../leads/BBRefMultiCustomerLead';
import {
  UIVolumeSalemanKeyAccountReportList,
  UIVolumeSalemanKeyAccountReportPlugin
} from './UIVolumeSalemanKeyAccountReportList';
import { BBRefMultiBFSOnePartner } from '../../bfsone/BBRefMultiBFSOneCustomer';
import { WQuickTimeRangeSelector } from '../dashboard/UIDashboardUtility';

const SESSION = app.host.DATATP_HOST.session;

const USD_CURR_FORMAT = (val: any) => util.text.formater.currency(val, 2);

interface UIGridReportProps extends app.AppComponentProps {
  rawRecords: Array<any>;
  title?: string;
  type: 'FCL' | 'AIR' | 'LCL' | 'OTHER'
}
class UIPerformanceGridReport extends bs.AvailableSize<UIGridReportProps> {
  config: bs.GridConfig;

  constructor(props: UIGridReportProps) {
    super(props);
    const { title, type } = this.props;
    let volumeLabel = 'GW (KGS)';
    if (type === 'FCL') {
      volumeLabel = 'TEUs'
    } else if (type === 'LCL') {
      volumeLabel = 'CBM'
    } else if (type === 'OTHER') {
      volumeLabel = 'TKs'
    }

    this.config = {
      header: {
        height: 20,
      },
      row: {
        height: 30,
      },
      showHeader: true,
      showBorder: true,
      columns: [
        { field: 'keyAccountName', label: `${title ? title : 'Key Account'}`, width: 320, cssClass: 'text-body fs-9 text-start' },
        { field: 'shipmentCount', label: 'Jobs', width: 120, cssClass: 'text-body fs-9 text-start' },
        { field: 'volume', label: volumeLabel, width: 120, cssClass: 'text-body fs-9 text-start' },
        { field: 'revenue', label: 'Revenue (USD)', width: 120, cssClass: 'text-body fs-9 text-start' },
        { field: 'profit', label: 'Profit (USD)', width: 120, cssClass: 'text-body fs-9 text-start' },
      ]
    }
  }

  getColumnWidths(contentWidth: number): number[] {
    const totalRatio = 2.5 + 1 + 1 + 1 + 1; // 5.5
    const unit = contentWidth / totalRatio;

    const widths = [
      Math.max(Math.floor(unit * 2.5), 120),
      Math.max(Math.floor(unit * 1), 120),
      Math.max(Math.floor(unit * 1), 120),
      Math.max(Math.floor(unit * 1), 120),
      Math.max(Math.floor(unit * 1), 120)
    ];

    return widths;
  }

  renderContent(width: number | string, _height: number): React.ReactElement {
    const contentWidth = typeof width === 'number' ? width : 500;
    const columnWidths = this.getColumnWidths(contentWidth - 30); // Subtract padding

    // Update column widths
    this.config.columns[0].width = columnWidths[0];
    this.config.columns[1].width = columnWidths[1];
    this.config.columns[2].width = columnWidths[2];
    this.config.columns[3].width = columnWidths[3];
    this.config.columns[4].width = columnWidths[4];

    const { rawRecords } = this.props;

    return (
      <div className="p-1" style={{ width: contentWidth }}>
        <bs.Grid config={this.config} beans={rawRecords} />
      </div>
    );
  }
}

class ReportBean {
  code: string;
  suggestionOrRequest: string;
  salemanAccountId: number;
  salemanLabel: string;
  reportedDateFrom: string;
  reportedDateTo: string;
  volumePerformance: any;
  profit: number;
  volume: number;
  revenue: number;
  highlights: {
    newCustomers: Array<any>,
    expectedLeadsToWin: Array<any>,
    marketInformation: string,
    reportedIssues: string
  };
  forecast: any;
}

export interface UISalemanKeyAccountReportProps extends app.AppComponentProps {
  space: 'User' | 'Company' | 'System';
}
export class UISalemanKeyAccountReport extends app.AppComponent<UISalemanKeyAccountReportProps> {
  dateFilterLabel: string = 'This Month';
  viewId: number = util.IDTracker.next();
  reportBean: ReportBean = {
    salemanAccountId: SESSION.getAccountId(),
    salemanLabel: SESSION.getAccountAcl().getFullName(),
    reportedDateFrom: '',
    reportedDateTo: '',
    code: '',
    volumePerformance: {},
    profit: 0,
    volume: 0,
    revenue: 0,
    highlights: {
      newCustomers: [],
      expectedLeadsToWin: [],
      marketInformation: '',
      reportedIssues: ''
    },
    forecast: {},
    suggestionOrRequest: '',
  }

  constructor(props: UISalemanKeyAccountReportProps) {
    super(props);
    this.initializeThisMonth();
    this.loadData();
  }

  initializeThisMonth() {
    const today = new Date();
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    firstDayOfMonth.setHours(0, 0, 0, 0);
    lastDayOfMonth.setHours(23, 59, 59, 999);

    this.reportBean.reportedDateFrom = util.TimeUtil.javaCompactDateTimeFormat(firstDayOfMonth);
    this.reportBean.reportedDateTo = util.TimeUtil.javaCompactDateTimeFormat(lastDayOfMonth);
    this.dateFilterLabel = 'This Month';
  }

  initialReportBean() {
    const today = new Date();
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    firstDayOfMonth.setHours(0, 0, 0, 0);
    lastDayOfMonth.setHours(23, 59, 59, 999);

    this.reportBean = {
      reportedDateFrom: util.TimeUtil.javaCompactDateTimeFormat(firstDayOfMonth),
      reportedDateTo: util.TimeUtil.javaCompactDateTimeFormat(lastDayOfMonth),
      salemanAccountId: SESSION.getAccountId(),
      salemanLabel: SESSION.getAccountAcl().getFullName(),
      code: '',
      volumePerformance: {},
      profit: 0,
      volume: 0,
      revenue: 0,
      highlights: {
        newCustomers: [],
        expectedLeadsToWin: [],
        marketInformation: '',
        reportedIssues: ''
      },
      forecast: {},
      suggestionOrRequest: '',
    }
    this.dateFilterLabel = 'This Month';
    this.loadData();
  }

  componentDidMount(): void {
    this.loadLatestReport();
  }

  loadLatestReport() {
    const { appContext } = this.props;
    this.markLoading(true);

    appContext.createHttpBackendCall('PartnerReportService', 'getLatestReportBySaleman')
      .withSuccessData((latestReport: any) => {
        if (latestReport) {
          this.reportBean = latestReport;
          this.dateFilterLabel = 'Custom';
        } else {
          this.initialReportBean();
        }
        this.viewId = util.IDTracker.next();
        this.forceUpdate();
      })
      .withFail((response: server.BackendResponse) => {
        let messageError: string = response.error['message'] || 'An unexpected error occurred. Please try again later.';
        bs.dialogShow('Error',
          <div className="text-danger fw-bold text-center py-3 border-bottom">
            <FeatherIcon.AlertCircle className="mx-2" />{messageError}
          </div>,
          { backdrop: 'static', size: 'md' }
        );
        if (!this.reportBean) {
          this.initialReportBean();
        }
        return;
      })
      .call()
  }

  rawDataProcessor(rawData: Array<any> = []): Array<any> {
    // Bước 1: Group theo transactionId
    const groupedByTransaction = rawData.reduce((acc, record) => {
      const transactionId = record.transactionId;

      if (!acc[transactionId]) {
        acc[transactionId] = {
          transactionId,
          keyAccountName: record.keyAccountName, // Lấy giá trị đầu tiên
          volume: 0,
          revenue: 0,
          cost: 0
        };
      }

      // Sum các giá trị số
      const revenue = (record.subtotalSellingVnd + record.subtotalOtherDebitVnd) / (record.exchangeRateUsd || 1) || 0;
      const cost = (record.subtotalBuyingVnd + record.subtotalOtherCreditVnd) / (record.exchangeRateUsd || 1) || 0;

      acc[transactionId].revenue += (revenue || 0);
      acc[transactionId].cost += (cost || 0);
      acc[transactionId].volume += (record.volume || 0);

      return acc;
    }, {});

    // Bước 2: Group theo keyAccountName từ kết quả đã group theo transactionId
    const groupedByKeyAccount = Object.values(groupedByTransaction).reduce<Record<string, {
      keyAccountName: string;
      shipmentCount: number;
      volume: number;
      revenue: number;
      cost: number;
    }>>((acc, transaction: {
      keyAccountName: string;
      volume: number;
      revenue: number;
      cost: number;
    }) => {
      const keyAccountName = transaction.keyAccountName;

      if (!acc[keyAccountName]) {
        acc[keyAccountName] = {
          keyAccountName,
          shipmentCount: 0,
          volume: 0,
          revenue: 0,
          cost: 0
        };
      }

      // Tăng shipmentCount lên 1 cho mỗi transaction
      acc[keyAccountName].shipmentCount += 1;
      acc[keyAccountName].revenue += transaction.revenue;
      acc[keyAccountName].cost += transaction.cost;
      acc[keyAccountName].volume += transaction.volume;

      return acc;
    }, {});

    let holder: Array<any> = Object.values(groupedByKeyAccount).map((group: any) => ({
      keyAccountName: group.keyAccountName,
      shipmentCount: group.shipmentCount,
      volume: group.volume,
      profit: group.revenue - group.cost,
      revenue: group.revenue
    })).sort((a, b) => b.profit - a.profit);

    if (holder.length === 0) return holder;

    let sumAll: { shipmentCount: number, volume: number, profit: number; revenue: number } = holder.reduce(
      (acc, curr) => {
        acc.shipmentCount += curr.shipmentCount;
        acc.volume += curr.volume;
        acc.profit += curr.profit;
        acc.revenue += curr.revenue;
        return acc;
      },
      { shipmentCount: 0, volume: 0, profit: 0, revenue: 0 }
    );

    holder.push({
      keyAccountName: 'Total',
      shipmentCount: sumAll.shipmentCount,
      volume: sumAll.volume,
      profit: sumAll.profit,
      revenue: sumAll.revenue
    })

    for (let rec of holder) {
      rec['keyAccountName'] = util.text.formater.uiTruncate(rec.keyAccountName || 'N/A', 200, true);
      rec['volume'] = USD_CURR_FORMAT(rec.volume)
      rec['profit'] = USD_CURR_FORMAT(rec.profit)
      rec['revenue'] = USD_CURR_FORMAT(rec.revenue)
    }
    return holder;
  }

  loadData() {
    const { appContext } = this.props;
    this.markLoading(true);

    const { reportedDateFrom, reportedDateTo, salemanAccountId } = this.reportBean;

    const params: any = {
      params: {
        salemanAccountId: salemanAccountId,
        fromDate: reportedDateFrom,
        toDate: reportedDateTo,
      }
    }

    appContext.createHttpBackendCall('PartnerReportService', 'searchVolumeSalemanKeyAccountReport', { params })
      .withSuccessData((records: Array<any>) => {

        const airImpHolder: Array<any> = []
        const airExpHolder: Array<any> = []
        const fclImpHolder: Array<any> = []
        const fclExpHolder: Array<any> = []
        const lclImpHolder: Array<any> = []
        const lclExpHolder: Array<any> = []
        const otherHolder: Array<any> = []

        for (let rec of records) {
          const mapRec: any = { ...rec };
          if (rec.shipmentType === 'FREE-HAND') {
            mapRec.keyAccountCode = rec.customerCode;
            mapRec.keyAccountName = (rec.customerName || 'N/A')
          } else {
            mapRec.keyAccountCode = rec.agentCode;
            mapRec.keyAccountName = (rec.agentName || 'N/A');
          }

          if (mapRec['typeOfService'] === 'AirExpTransactions') {
            mapRec.volume = mapRec.hawbGw;
            airExpHolder.push(mapRec);
          } else if (mapRec['typeOfService'] === 'AirImpTransactions') {
            mapRec.volume = mapRec.hawbGw;
            airImpHolder.push(mapRec);
          } else if (mapRec['typeOfService'] === 'SeaExpTransactions_FCL') {
            // Count TEUs based on containerSize
            const containerSize = mapRec.containerSize || '';
            const teusMatch = containerSize.match(/(\d+)[xX](\d+)/);
            if (teusMatch) {
              const containers = parseInt(teusMatch[1]);
              const size = parseInt(teusMatch[2]);
              mapRec.volume = containers * (size === 20 ? 1 : 2); // 20' = 1 TEU, 40' = 2 TEU
            }
            fclExpHolder.push(mapRec)
          } else if (mapRec['typeOfService'] === 'SeaImpTransactions_FCL') {
            // Count TEUs based on containerSize
            const containerSize = mapRec.containerSize || '';
            const teusMatch = containerSize.match(/(\d+)[xX](\d+)/);
            if (teusMatch) {
              const containers = parseInt(teusMatch[1]);
              const size = parseInt(teusMatch[2]);
              mapRec.volume = containers * (size === 20 ? 1 : 2); // 20' = 1 TEU, 40' = 2 TEU
            }
            fclImpHolder.push(mapRec)
          } else if (mapRec['typeOfService'] === 'SeaExpTransactions_CSL' || mapRec['typeOfService'] === 'SeaExpTransactions_LCL') {
            mapRec.volume = mapRec.hawbCbm;
            lclExpHolder.push(mapRec)
          } else if (mapRec['typeOfService'] === 'SeaImpTransactions_CSL' || mapRec['typeOfService'] === 'SeaImpTransactions_LCL') {
            mapRec.volume = mapRec.hawbCbm;
            lclImpHolder.push(mapRec)
          } else {
            mapRec.volume = mapRec.customsNumberCount || 0
            otherHolder.push(mapRec)
          }
        }

        let volumePerformance: any = {
          airImp: this.rawDataProcessor(airImpHolder),
          airExp: this.rawDataProcessor(airExpHolder),
          fclImp: this.rawDataProcessor(fclImpHolder),
          fclExp: this.rawDataProcessor(fclExpHolder),
          lclExp: this.rawDataProcessor(lclExpHolder),
          lclImp: this.rawDataProcessor(lclImpHolder),
          otherService: this.rawDataProcessor(otherHolder),
          totalProfit: 0,
          totalRevenue: 0
        }

        const airImp: Array<any> = volumePerformance['airImp'] || []
        const airExp: Array<any> = volumePerformance['airExp'] || []
        const fclExp: Array<any> = volumePerformance['fclExp'] || []
        const fclImp: Array<any> = volumePerformance['fclImp'] || []
        const lclExp: Array<any> = volumePerformance['lclExp'] || []
        const lclImp: Array<any> = volumePerformance['lclImp'] || []
        const otherService: Array<any> = volumePerformance['otherService'] || []

        // Calculate total profit and revenue from all segments
        const allSegments = [...airImp, ...airExp, ...fclExp, ...fclImp, ...lclExp, ...lclImp, otherService];
        volumePerformance.totalProfit = allSegments
          .filter(record => record.keyAccountName !== 'Total')
          .reduce((sum, record) => {
            // Remove currency formatting and convert to number
            const profit = typeof record.profit === 'string'
              ? Number(record.profit.replace(/[^0-9.-]+/g, ''))
              : (record.profit || 0);
            return sum + profit;
          }, 0);

        volumePerformance.totalRevenue = allSegments
          .filter(record => record.keyAccountName !== 'Total')
          .reduce((sum, record) => {
            // Remove currency formatting and convert to number
            const revenue = typeof record.revenue === 'string'
              ? Number(record.revenue.replace(/[^0-9.-]+/g, ''))
              : (record.revenue || 0);
            return sum + revenue;
          }, 0);

        this.reportBean['volumePerformance'] = volumePerformance;
        this.markLoading(false);
        this.viewId = util.IDTracker.next();
        this.forceUpdate();
      })
      .withFail((response: server.BackendResponse) => {
        let messageError: string = response.error['message'] || 'An unexpected error occurred. Please try again later.';
        bs.dialogShow('Error',
          <div className="text-danger fw-bold text-center py-3 border-bottom">
            <FeatherIcon.AlertCircle className="mx-2" />{messageError}
          </div>,
          { backdrop: 'static', size: 'md' }
        );
        return;
      })
      .call()
  }

  onSave = () => {
    let { appContext } = this.props;
    appContext.createHttpBackendCall('PartnerReportService', 'saveSalemanKeyAccountReport', { report: this.reportBean })
      .withEntityOpNotification('commit', 'Sale Account Report')
      .withSuccessData((reportBeanInDb: any) => {
        this.reportBean = reportBeanInDb
        this.viewId = util.IDTracker.next();
        this.forceUpdate();
      })
      .call();
  }

  onViewAllReports = () => {
    let { pageContext, space } = this.props;
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {

      const onSelect = (appCtx: app.AppContext, pageCtx: app.PageContext, entity: any) => {

        let volumePerformance: any = typeof entity['volumePerformance'] === 'string'
          ? JSON.parse(entity['volumePerformance'])
          : (entity['volumePerformance'] || {});
        entity['volumePerformance'] = volumePerformance;

        let forecast: any = typeof entity['forecast'] === 'string'
          ? JSON.parse(entity['forecast'])
          : (entity['forecast'] || {});
        entity['forecast'] = forecast;

        let highlights: any = typeof entity['highlights'] === 'string'
          ? JSON.parse(entity['highlights'])
          : (entity['highlights'] || {});
        entity['highlights'] = highlights;

        pageCtx.back();
        this.reportBean = entity;
        this.forceUpdate();
      }

      return (
        <UISalemanKeyAccountReportList
          appContext={appCtx} pageContext={pageCtx} plugin={new UISalemanKeyAccountReportPlugin(space)}
          onSelect={onSelect} />
      )
    }
    pageContext.createPopupPage('saleman-account-report', "Saleman Account Reports", createAppPage, { size: 'xl', backdrop: 'static' });
  }

  onViewAllPerformance = () => {
    let { pageContext } = this.props;
    const { reportedDateFrom, reportedDateTo } = this.reportBean;
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <UIVolumeSalemanKeyAccountReportList
          appContext={appCtx} pageContext={pageCtx}
          plugin={new UIVolumeSalemanKeyAccountReportPlugin().withReportedDate(reportedDateFrom, reportedDateTo)} />
      )
    }
    pageContext.createPopupPage('performance', "Performance", createAppPage, { size: 'xl', backdrop: 'static' });
  }

  onNewReport = () => {
    this.initialReportBean();
    this.viewId = util.IDTracker.next();
    this.forceUpdate();
  }

  renderUIPerformance(type: 'FCL' | 'AIR' | 'LCL' | 'OTHER', title: string, records: Array<any> = []) {
    if (records.length === 0) return <></>
    const { appContext, pageContext } = this.props;
    return (
      <div className="mt-1">
        <UIPerformanceGridReport type={type} title={title}
          appContext={appContext} pageContext={pageContext} rawRecords={records} />
      </div>
    )
  }

  onModifyHighlights = (bean: any | Array<any>, _field: string, _oldVal: any, newVal: any) => {
    let highlights: any = this.reportBean['highlights'] || {}
    if (_field === 'newCustomers' || _field === 'expectedLeadsToWin') {
      let refEntities: Array<any> = Array.isArray(bean) ? bean : []
      highlights[_field] = refEntities;
    } else {
      highlights[_field] = newVal;
    }
    this.reportBean['highlights'] = highlights;
    this.viewId = util.IDTracker.next();
    this.forceUpdate();
  }

  onModifyForecast = (bean: any | Array<any>, _field: string, _oldVal: any, newVal: any) => {
    let forecast: any = this.reportBean['forecast'] || {}
    if (_field === 'partnersForecast') {
      let refEntities: Array<any> = Array.isArray(bean) ? bean : []
      forecast[_field] = refEntities;
    } else {
      forecast[_field] = newVal;
    }
    this.reportBean['forecast'] = forecast;
    this.viewId = util.IDTracker.next();
    this.forceUpdate();
  }

  render() {

    const cardStyle = {
      minHeight: '200px',
      maxHeight: '500px',
      overflow: 'auto'
    };

    const { appContext, pageContext } = this.props;
    const writeCap = pageContext.hasUserWriteCapability();
    const moderatorCap = pageContext.hasUserModeratorCapability();

    const volumePerformance: any = this.reportBean['volumePerformance'] || {}
    const airImp: Array<any> = volumePerformance['airImp'] || []
    const airExp: Array<any> = volumePerformance['airExp'] || []
    const fclExp: Array<any> = volumePerformance['fclExp'] || []
    const fclImp: Array<any> = volumePerformance['fclImp'] || []
    const lclExp: Array<any> = volumePerformance['lclExp'] || []
    const lclImp: Array<any> = volumePerformance['lclImp'] || []
    const otherService: Array<any> = volumePerformance['otherService'] || []

    let totalProfit: number = 0;
    let totalRevenue: number = 0;

    const allSegments = [...airImp, ...airExp, ...fclExp, ...fclImp, ...lclExp, ...lclImp, ...otherService];
    totalProfit = allSegments
      .filter(record => record.keyAccountName !== 'Total')
      .reduce((sum, record) => {
        const profit = typeof record.profit === 'string'
          ? Number(record.profit.replace(/[^0-9.-]+/g, ''))
          : (record.profit || 0);
        console.log(profit);

        return sum + profit;
      }, 0);

    totalRevenue = allSegments
      .filter(record => record.keyAccountName !== 'Total')
      .reduce((sum, record) => {
        const revenue = typeof record.revenue === 'string'
          ? Number(record.revenue.replace(/[^0-9.-]+/g, ''))
          : (record.revenue || 0);
        return sum + revenue;
      }, 0);

    let profitRate: number = totalProfit / totalRevenue || 0;
    let margin = util.text.formater.currency(profitRate * 100, 2)

    const highlights: any = this.reportBean['highlights'] || {}

    let newCustomers: Array<any> = highlights['newCustomers'] || [];
    let expectedLeadsToWin: Array<any> = highlights['expectedLeadsToWin'] || [];

    let forecast: any = this.reportBean['forecast'] || {}

    let reportCode: string = this.reportBean['code'] || `NEW`;
    const reportSalemanLabel: string = this.reportBean['salemanLabel'] || 'N/A'

    const isOwnerReport: boolean = this.reportBean['salemanAccountId'] === SESSION.getAccountId();

    const lightColor = '#D6EFD8';
    const borderColor = '#80AF81';

    const dateFilter = {
      label: this.dateFilterLabel,
      fromValue: this.reportBean.reportedDateFrom,
      toValue: this.reportBean.reportedDateTo
    };

    return (
      <bs.GreedyScrollable className='p-1'>
        <div className='flex-vbox'>

          <div className='flex-hbox flex-grow-0 w-100 justify-content-between align-items-center bg-white my-1 rounded-sm'>

            <div className='flex-hbox align-items-center justify-content-start mx-2 py-2'>
              <h5 className='mx-2' style={{ color: '#6c757d' }}>{`${reportSalemanLabel} Report`}</h5>
              <h6 className='px-2' style={{ color: '#6c757d' }}>{`(${reportCode})`}</h6>
            </div>

            <div className='flex-hbox align-items-center justify-content-end mx-2 gap-2'>
              <WQuickTimeRangeSelector appContext={appContext} pageContext={pageContext}
                initBean={dateFilter}
                onModify={(bean: any, _field: string, _oldVal: any, _newVal: any) => {
                  this.dateFilterLabel = bean.label;
                  this.reportBean.reportedDateFrom = bean.fromValue;
                  this.reportBean.reportedDateTo = bean.toValue;
                  this.loadData();
                }} />
              <bs.Button laf="secondary" outline size="sm" className="me-1 p-1" style={{ color: '#6c757d' }}
                onClick={this.onNewReport} hidden={bs.ScreenUtil.isMobileScreen() || !writeCap}>
                <FeatherIcon.Plus size={14} className="me-1" /> New Report
              </bs.Button>

              <bs.Button laf="secondary" outline size="sm" className="p-1" style={{ color: '#6c757d' }}
                onClick={this.onViewAllReports} hidden={bs.ScreenUtil.isMobileScreen() || !writeCap}>
                View all
                <FeatherIcon.ChevronRight size={14} className="ms-2" />
              </bs.Button>

            </div>

          </div>

          <div className="d-flex flex-wrap p-1 m-0" key={this.viewId}>
            <div className="col-12 col-md-6 mb-2 pe-md-2">
              <div className="h-100 border rounded-3 bg-white"
                style={{
                  transition: 'all 0.3s ease',
                  borderColor: borderColor
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
                  e.currentTarget.style.transform = 'translateY(-2px)';
                  e.currentTarget.style.backgroundColor = lightColor;
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.boxShadow = 'none';
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.backgroundColor = '#fff';
                }}>
                <bs.Card header="PERFORMANCE" className="h-100 border-0">
                  <div className="flex-vbox p-2" style={{
                    height: '100%',
                    minHeight: '350px',
                    maxHeight: '700px',
                  }}>
                    <div className="d-flex justify-content-end">
                      <bs.Button laf="secondary" outline size="sm" className="p-1" style={{ color: '#6c757d' }}
                        onClick={() => this.onViewAllPerformance()} hidden={bs.ScreenUtil.isMobileScreen() || !writeCap}>
                        View all
                        <FeatherIcon.ChevronRight size={14} />
                      </bs.Button>
                    </div>
                    <bs.GreedyScrollable className='p-1 flex-vbox flex-grow-1' style={{ width: '100%', height: '100%' }}>

                      {this.renderUIPerformance('AIR', 'Air Export', airExp)}
                      {this.renderUIPerformance('AIR', 'Air Import', airImp)}
                      {this.renderUIPerformance('FCL', 'FCL Export', fclExp)}
                      {this.renderUIPerformance('FCL', 'FCL Import', fclImp)}
                      {this.renderUIPerformance('LCL', 'LCL Export', lclExp)}
                      {this.renderUIPerformance('LCL', 'LCL Import', lclImp)}
                      {this.renderUIPerformance('OTHER', 'Other Service', otherService)}

                    </bs.GreedyScrollable>

                    <div className="flex-grow-0 flex-shrink-0 mt-2 p-2 border-top">
                      <h6 className="fw-bold text-danger"><FeatherIcon.Package className="me-2" size={16} />TOTAL PERFORMANCE</h6>

                      <div className="ps-2 mt-1 flex-hbox justify-content-between align-items-center gap-2">

                        <div className="d-flex justify-content-between align-items-center mb-1 fs-9">
                          <span className="fw-bold me-2">GP:</span>
                          <span>{`${USD_CURR_FORMAT(totalProfit)} USD`}</span>
                        </div>

                        <div className="d-flex justify-content-between align-items-center mb-1 fs-9">
                          <span className="fw-bold me-2">REV:</span>
                          <span>{`${USD_CURR_FORMAT(totalRevenue)} USD`}</span>
                        </div>

                        <div className="d-flex justify-content-between align-items-center text-primary fs-9">
                          <span className="fw-bold me-2">Margin (% GP/ REV):</span>
                          <span>{`${margin}  %`}</span>
                        </div>

                      </div>
                    </div>

                  </div>

                </bs.Card>
              </div>
            </div>

            <div className="col-12 col-md-6 ps-md-2">
              <div className="h-100 border rounded-3 bg-white"
                style={{
                  transition: 'all 0.3s ease',
                  borderColor: borderColor
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
                  e.currentTarget.style.transform = 'translateY(-2px)';
                  e.currentTarget.style.backgroundColor = lightColor;
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.boxShadow = 'none';
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.backgroundColor = '#fff';
                }}>
                <bs.Card header="HIGHLIGHTS" className="h-100 border-0">
                  <div className="p-2 d-flex flex-column" style={{
                    height: '100%',
                    minHeight: '350px',
                    maxHeight: '700px',
                    overflow: 'auto'
                  }}>
                    <div className="flex-vbox" style={{ flex: 1 }}>
                      <div className="flex-vbox justify-content-start py-1 w-100">
                        <div className="flex-grow-0 flex-hbox justify-content-start align-items-center w-100">
                          <FeatherIcon.FileText className="me-2 text-primary" size={14} />
                          <span>Khách hàng mới</span>
                        </div>
                        <div className="flex-vbox">
                          <BBRefMultiBFSOnePartner beanIdField='bfsonePartnerCode' beanLabelField='name' partnerType='Customer'
                            appContext={appContext} pageContext={pageContext} placeholder="Enter your customers..." className='w-100'
                            placement="bottom-start" offset={[0, 5]} bean={newCustomers} minWidth={400}
                            onPostUpdate={(_inputUI, bean, _selectOpt, _userInput) => this.onModifyHighlights(bean, 'newCustomers', null, _selectOpt)} />
                        </div>
                      </div>

                      <div className="flex-vbox justify-content-start py-1 w-100">
                        <div className="flex-grow-0 flex-hbox justify-content-start align-items-center w-100">
                          <FeatherIcon.Calendar className="me-2 text-info" size={14} />
                          <span>Khách hàng tiềm năng, dự kiến win (next month)</span>
                        </div>
                        <div className="flex-vbox">
                          <BBRefMultiCustomerLead beanIdField='code' beanLabelField='name'
                            appContext={appContext} pageContext={pageContext} placeholder="Enter your lead..." className='w-100'
                            placement="bottom-start" offset={[0, 5]} bean={expectedLeadsToWin} minWidth={400}
                            onPostUpdate={(_inputUI, bean, _selectOpt, _userInput) => this.onModifyHighlights(bean, 'expectedLeadsToWin', null, _selectOpt)}
                            refCustomerLeadBy={'code'} />
                        </div>
                      </div>

                      <bs.CssTooltip position='bottom-left' width={400} offset={{ x: -405, y: -100 }}>
                        <bs.CssTooltipToggle className='flex-vbox justify-content-start py-1 w-100'>
                          <div className='flex-grow-0 flex-hbox justify-content-start align-items-center w-100'>
                            <FeatherIcon.Users className="me-2 text-success" size={14} />
                            <span>Đánh giá thị trường</span>
                          </div>
                          <input.BBTextField bean={highlights} field="marketInformation" style={{ height: '5rem' }}
                            onInputChange={this.onModifyHighlights} />
                        </bs.CssTooltipToggle>
                        <bs.CssTooltipContent className="d-flex flex-column rounded">
                          <div className="tooltip-header mb-2">
                            <span className="tooltip-title">Đánh giá thị trường:</span>
                          </div>
                          <ul className="mb-2 ps-3">
                            {(highlights['marketInformation'] || '...').split('\n').map((line: string, i: number) => (
                              <li key={i}>{line}</li>
                            ))}
                          </ul>
                        </bs.CssTooltipContent>
                      </bs.CssTooltip>

                      <bs.CssTooltip position='bottom-left' width={400} offset={{ x: -405, y: -100 }}>
                        <bs.CssTooltipToggle className='flex-vbox justify-content-start py-1 w-100'>
                          <div className='flex-grow-0 flex-hbox justify-content-start align-items-center w-100'>
                            <FeatherIcon.Star className="me-2 text-warning" size={14} />
                            <span>Các vấn đề phát sinh</span>
                          </div>
                          <input.BBTextField bean={highlights} field="reportedIssues" style={{ height: '5rem' }}
                            onInputChange={this.onModifyHighlights} />
                        </bs.CssTooltipToggle>
                        <bs.CssTooltipContent className="d-flex flex-column rounded">
                          <div className="tooltip-header mb-2">
                            <span className="tooltip-title">Các vấn đề phát sinh:</span>
                          </div>
                          <ul className="mb-2 ps-3">
                            {(highlights['reportedIssues'] || '...').split('\n').map((line: string, i: number) => (
                              <li key={i}>{line}</li>
                            ))}
                          </ul>
                        </bs.CssTooltipContent>
                      </bs.CssTooltip>
                    </div>
                  </div>
                </bs.Card>
              </div>
            </div>

            <div className="col-12 col-md-6 mb-3 pe-md-2">
              <div className="h-100 border rounded-3 bg-white"
                style={{
                  transition: 'all 0.3s ease',
                  borderColor: borderColor
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
                  e.currentTarget.style.transform = 'translateY(-2px)';
                  e.currentTarget.style.backgroundColor = lightColor;
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.boxShadow = 'none';
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.backgroundColor = '#fff';
                }}>
                <bs.Card header="FORECAST" className="h-100 border-0">
                  <div className="p-2 d-flex flex-column" style={{
                    minHeight: '200px',
                    maxHeight: '500px',
                    overflow: 'auto'
                  }}>
                    <input.BBTextField bean={forecast} field="forecast" style={{ height: 180 }}
                      onInputChange={this.onModifyForecast} />
                  </div>
                </bs.Card>
              </div>
            </div>

            <div className="col-12 col-md-6 mb-3 ps-md-2">
              <div className="h-100 border rounded-3 bg-white"
                style={{
                  transition: 'all 0.3s ease',
                  borderColor: borderColor
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
                  e.currentTarget.style.transform = 'translateY(-2px)';
                  e.currentTarget.style.backgroundColor = lightColor;
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.boxShadow = 'none';
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.backgroundColor = '#fff';
                }}>
                <bs.Card header="SUGGESTION/ REQUEST" className="h-100 border-0">
                  <div className="p-2 d-flex flex-column" style={{
                    minHeight: '200px',
                    maxHeight: '500px',
                    overflow: 'auto'
                  }}>
                    <input.BBTextField bean={this.reportBean} field="suggestionOrRequest" style={{ height: 180 }}
                      placeholder='Any suggestion/ request for improvement?' />
                  </div>
                </bs.Card>
              </div>
            </div>
          </div>

          <bs.Toolbar size='sm' className='border p-1 m-1' hide={!writeCap || (!isOwnerReport && !moderatorCap)}>
            <entity.WButtonEntityWrite
              appContext={appContext} pageContext={pageContext}
              label={'Save'} onClick={this.onSave} />
          </bs.Toolbar>

        </div>
      </bs.GreedyScrollable>
    )
  }
}