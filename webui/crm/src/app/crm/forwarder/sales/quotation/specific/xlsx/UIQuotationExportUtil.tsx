import { entity } from '@datatp-ui/lib';
import { app as lgc_app } from '@datatp-ui/logistics';

import { AdditionalChargeTarget } from 'app/crm/forwarder/price';
import { CELL_STYLES } from './StyleHelper';
import { createHeaderRow, createIntroRow, createTitleRow, formatDate, } from './XLSXUtils';
import { GeneralInformation, IGeneralInformation } from './models/GeneralInformation';
import { ContainerType, ContainerTypeUnit } from 'app/crm/forwarder/common/ContainerTypeUtil';

import _settings = lgc_app.logistics.settings;
import TransportationTool = _settings.TransportationTool;
import TransportationMode = _settings.TransportationMode;
import getAbbreviation = _settings.getAbbreviation;

export const MAX_COLUMN: number = 10;

export type Header = {
  name: string;
  colIdx: number;
  mappingTo: string;
  dataType?: 'currency' | 'date';
}

export type QuoteExportRequest = {
  issuedDate: string;
  quotationNo: string;
  inquiry: any;
  quoteSections: any[];
  maxColumnIdx: number;
}

export class SpecificQuotationExportProcessor {
  isMultipleRouting: boolean = false;
  rows: entity.XLSXRow[] = [];
  inquiry: any = {};
  request: QuoteExportRequest;

  containerTypes: ContainerType[] = []
  containerQtyMap: Record<string, number> = {}
  totalQuoteCurrMap: Record<string, number> = {}

  indexMap: any = { 1: 'I', 2: 'II', 3: 'III', 4: 'IV', 5: 'V', 6: 'VI', 7: 'VII' }
  currentIdx: number = 1;
  maxColumn: number = MAX_COLUMN;

  constructor(quotation: any, isMultipleRouting: boolean = false, clientType: 'CUSTOMER' | 'AGENT' = 'CUSTOMER') {
    this.isMultipleRouting = isMultipleRouting;
    this.inquiry = quotation['inquiry'] || {};
    this.containerQtyMap = {}
    this.totalQuoteCurrMap = {};

    let containers = quotation['inquiry']['containers'] || [];
    for (let cont of containers) {
      let containerType: string = (cont['containerType'] || '').trim();
      let type: ContainerType | undefined = ContainerTypeUnit.match(containerType);
      if (type) {
        this.containerTypes.push(type)
        this.containerQtyMap[type.name] = cont['quantity'] || 0;
      }
    }

    this.currentIdx = 1;
    let computeMaxCol = this.containerTypes.length + 6;
    if (isMultipleRouting) computeMaxCol += 3; // for POL, POD, Final Dest column
    this.maxColumn = Math.max(MAX_COLUMN, computeMaxCol);

    const mode: TransportationMode = this.inquiry.mode;
    let quotationTitle = 'QUOTATION';
    if (TransportationTool.isSea(mode) || TransportationTool.isAir(mode)) {
      quotationTitle = getAbbreviation(mode) + ' ' + this.inquiry.purpose + ' ' + 'QUOTATION';
    }

    let pickupAddress = this.inquiry.pickupAddress;
    let deliveryAddress = this.inquiry.deliveryAddress;

    const quotationNo = quotation.code || '';

    const generalInfoData: IGeneralInformation = {
      header: {
        title: quotationTitle,
        quotationNo,
        date: new Date()
      },
      contactInfo: {
        company: this.inquiry.clientLabel,
        attention: this.inquiry.attention
      },
      shipmentLocation: {
        fromLocation: this.inquiry.fromLocationLabel,
        toLocation: this.inquiry.finalDestination || this.inquiry.toLocationLabel,
        pickupAddress,
        deliveryAddress
      },
      cargo: {
        commodity: this.inquiry.descOfGoods,
        cargoReadyDate: this.inquiry.cargoReadyDate,
        grossWeightKg: this.inquiry.grossWeightKg,
        volumeInfo: this.inquiry.containerTypes,
        cargos: this.inquiry.cargos,
        hsCode: this.getHsCodeSummary(this.inquiry.commodities)
      },
      transportationMode: this.inquiry.mode,
      incoterms: this.inquiry.incoterms,
      truckDetails: TransportationTool.isTruck(this.inquiry.mode) ? {
        deliveryLocation: deliveryAddress,
        pickupLocation: pickupAddress,
        commodity: this.inquiry.commodity
      } : undefined
    };

    const generalInfo = new GeneralInformation(generalInfoData, this.maxColumn, this.getIndex());
    this.rows.push(...generalInfo.generateRows());

    const displayType = clientType === 'AGENT' ? 'partner' : 'customer';

    let intro = `On behalf of Bee Logistics, we would like to extend our sincere thanks to you, our valued ${displayType}. We are pleased to present our rates and services as detailed below:`
    let copyrightNotice = 'Copyright by BEE LOGISTICS CORPORATION. All Rights Reserved';
    this.rows.push(createIntroRow(intro, this.maxColumn));
    this.request = this.processQuotation(quotation);
    this.rows.push(createIntroRow(copyrightNotice, this.maxColumn));
  }

  private getIndex(): string {
    let value = this.indexMap[this.currentIdx];
    this.currentIdx++;
    return value;
  }

  private processQuotation(quotation: any): QuoteExportRequest {

    const { inquiry, code: quotationNo, issuedDate } = quotation;
    const mode: TransportationMode = inquiry.mode;
    if (TransportationTool.isAir(mode)) {
      this.processAirFreight(quotation, this.rows);
      this.processDomesticQuote(quotation, this.rows)
    } else if (TransportationTool.isSeaLCL(mode)) {
      this.processLCLFreight(quotation, this.rows);
      this.processDomesticQuote(quotation, this.rows)
    } else if (TransportationTool.isSeaFCL(mode)) {
      this.processFCLFreight(quotation, this.rows);
      this.processDomesticQuote(quotation, this.rows)
    } else {
      // bs.dialogShow('Message',
      //   <div className="text-danger text-center p-2">
      //     This quotation format is not supported at the moment. Please try a different format.
      //   </div>
      // );
    }

    let totalRow: entity.XLSXRow = {
      dataType: 'complex',
      rowHeight: 2,
      cells: [
        {
          value: `TOTAL CHARGES OF SHIMENT (II to ${this.indexMap[this.currentIdx - 1]})`,
          colStart: 0,
          style: CELL_STYLES.BOLD_RED
        },
        {
          value: 'USD',
          colStart: 1,
          style: CELL_STYLES.BOLD_RED_CENTER
        },

        {
          value: '',
          colStart: 2,
          colEnd: 4,
          style: CELL_STYLES.CENTER
        },
        {
          value: this.totalQuoteCurrMap['USD'] || '',
          colStart: 5,
          dataType: 'currency',
          style: CELL_STYLES.BOLD_RED_CENTER
        },
        {
          value: '',
          colStart: 6,
          colEnd: this.maxColumn,
          style: {
            border: true,
          },
        },
      ]
    }

    if (this.containerTypes.length === 0) {
      this.rows.push({
        dataType: 'complex', rowHeight: 2,
        cells: [
          {
            value: ``,
            colStart: 0,
            colEnd: this.maxColumn,
            style: CELL_STYLES.BASE
          }
        ]
      })
      this.rows.push(totalRow)
    }

    this.createLocalTariff(this.rows);
    let indexValue = this.getIndex();
    this.rows.push(createTitleRow(`${indexValue}. Note`, this.maxColumn));
    this.rows.push({
      dataType: 'complex',
      rowHeight: 6,
      cells: [{
        value: inquiry['note'] || '',
        colEnd: this.maxColumn,
        style: CELL_STYLES.WRAP_TEXT,
      }]
    });

    return {
      quotationNo,
      issuedDate,
      inquiry,
      quoteSections: this.rows,
      maxColumnIdx: this.maxColumn,
    };
  }

  private createLocalTariff(rows: entity.XLSXRow[]) {
    let indexValue = this.getIndex();
    this.rows.push(createTitleRow(`${indexValue}.Local tariff`, this.maxColumn));
    let headerRow: entity.XLSXRow = {
      dataType: 'complex',
      rowspan: 1,
      rowHeight: 2,
      cells: [
        {
          value: `DESCRIPTION`,
          colStart: 0,
          rowspan: 1,
          style: { ...CELL_STYLES.BOLD, bgColor: 'LIGHT_ORANGE' }
        },
        {
          value: 'THC (per CTNR)',
          colStart: 1,
          colEnd: 4,
          style: CELL_STYLES.BOLD_CENTER_LIGHT_ORANGE
        },
        {
          value: `20'DC`,
          colStart: 1,
          style: CELL_STYLES.BOLD_CENTER_LIGHT_ORANGE
        },
        {
          value: `40'DC/HC`,
          colStart: 2,
          style: CELL_STYLES.BOLD_CENTER_LIGHT_ORANGE
        },
        {
          value: `20'RF`,
          colStart: 3,
          style: CELL_STYLES.BOLD_CENTER_LIGHT_ORANGE
        },
        {
          value: `40'RF`,
          colStart: 4,
          style: CELL_STYLES.BOLD_CENTER_LIGHT_ORANGE
        },

        {
          value: 'BILL \n(per BL)',
          rowspan: 1,
          colStart: 5,
          style: CELL_STYLES.BOLD_CENTER_LIGHT_ORANGE
        },
        {
          value: 'SEAL \n(per PCS)',
          rowspan: 1,
          colStart: 6,
          style: CELL_STYLES.BOLD_CENTER_LIGHT_ORANGE
        },
        {
          value: 'TELEX/SURRENDERED \n(per BL)',
          rowspan: 1,
          colStart: 7,
          style: CELL_STYLES.BOLD_CENTER_LIGHT_ORANGE
        },
        {
          value: 'AFS/AFR/ENS/AMS/ACI \n(For CN/JP/EU/US/CA routings per BL)',
          rowspan: 1,
          colStart: 8,
          style: CELL_STYLES.BOLD_CENTER_LIGHT_ORANGE
        },
        {
          value: 'HANDLING FEE \n(SHIPMENT)',
          rowspan: 1,
          colStart: 9,
          colEnd: this.maxColumn,
          style: CELL_STYLES.BOLD_CENTER_LIGHT_ORANGE
        },
      ]
    }

    this.rows.push(headerRow);
    let row: entity.XLSXRow = {
      dataType: 'complex',
      rowHeight: 2,
      cells: [
        {
          value: 'ALL LINES (Except SEALAND to CN/JP)',
          colStart: 0,
          style: CELL_STYLES.BASE
        },
        {
          value: '',
          colStart: 1,
          style: CELL_STYLES.BASE
        },
        {
          value: '',
          colStart: 2,
          style: CELL_STYLES.BASE
        },
        {
          value: '',
          colStart: 3,
          style: CELL_STYLES.BASE
        },
        {
          value: '',
          colStart: 4,
          style: CELL_STYLES.BASE
        },
        {
          value: '',
          colStart: 5,
          style: CELL_STYLES.BASE
        },
        {
          value: '',
          colStart: 6,
          style: CELL_STYLES.BASE
        },
        {
          value: '',
          colStart: 7,
          style: CELL_STYLES.BASE
        },
        {
          value: '',
          colStart: 8,
          style: CELL_STYLES.BASE
        },
        {
          value: '',
          colStart: 9,
          colEnd: this.maxColumn,
          style: CELL_STYLES.BASE
        },
      ]
    }
    this.rows.push(row);
    this.rows.push(this.createEmptyCellRow());
    this.rows.push(this.createEmptyCellRow());
  }

  private createEmptyCellRow(): entity.XLSXRow {
    let row: entity.XLSXRow = {
      dataType: 'complex',
      rowHeight: 2,
      cells: [
        {
          value: '',
          colStart: 0,
          style: CELL_STYLES.BASE
        },
        {
          value: '',
          colStart: 1,
          style: CELL_STYLES.BASE
        },
        {
          value: '',
          colStart: 2,
          style: CELL_STYLES.BASE
        },
        {
          value: '',
          colStart: 3,
          style: CELL_STYLES.BASE
        },
        {
          value: '',
          colStart: 4,
          style: CELL_STYLES.BASE
        },
        {
          value: '',
          colStart: 5,
          style: CELL_STYLES.BASE
        },
        {
          value: '',
          colStart: 6,
          style: CELL_STYLES.BASE
        },
        {
          value: '',
          colStart: 7,
          style: CELL_STYLES.BASE
        },
        {
          value: '',
          colStart: 8,
          style: CELL_STYLES.BASE
        },
        {
          value: '',
          colStart: 9,
          colEnd: this.maxColumn,
          style: CELL_STYLES.BASE
        },
      ]
    }
    return row;
  }

  private processDomesticQuote(quotation: any, rows: entity.XLSXRow[]) {
    let ccQuote: any = quotation['customQuote'] || {}
    let domesticLocalCharges: any[] = [...ccQuote['localCharges'] || []];

    let originDomesticRecords: any[] = domesticLocalCharges.filter(sel => sel['target'] === AdditionalChargeTarget.ORIGIN)
    if (originDomesticRecords.length > 0) {
      let indexValue = this.getIndex();
      rows.push(createTitleRow(`${indexValue}. Domestics charge in origin`, this.maxColumn));

      if (this.containerTypes.length > 0) {
        const containerHeaders: string[] = this.containerTypes.map(sel => sel.label);
        const headers = ['COST', 'Currency', ...containerHeaders, 'B/L', 'Remarks']
        rows.push(createHeaderRow(headers, this.maxColumn));
        this.addLocalChargeRow(rows, originDomesticRecords, indexValue, this.containerTypes);
      } else {
        rows.push(this.createLocalChargeHeaderRow());
        this.addLocalChargeRow(rows, originDomesticRecords, indexValue, this.containerTypes);
      }
    }

    let destDomesticRecords: any[] = domesticLocalCharges.filter(sel => sel['target'] === AdditionalChargeTarget.DESTINATION)
    if (destDomesticRecords.length > 0) {
      let indexValue = this.getIndex();
      rows.push(createTitleRow(`${indexValue}. Domestics charge in destination`, this.maxColumn));
      if (this.containerTypes.length > 0) {
        const containerHeaders: string[] = this.containerTypes.map(sel => sel.label);
        const headers = ['COST', 'Currency', ...containerHeaders, 'B/L', 'Remarks']
        rows.push(createHeaderRow(headers, this.maxColumn));
        this.addLocalChargeRow(rows, destDomesticRecords, indexValue, this.containerTypes);
      } else {
        rows.push(this.createLocalChargeHeaderRow());
        this.addLocalChargeRow(rows, destDomesticRecords, indexValue, this.containerTypes);
      }
    }
  }

  private createAirQuoteRow(quote: any): entity.XLSXRow {
    let validity: string = '';
    if (quote['validity']) {
      validity = formatDate(quote['validity']);
    }

    let transitTime = quote['transitTime'];
    let transitPort = quote['transitPort'];
    let transitDisplay = '';
    if (transitTime && transitPort) {
      transitDisplay = `${transitTime} / ${transitPort}`;
    } else {
      transitDisplay = transitTime || transitPort || '';
    }

    let qty = this.inquiry['chargeableWeight'] || this.inquiry['grossWeightKg'] || 0;
    let price = (quote['priceGroup']['selectedPrice'] || 0)


    let dataRow: entity.XLSXRow = {
      dataType: 'complex',
      rowHeight: 2,
      cells: [
        {
          value: `${quote['carrierLabel'] || ''}`,
          colStart: 0,
          style: CELL_STYLES.BASE
        },
        {
          value: quote['currency'],
          colStart: 1,
          style: CELL_STYLES.CENTER,
        },
        {
          value: 'KGS',
          colStart: 2,
          style: CELL_STYLES.CENTER,
        },
        {
          value: price,
          colStart: 3,
          dataType: 'currency',
          style: CELL_STYLES.CENTER,
        },
        {
          value: qty,
          colStart: 4,
          dataType: 'currency',
          style: CELL_STYLES.CENTER,
        },
        {
          value: price,
          colStart: 5,
          dataType: 'currency',
          style: CELL_STYLES.CENTER,
        },
        {
          value: transitDisplay,
          colStart: 6,
          style: CELL_STYLES.WRAP_TEXT,
        },
        {
          value: validity,
          dataType: 'date',
          colStart: 7,
          style: CELL_STYLES.CENTER,
        },
        {
          value: quote['note'] || '',
          colStart: 8,
          colEnd: this.maxColumn,
          style: CELL_STYLES.WRAP_TEXT,
        },
      ]
    }
    return dataRow;
  }

  private createLCLQuoteRow(quote: any): entity.XLSXRow {
    let validity: string = '';
    if (quote['validity']) {
      validity = formatDate(quote['validity']);
    }

    let qty = this.inquiry['chargeableVolume'] || this.inquiry['volumeCbm'] || 0;

    let transitTime = quote['transitTime'];
    let transitPort = quote['transitPort'];
    let transitDisplay = '';
    if (transitTime && transitPort) {
      transitDisplay = `${transitTime} / ${transitPort}`;
    } else {
      transitDisplay = transitTime || transitPort || '';
    }

    let dataRow: entity.XLSXRow = {
      dataType: 'complex',
      rowHeight: 2,
      cells: [
        {
          value: quote['carrierLabel'] || '',
          colStart: 0,
          style: CELL_STYLES.BASE
        },
        {
          value: quote['currency'],
          colStart: 1,
          style: CELL_STYLES.CENTER,
        },
        {
          value: 'CBM',
          colStart: 2,
          style: CELL_STYLES.CENTER,
        },
        {
          value: quote['priceGroup']['selectedPrice'] || 0,
          colStart: 3,
          dataType: 'currency',
          style: CELL_STYLES.CENTER,
        },
        {
          value: qty,
          colStart: 4,
          dataType: 'currency',
          style: CELL_STYLES.CENTER,
        },
        {
          value: (quote['priceGroup']['selectedPrice'] || 0) * qty,
          colStart: 5,
          dataType: 'currency',
          style: CELL_STYLES.CENTER,
        },
        {
          value: transitDisplay,
          colStart: 6,
          style: CELL_STYLES.WRAP_TEXT,
        },
        {
          value: validity,
          colStart: 7,
          dataType: 'date',
          style: CELL_STYLES.CENTER,
        },
        {
          value: quote['note'] || '',
          colStart: 8,
          colEnd: this.maxColumn,
          style: CELL_STYLES.WRAP_TEXT,
        },
      ]
    }
    return dataRow;
  }

  private processFCLFreight(quotation: any, rows: entity.XLSXRow[]): void {
    const { inquiry, seaQuotesSelector = [] } = quotation;
    let indexValue = this.getIndex();

    let title = this.isMultipleRouting
      ? `${indexValue}. Ocean Freight`
      : `${indexValue}. Ocean Freight ${inquiry.fromLocationCode || 'POL'} - ${inquiry.toLocationCode || 'POD'}`;

    rows.push(createTitleRow(title, this.maxColumn));

    const hasFinalDestination = seaQuotesSelector.every((quote: any) =>
      quote.finalDestination !== undefined && quote.finalDestination !== null && quote.finalDestination !== ''
    );

    const containerHeaders: string[] = this.containerTypes.map(sel => sel.label);
    const headers = [
      'Carrier',
      ...(this.isMultipleRouting ? [
        'POL',
        'POD',
        ...(hasFinalDestination ? ['Final Dest'] : [])
      ] : []),
      'Currency',
      ...containerHeaders,
      'T/T (days)',
      'Validity',
      'Remarks'
    ];

    rows.push(createHeaderRow(headers, this.maxColumn));

    const localCharges: any[] = [];

    let totalFreight: number = 0;
    let indexOfTotal = 5;

    seaQuotesSelector.forEach((quote: any) => {

      let totalEachFreight: number = 0;

      let priceGroup: any = quote['priceGroup'] || {};

      let containerCells: any[] = [];
      for (let i = 0; i < this.containerTypes.length; i++) {
        let cont = this.containerTypes[i];
        let fclLevel: string | undefined = cont.toFCLPriceLevel();
        if (fclLevel) {
          let priceVal = priceGroup[fclLevel] || 0
          totalEachFreight += priceVal * this.containerQtyMap[cont.name]
          containerCells.push({
            value: priceVal,
            colStart: i + 2,
            dataType: 'currency',
            style: CELL_STYLES.CENTER,
          })
        }
      }

      totalFreight += totalEachFreight;

      let validity: string = '';
      if (quote['validity']) {
        validity = formatDate(quote['validity']);
      }

      indexOfTotal = containerCells.length + 2;

      let transitTime = quote['transitTime'];
      let transitPort = quote['transitPort'];
      let transitDisplay = '';
      if (transitTime && transitPort) {
        transitDisplay = `${transitTime} / ${transitPort}`;
      } else {
        transitDisplay = transitTime || transitPort || '';
      }

      let dataRow: entity.XLSXRow = {
        dataType: 'complex',
        rowHeight: 2,
        cells: [
          {
            value: quote['carrierLabel'],
            colStart: 0,
            style: CELL_STYLES.BASE
          },
          ...(this.isMultipleRouting ? [
            {
              value: quote['fromLocationLabel'] || '',
              colStart: 1,
              style: CELL_STYLES.BASE,
            },
            {
              value: quote['toLocationLabel'] || '',
              colStart: 2,
              style: CELL_STYLES.BASE,
            },
            ...(hasFinalDestination ? [{
              value: quote['finalDestination'] || '',
              colStart: 3,
              style: CELL_STYLES.BASE,
            }] : [])
          ] : []),
          {
            value: quote['currency'],
            colStart: this.isMultipleRouting ? (hasFinalDestination ? 4 : 3) : 1,
            style: CELL_STYLES.CENTER,
          },
          ...containerCells.map((cell, index) => ({
            ...cell,
            colStart: (this.isMultipleRouting ? (hasFinalDestination ? 5 : 4) : 2) + index
          })),
          {
            value: transitDisplay,
            colStart: indexOfTotal + (this.isMultipleRouting ? (hasFinalDestination ? 3 : 2) : 0),
            style: CELL_STYLES.WRAP_TEXT,
          },
          {
            value: validity,
            dataType: 'date',
            colStart: indexOfTotal + (this.isMultipleRouting ? (hasFinalDestination ? 4 : 3) : 1),
            style: CELL_STYLES.CENTER,
          },
          {
            value: quote['note'] || '',
            colStart: indexOfTotal + (this.isMultipleRouting ? (hasFinalDestination ? 5 : 4) : 2),
            colEnd: this.maxColumn,
            style: CELL_STYLES.WRAP_TEXT,
          }
        ]
      }

      rows.push(dataRow);
      localCharges.push(...(quote.localCharges || []));
    });

    if (seaQuotesSelector.length === 1) {
      let totalRow: entity.XLSXRow = {
        dataType: 'complex',
        rowHeight: 2,
        cells: [
          {
            value: `TOTAL (${indexValue})`,
            colStart: 0,
            style: CELL_STYLES.BOLD
          },
          {
            value: 'USD',
            colStart: 1,
            style: CELL_STYLES.CENTER
          },
          {
            value: 'per shipment ',
            colStart: 2,
            style: CELL_STYLES.CENTER
          },
          {
            value: 3 === indexOfTotal ? totalFreight : '',
            colStart: 3,
            dataType: 'currency',
            style: CELL_STYLES.BOLD_CENTER
          },
          {
            value: 4 === indexOfTotal ? totalFreight : '',
            colStart: 4,
            dataType: 'currency',
            style: CELL_STYLES.BOLD_CENTER
          },
          {
            value: 5 === indexOfTotal ? totalFreight : '',
            colStart: 5,
            dataType: 'currency',
            style: CELL_STYLES.BOLD_CENTER
          },
          {
            value: '',
            colStart: 6,
            colEnd: this.maxColumn,
            style: {
              border: true,
            },
          },
        ]
      }
      // rows.push(totalRow);
    }

    this.totalQuoteCurrMap['USD'] = (this.totalQuoteCurrMap['USD'] || 0) + totalFreight;
    this.processLocalCharges(localCharges, rows, 'FCL', this.containerTypes);
  }

  private processLCLFreight(quotation: any, rows: entity.XLSXRow[]): void {
    const { inquiry, seaQuotesSelector = [] } = quotation;

    let indexValue = this.getIndex();
    let title = `${indexValue}.Ocean Freight ${inquiry.fromLocationCode || 'POL'} - ${inquiry.toLocationCode || 'POD'}`;
    rows.push(createTitleRow(title, this.maxColumn));

    const headers = ['Carrier', 'Currency', 'Unit', 'Unit Price', 'Quantity', 'TOTAL', 'T/T (days)', 'Validity', 'Remarks'];
    rows.push(createHeaderRow(headers, this.maxColumn));

    const localCharges: any[] = [];

    let qty: number = this.inquiry['chargeableVolume'] || this.inquiry['volumeCbm'] || 0;

    let totalFreight: number = 0;
    seaQuotesSelector.forEach((quote: any) => {
      totalFreight += (quote['priceGroup']['selectedPrice'] || 0) * qty;
      localCharges.push(...(quote.localCharges || []));
      rows.push(this.createLCLQuoteRow(quote));
    });
    if (seaQuotesSelector.length === 1) rows.push(this.createTotalRow(`TOTAL (${indexValue})`, totalFreight));
    this.processLocalCharges(localCharges, rows, 'LCL');
  }

  private processAirFreight(quotation: any, rows: entity.XLSXRow[]): void {
    const { inquiry, airQuotesSelector = [] } = quotation;
    let indexValue = this.getIndex();
    let title = `${indexValue}.Air Freight ${inquiry.fromLocationCode || 'AOL'} - ${inquiry.toLocationCode || 'AOD'}`;
    rows.push(createTitleRow(title, this.maxColumn));

    const headers = ['AirLine', 'Currency', 'Unit', 'Unit Price', 'Quantity', 'TOTAL', 'T/T (days)', 'Validity', 'Remarks'];
    rows.push(createHeaderRow(headers, this.maxColumn));
    const localCharges: any[] = [];
    let totalFreight: number = 0;
    let qty = this.inquiry['chargeableWeight'] || this.inquiry['grossWeightKg'] || 0;

    airQuotesSelector.forEach((quote: any) => {
      totalFreight += (quote['priceGroup']['selectedPrice'] || 0) * qty
      localCharges.push(...(quote.localCharges || []));
      rows.push(this.createAirQuoteRow(quote));
    });
    if (airQuotesSelector.length === 1) rows.push(this.createTotalRow(`TOTAL (${indexValue})`, totalFreight));
    this.processLocalCharges(localCharges, rows, 'Air');
  }

  private processLocalCharges(charges: any[], rows: entity.XLSXRow[], type: 'FCL' | 'LCL' | 'Air', containers: ContainerType[] = []): void {

    const originCharges = charges.filter(c => c.target === AdditionalChargeTarget.ORIGIN);
    const destCharges = charges.filter(c => c.target === AdditionalChargeTarget.DESTINATION);

    if (originCharges.length) {
      let indexValue = this.getIndex();
      rows.push(createTitleRow(`${indexValue}.Local charge at POL / Origin`, this.maxColumn));
      if (containers.length > 0) {
        const containerHeaders: string[] = containers.map(sel => sel.label);
        const headers = ['COST', 'Currency', ...containerHeaders, 'B/L', 'Remarks']
        rows.push(createHeaderRow(headers, this.maxColumn));
        this.addLocalChargeRow(rows, originCharges, indexValue, containers, type);
      } else {
        rows.push(this.createLocalChargeHeaderRow());
        this.addLocalChargeRow(rows, originCharges, indexValue, containers, type);
      }
    }

    if (destCharges.length) {
      let indexValue = this.getIndex();
      rows.push(createTitleRow(`${indexValue}.Local charge at POD / Destination`, this.maxColumn));
      if (containers.length > 0) {
        const containerHeaders: string[] = this.containerTypes.map(sel => sel.label);
        const headers = ['COST', 'Currency', ...containerHeaders, 'B/L', 'Remarks']
        rows.push(createHeaderRow(headers, this.maxColumn));
        this.addLocalChargeRow(rows, destCharges, indexValue, this.containerTypes, type);
      } else {
        rows.push(this.createLocalChargeHeaderRow());
        this.addLocalChargeRow(rows, destCharges, indexValue, this.containerTypes, type);
      }
    }

  }

  private createLocalChargeHeaderRow(): entity.XLSXRow {
    const headers: string[] = ['COST', 'Currency', 'Unit', 'Unit Price', 'Quantity', 'TOTAL', 'Remarks'];
    let headerRow: entity.XLSXRow = {
      dataType: 'complex',
      rowHeight: 2,
      cells: []
    }
    for (let i = 0; i < headers.length; i++) {
      let header: any = headers[i];
      let cell: any = {
        value: header,
        colStart: i,
        colEnd: i === headers.length - 1 ? this.maxColumn : 0,
        style: i !== 0 ? { ...CELL_STYLES.BOLD_CENTER, bgColor: 'LIGHT_ORANGE' } : { ...CELL_STYLES.BOLD, bgColor: 'LIGHT_ORANGE' },
      }
      headerRow.cells.push(cell);
    }
    return headerRow;
  }

  private addLocalChargeRow(rows: entity.XLSXRow[], charges: any[], indexValue: string, containers: ContainerType[] = [], type: 'FCL' | 'LCL' | 'Air' | 'Domestic' = 'Domestic'): void {
    let total = 0;
    if (containers.length > 0) {
      for (let sel of charges) {
        let totalItem = 0;
        let unitPrice = sel['unitPrice'] || 0;
        // totalItem += unitPrice * (sel['quantity'] || 1)
        totalItem += unitPrice

        let containerCells: any[] = [];
        let quoteRate = sel['quoteRate'] || {};

        for (let i = 0; i < containers.length; i++) {
          let cont = containers[i];
          let amount = quoteRate[cont.label] || quoteRate[cont.name] || 0;
          totalItem += amount * this.containerQtyMap[cont.name];

          let cell = {
            value: amount,
            colStart: i + 2,
            dataType: 'currency',
            style: CELL_STYLES.CENTER
          }
          containerCells.push(cell)
        }

        total += totalItem

        let indexOfTotal = containerCells.length + 2;

        let dataRow: entity.XLSXRow = {
          dataType: 'complex',
          rowHeight: 2,
          cells: [
            {
              value: `${sel['name'] || ''}`,
              colStart: 0,
              style: CELL_STYLES.BASE
            },
            {
              value: sel['currency'],
              colStart: 1,
              style: CELL_STYLES.CENTER
            },
            ...containerCells,
            {
              value: unitPrice,
              dataType: 'currency',
              colStart: indexOfTotal,
              style: CELL_STYLES.CENTER
            },
            {
              value: sel['note'] || '',
              colStart: indexOfTotal + 1,
              colEnd: this.maxColumn,
              style: CELL_STYLES.WRAP_TEXT
            },
          ]
        }
        rows.push(dataRow)
      }
    } else {
      for (let sel of charges) {
        let unitPrice = sel['unitPrice'] || 0;
        // if (unitPrice === 0) continue;
        total += unitPrice;
        let dataRow: entity.XLSXRow = {
          dataType: 'complex',
          rowHeight: 2,
          cells: [
            {
              value: `${sel['name'] || ''}`,
              colStart: 0,
              style: CELL_STYLES.BASE
            },
            {
              value: sel['currency'],
              colStart: 1,
              style: CELL_STYLES.CENTER
            },
            {
              value: sel['unit'],
              colStart: 2,
              style: CELL_STYLES.CENTER
            },
            {
              value: unitPrice,
              colStart: 3,
              dataType: 'currency',
              style: CELL_STYLES.CENTER
            },
            {
              value: sel['quantity'] || 0,
              colStart: 4,
              dataType: 'currency',
              style: CELL_STYLES.CENTER
            },
            {
              value: (sel['quantity'] || 0) * unitPrice,
              colStart: 5,
              dataType: 'currency',
              style: CELL_STYLES.CENTER
            },
            {
              value: sel['note'] || '',
              colStart: 6,
              colEnd: this.maxColumn,
              style: CELL_STYLES.WRAP_TEXT
            },
          ]
        }
        rows.push(dataRow)
      }
    }
    if (type === 'LCL') {
      rows.push(this.createTotalRow(`SUB TOTAL (${indexValue})`, total));
      rows.push(this.createTaxRateRow(`TAX RATE`));
    }
    if (containers.length === 0) {
      rows.push(this.createTotalRow(`TOTAL (${indexValue})`, total));
    }
  }

  private createTaxRateRow(title: string): entity.XLSXRow {
    let taxRateRow: entity.XLSXRow = {
      dataType: 'complex',
      rowHeight: 2,
      cells: [
        {
          value: title,
          colStart: 0,
          style: CELL_STYLES.BOLD
        },
        {
          value: '%',
          colStart: 1,
          style: CELL_STYLES.CENTER
        },
        {
          value: '',
          colStart: 2,
          colEnd: 4,
          style: CELL_STYLES.CENTER
        },
        {
          value: '',
          dataType: 'currency',
          colStart: 5,
          style: CELL_STYLES.CENTER
        },
        {
          value: '',
          colStart: 6,
          colEnd: this.maxColumn,
          style: {
            border: true,
          },
        },
      ]
    }
    return taxRateRow;
  }

  private createTotalRow(title: string, value: number): entity.XLSXRow {
    this.totalQuoteCurrMap['USD'] = (this.totalQuoteCurrMap['USD'] || 0) + value;

    let totalRow: entity.XLSXRow = {
      dataType: 'complex',
      rowHeight: 2,
      cells: [
        {
          value: title,
          colStart: 0,
          style: CELL_STYLES.BOLD
        },
        {
          value: 'USD',
          colStart: 1,
          style: CELL_STYLES.CENTER
        },
        {
          value: '',
          colStart: 2,
          colEnd: 4,
          style: CELL_STYLES.CENTER
        },
        {
          value: value || 0,
          dataType: 'currency',
          colStart: 5,
          style: CELL_STYLES.BOLD_CENTER
        },
        {
          value: '',
          colStart: 6,
          colEnd: this.maxColumn,
          style: {
            border: true,
          },
        }
      ]
    }
    return totalRow;
  }

  private getHsCodeSummary(commodities: any[]): string {
    if (!commodities?.length) return "";
    return commodities
      .flatMap(commodity => (commodity.types || [])
        .map((type: any) => type.hsCode))
      .filter(Boolean)
      .join(", ");
  }

}
