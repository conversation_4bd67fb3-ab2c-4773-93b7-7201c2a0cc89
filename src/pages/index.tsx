import clsx from 'clsx';
import Link from '@docusaurus/Link';
import useDocusaurusContext from '@docusaurus/useDocusaurusContext';
import Layout from '@theme/Layout';
import HomepageFeatures from '@site/src/components';
import Heading from '@theme/Heading';

import styles from './index.module.css';

export default function Home(): JSX.Element {
  const { siteConfig } = useDocusaurusContext();
  return (
    <Layout title={`${siteConfig.title}`} description="Description will go into a meta tag in <head />">

      <header className={clsx('hero hero--primary', styles.heroBanner)}>
        <div className="container">
          <Heading as="h1" className="hero__title">
            {siteConfig.title}
          </Heading>
          <p className="hero__subtitle">{siteConfig.tagline}</p>
          <div className={styles.buttons}>
            <Link className="button button--secondary button--lg" to="/docs/shared/user/system">
              <PERSON><PERSON><PERSON> đầu
            </Link>
            <Link className="button button--outline button--lg button--secondary" to="https://beelogistics.cloud" style={{ marginLeft: '12px' }}>
              Trang web chính thức
            </Link>
          </div>
        </div>
      </header>

      <main>
        <HomepageFeatures />
      </main>

    </Layout>
  );
}
