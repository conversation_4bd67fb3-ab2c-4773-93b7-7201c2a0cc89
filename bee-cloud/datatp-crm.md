# I. THÔNG TIN CHUNG

### 1. người được phỏng vấn:
- `<PERSON><PERSON>`

- Thời gian triển khai
  `01/2024 -> current`

### 2. <PERSON><PERSON><PERSON> sự triển khai:
  - `<PERSON><PERSON>`
  - `<PERSON><PERSON>ờng An`
  - `<PERSON><PERSON>`
  - `<PERSON><PERSON><PERSON>n <PERSON>` đã thôi việc từ 06/2024

### 3. <PERSON><PERSON><PERSON> chức năng chính:

  - Module Pricing Tools: Quản lý giá cước vận chuyển (sea, air, logistics, trucking)
  - Module Sales: Quản lý quy trình bán <PERSON>àng (lead, inquiry, quotation, booking)
  - Dashboard: <PERSON> dõi hiệu suất và báo cáo KPI
  - Quản lý đối tác (Partner Management)
  - <PERSON> dõi hoạt động hàng ngày của nhân viên kinh doanh (Tasks Calender)

### 4. Phạm vi triển khai:
  - <PERSON><PERSON> thống được triển khai phục vụ nội bộ công ty
  - Số lượng người sử dụng theo thiết kế:
      * Toàn bộ nhân viên sales và pricing/ bộ phận backoffice khác.
  - Số lượng người sử dụng thực tế hiện tại:
      * 1074 người (dựa theo dữ liệu account được phân quyền vào CRM)

# II. CHỨC NĂNG VÀ QUY TRÌNH NGHIỆP VỤ
- Danh sách chi tiết các module:

###  1. Module Pricing Tools:
  - Quản lý bảng giá Sea FCL, Sea LCL, Air Transport, Trucking (Container / Truck/ CBT).
  - Tách bảng giá nhập/xuất theo mục đích sử dụng.
  - Template cho Local Charge.
  - Theo dõi công việc, tính kpi cho bộ phận Pricing dựa theo inquiry và số lượng đầu việc trên từng inquiry.

### 2. Module Sales:
  - Quản lý khách hàng tiềm năng (Lead Management).
  - Quản lý khách hàng (Customer Management).
  - Theo dõi yêu cầu báo giá (Inquiry).
  - Tạo và gửi báo giá (Quotation).
  - Chuyển đổi báo giá thành booking, api với hệ thống BFSOne.

###  3. Dashboard:
  - Company Pricing Dashboard
  - Sales Dashboard
  - Volume Performance by Department
  - Sales Activities Tracker

- Quy trình làm việc chính:
  1. Sales nhập inquiry vào hệ thống
  2. Hệ thống kiểm tra giá có sẵn hoặc gửi yêu cầu báo giá đến bộ phận pricing
  3. Tạo quotation và gửi cho khách hàng
  4. Xác nhận booking và đồng bộ với hệ thống OF1

- Liên kết với hệ thống CoreTP:
  - Sử dụng chung cơ sở dữ liệu về khách hàng, đối tác
  - Kế hoạch tách ra thành hệ thống độc lập với database riêng

III. KIẾN TRÚC VÀ CÔNG NGHỆ
### 1. Công nghệ sử dụng chính:
    - Backend:
        + Ngôn ngữ lập trình: Java 21, Groovy scripting language v4.0.20
        + Framework/thư viện: springframework v3.3.3
        + Kiến trúc hệ thống: Monolithic với các module tách biệt
    - Frontend:
        + Ngôn ngữ lập trình: JavaScript, HTML/CSS
        + Framework/thư viện: Reactjs V18.3.1, Bootstrap V5.3

### 2. Cơ sở dữ liệu:
- Database
  * PostgresQL 16.1

- Mô tả kiến trúc và cấu trúc CSDL?
  * Không có thiết kế tổng thể, chỉ có 1 số quy định chung về cách đặt tên bảng, tên trường, các trường cơ bản bắt buộc bảng nào cũng phải có như created_by, created_time, modified_by, modified_time, company_id, storage_state... Các tên bảng cùng module có prefix như lgc_sale_xxx, lgc_price_xxxx.
  * Khi có yêu cầu cho từng module, team mới thiết kế sơ bộ.
  * Team dùng chiến lược adative strategy, vừa làm vừa rút kinh nghiệm và có phát triển các công cụ hỗ trợ cho phép thay đổi linh hoạt bằng các cách như thêm bảng, xoá bảng, tạo lại các liên kết hay migrate dữ liệu.

### 3. Phân quyền:
- Sử dụng hệ thống phân quyền của DataTP Core
- Các vai trò: Admin, Moderator, Write, Read
- Scope dữ liệu: All, Company, Group, Owner.
- Cơ chế đăng nhập: Thông qua DataTP Core, kế hoạch chuyển sang Keycloak SSO

### 4. Sao lưu dữ liệu:
- Hiện tại sử dụng cơ chế sao lưu chung của hệ thống DataTP Core
- Kế hoạch xây dựng cơ chế sao lưu riêng khi tách hệ thống

Hạ tầng tài nguyên hệ thống:
- Chạy trên nền tảng cloud chung với DataTP Core
- Kế hoạch tách thành hệ thống độc lập

# IV. VẬN HÀNH VÀ KHAI THÁC
- Phần mềm đang được phát triển tích cực với các phiên bản mới hàng tháng
- Có tài liệu hướng dẫn sử dụng cho người dùng trên trang web `https://docs.beelogistics.cloud/`
- Đang xây dựng hệ thống giám sát và cảnh báo riêng

# V. HƯỚNG PHÁT TRIỂN
- Kế hoạch 3 tháng tới:
  + Tách dự án thành hệ thống độc lập với database riêng
  + Tích hợp với Keycloak SSO
  + Hoàn thiện các màn hình và chức năng module sales
  + Cải thiện thông báo lỗi từ server đến người dùng

- Kế hoạch 6 tháng tới:
  + Tối ưu kiến trúc hệ thống
  + Phát triển thêm các tính năng marketing
  + Tích hợp đầy đủ với hệ thống OF1

- Cải tiến hệ thống:
  + Cập nhật UI/UX theo tiêu chuẩn mới
  + Tối ưu hiệu suất truy vấn cơ sở dữ liệu

# VI. CÁC THÔNG TIN KHÁC
### 1. Phỏng vấn lập trình viên tham gia phát triển:
    - Thách thức trong việc xử lý dữ liệu từ nhiều nguồn và đồng bộ với hệ thống cũ.
    - Khó khăn trong việc thiết kế UI/UX đáp ứng nhu cầu của nhiều nhóm người dùng khác nhau.
    - Yêu cầu thay đổi thường xuyên từ người dùng.

### 2. Phỏng vấn người dùng:
    - Phần mềm sử dụng thường xuyên, mỗi ngày.
    - Phần mềm đáp ứng tốt nhu cầu quản lý giá và theo dõi quy trình bán hàng.
    - Cần cải thiện tốc độ và giao diện người dùng trên thiết bị di động.
    - Một số chức năng cần được đơn giản hóa quy trình.

