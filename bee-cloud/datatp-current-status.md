# Team DataTP

Thành Viên:
- <PERSON><PERSON><PERSON><PERSON> (7/2020 - current)
- <PERSON><PERSON><PERSON>
- Đàn
- ...
- <PERSON><PERSON><PERSON>(2021 - 2022)
- <PERSON><PERSON><PERSON> (2021 - 2023)

Quản lý: <PERSON><PERSON><PERSON><PERSON>.
Team Leader: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>.

# Các Dự Án <PERSON>ang <PERSON> Lý

## DataTP Forwarder Management

Người được phỏng vấn: `<PERSON><PERSON>`

I. THÔNG TIN CHUNG
1. Thời gian triển khai:
  - `06/2022 -> 11/2023`

2. <PERSON><PERSON><PERSON> sự triển khai:
  - `<PERSON><PERSON><PERSON><PERSON>`
  - `<PERSON><PERSON> Trung Hiếu`
  - `<PERSON><PERSON>à<PERSON>`
  - `<PERSON><PERSON>`

3. <PERSON><PERSON><PERSON> chức năng ch<PERSON>h (mô tả chi tiết các chức năng chính của hệ thống)
  - Quy trình vận hành, thay thế BFSOne.
  - <PERSON><PERSON><PERSON><PERSON> lý vận đơ<PERSON> (Master Bill, House Bill),
  - <PERSON><PERSON><PERSON><PERSON> lý Job file, bao gồm toàn bộ thông tin của 1 lô hàng.

4. Phạm vi triển khai:
- GD1: <PERSON><PERSON><PERSON> triển và triển cho công ty HPS chạy thử nghiệm.
- Số lượng người sử dụng:
  - Kỳ vọng: toàn bộ nhân sự khối vấn hàng (Docs/ Cus) của BEE.

II. CHỨC NĂNG VÀ QUY TRINH NGHIỆP VỤ

III. KIẾN TRÚC VÀ CÔNG NGHỆ

1. Công nghệ sử dụng chính:
  - Backend:
    + Ngôn ngữ lập trình: Java 21, Groovy
    + Framework/thư viện: Spring Boot, Spring Batch
    + Kiến trúc hệ thống: Monolithic với các module tách biệt
  - Frontend:
    + Ngôn ngữ lập trình: JavaScript, HTML/CSS
    + Framework/thư viện: React

2. Cơ sở dữ liệu:
  - PostgreSQL

3. Phân quyền:
  - Sử dụng hệ thống phân quyền của DataTP Core
  - Các vai trò: Admin, Moderator, Write, Read
  - Scope dữ liệu: All, Company, Group, Owner.
  - Cơ chế đăng nhập: Thông qua DataTP Core, kế hoạch chuyển sang Keycloak SSO

4. Sao lưu dữ liệu:
  - Hiện tại sử dụng cơ chế sao lưu chung của hệ thống DataTP Core
  - Kế hoạch xây dựng cơ chế sao lưu riêng khi tách hệ thống

5. Hạ tầng tài nguyên hệ thống:
  - Chạy trên nền tảng cloud chung với DataTP Core
  - Kế hoạch tách thành hệ thống độc lập

IV. VẬN HÀNH VÀ KHAI THÁC
  - Dự án dùng và không tiếp tục phát triển vì các lý do khách quan không liên quan đến phần mềm như:
    - Công ty HPS tách khỏi Bee.
    - Api, liên kết dữ liệu với phần mềm kế toán (Công ty đang dùng AV - phần mềm kế toán cũ)

V. HƯỚNG PHÁT TRIỂN
VI. CÁC THÔNG TIN KHÁC


## DataTP TMS

Người được phỏng vấn: Chiến

I. THÔNG TIN CHUNG
1. Thời gian triển khai
  - Giai giai đoạn 1 (Bắt đầu dự án - Phát triển cho BEESCS) : 02/2023 - 07/2023
  - Giai giai đoạn 2 : 02/2024 - Hiện tại
2. Nhân sự triển khai:
  - Nguyễn Tuấn Anh
  - Phạm Minh Chiến
  - Phùng Hoàn Quân

3. Các chức năng chính (mô tả chi tiết các chức năng chính của hệ thống)
  - Module TMSBill:
    + Quản lý kế hoạch của 1 lô hàng
  - Module Fleet:
    + Quản lý kế hoạch vận tại
    + Quản lý chuyến xe
    + Quản lý đội xe
   - Module VendorBill:
    + Quản lý bill của Thầu Phụ
  - Module TMSRouseUse:
    + Quản lý bill Round Use

4. Phạm vi triển khai:
- Hệ thống được triển khai ở đâu?
  - Giai đoạn 1: Triển khai cho BEESCS
  - Giai đoạn 2: Triển khai cho BEEHPH, BEEHAN
  - Giai đoạn hiện tại: Đang triển khai cho BEEHCM, BEEDAD, MARINE
- Số lượng người sử dụng (theo thiết kế và theo thực tế hiện tại)?
  - Bộ phận trucking (>30 người)
  - Bộ phận kê toán (>5 người)
  - Bộ phận sale (>5 người)

II. CHỨC NĂNG VÀ QUY TRINH NGHIỆP VỤ
- Danh sách đầy đủ các module, chức năng cho từng nhánh công việc
  1. Module TMSBill - Quản lý kế hoạch của 1 lô hàng :
    + Lên kế hoạch cho 1 lô hàng bao gồm: Thông tin khách hàng, Khối lượng hàng, Ngày giao hàng, Địa chỉ lấy trả hàng,...
    + Tạo file PDF biên bản giao hàng(POD)
    + Gửi Mail cho thầu phụ
    + Export data ra file excell
    + Tạo tempalte export data gửi thầu phụ
    + Tạo tempalte export data BFSOne
    + Tích đầu việc cần hoàn thành của 1 lô hàng
    + Báo cáo số lượng lô hàng được mở và số lượng đầu việc đã hoàn thành theo từng Account
    + API push các lô hàng sang các đơn vị thầu phụ có sử dụng API(Hiện tại đang push chéo các công ty nội bộ)
  2. Module Fleet - Quản lý kế hoạch vận tại (Module:Fleet) :
    + Lên kế hoạch xe, xếp xe, ghép chuyến cho các kế hoạch được gửi từ TMSBill
    + Hệ thống gửi tin nhắn zalo, mail khi nhập đủ thông tin xe cho các đối tượng tham gia vào lô hàng
    + Định vị lô hàng, ghi nhận lịch sử di chuyển các xe có hỗ trợ kết nối định vị
    + Quản thông tin thầu phụ, thông tin xe, thông tin lái xe
    + Hệ thống tích hợp định vị xe từ nhiều nhà cung cấp định vị khác nhau
  3. Module VendorBill - Quản lý bill của Thầu Phụ :
    + Export/Import bill cho thầu phụ (Bản kê), so sánh đối chiếu giá cước thầu phụ input
    + Lưu trữ thông tin xe do thầu phụ cung cấp
  4. Module RoundUse: Quản lý bill Round Use
    + Nhập và lên kế hoách cho các lô hang round-use
- Mô tả các workflow
  1. TMSBill: Dữ liệu nguồn cho các module dưới kế thừa
    + Nhận yêu cầu vận chuyển hàng từ Sale hoặc khách hàng
    + Tạo kế hoạch mới hoặc kéo kế hoạch từ BFSOne về TMSBill và điền đủ thông tin
    + Xác định thầu phụ sẽ vận chuyển lô hàng và gửi mail + biên bản giao hàng(POD) cho thầu phụ mục tiêu
    + Theo dõi tiến độ vận tải lô hàng, nhập thông tin xe, cước nếu đó là các xe gọi ngoài(Các xe nội bộ điều vận sẽ nhập)
  2. Fleet:
    - Điều vận
      + Nhận các lô hàng TMSBill gửi cho thầu phụ nội bộ
      + Tách các lô hàng nếu lô hàng lớn vượt qua mức tải của xe, tối ưu hóa tuyến đường vận tải bằng các nhóm các lô hàng trên cùng tuyến vận tải lên 1 chuyến
      + Khóa các lô hàng đã chốt thông tin vận tải
    - Lái xe
      + Nhận các lô hàng điều vận đã khóa kế hoạch
      + Nhận chuyến xe khi bắt đầu chạy và kết thúc chuyến khi hoàn thành
  3. VendorBill
      + Thầu phụ nhận bản kê vận tải theo ngày bằng file excell hoặc trên hệ thống
      + Nhập giá và update lên hệ thống, đối chiếu giá thầu phụ nhập và Cus Trucking nhập trên TMSBill, nếu lệch thì 2 bên phải trao đổi và thông nhất lại giá
  4. RoundUse : Mục đích sử dụng lại CONT 1 cách hiệu quả
      + Giải thích cách hoạt động: Tận dụng CONT các lô hàng Nhâp/Xuất để tối ưu chi phí thuê CONT.
      VD: 1 lô hàng CONT 20DC nhập về cảng và vận chuyển tới Hải Dương, nếu hãng tàu A cho phép tái sử dụng CONT và hạn 3 ngày phải kéo trả lại hãng tàu thì khi đó BEE sẽ tận dụng để tìm kiếm các khách hàng ở Hải Dương có book CONT 20DC cho hãng tàu A vận tải để làm round-use, như vậy sẽ tiết kiệm được chi phí kéo CONT rỗng về cảng.
      Hoặc nếu có bãi để CONT thì có thể kéo về bãi và chờ các lô hàng phù hợp hoặc các lô hàng chuyển kho để vận tải
      + Hoạt động trên Phần Mềm:
      Tìm kiếm trên TMSBill đã tạo các lô hàng CONT chưa vận chuyển, các lô đủ điều kiện round-use sẽ yêu cầu người chụ trách nhiệm tms-bill đó cho cho phép round-use không (Vì tùy theo CONT đang mượn của hãng tàu có được phép round-use không)
      + Lưu thông tin nâng/hạ CONT với các CONT kéo về bãi của BEE
- Liên kết với hệ thống CoreTP thế nào
  - Sử dụng chung cơ sở dữ liệu về khách hàng, đối tác, locaion

III. KIẾN TRÚC VÀ CÔNG NGHỆ
Công nghệ sử dụng chính:
- Backend:
    + Ngôn ngữ lập trình: Java 21, Groovy
    + Framework/thư viện: Spring Boot, Spring Batch
    + Kiến trúc hệ thống: (Monolithic, Microservices, SOA...?)
- Frontend:
    + Ngôn ngữ lập trình: JavaScript, HTML/CSS
    + Framework/thư viện: React
Cơ sở dữ liệu:
- PostgreSQL
Phân quyền:
- Các loại người dùng : quản trị, nhân viên...
- Các cơ chế phân quyền
- Cơ chế đăng nhập: (tài khoản độc lập, SSO, LDAP...?)
- Cơ chế xác thực: 2FA, OTP?
Sao lưu dữ liệu
- Cơ chế sao lưu, backup dữ liệu, khả năng
- Hình thức sao lưu (local, cloud...) sao lưu tự động
Hạ tầng tài nguyên hệ thống:
- Loại hạ tầng của hệ thống: Cloud
- Hệ điều hành:
- Hạ tầng mạng
    + Ảo hóa, mạng vật lý
    + Tốc độ, băng thông giữa các thiết bị
- Bảo mật hệ thống
    + Có các hình thức ngăn chặn truy cập trái phép (firewall, IPS/IDS, VPN, filter...)
    + Có phân hệ thống thành các vùng riêng biệt (VLAN, VPN, DMZ..?)
    + Chống tấn công DDoS?

IV. VẬN HÀNH VÀ KHAI THÁC
- Phần mềm vẫn được duy trì phát triển và maintain thế nào
- Có tài liệu hướng dẫn sử dụng và vận hành chưa?
- Hệ thống giám sát, cảnh bảo hoạt động thế nào (tài nguyên hệ thống, lưu lượng, bảo mật, log...)

V. HƯỚNG PHÁT TRIỂN
- Kế hoach 3 tháng tới là tách dự án, dùng db riêng giao diện độc lập. Có thể kết hợp với Keycloadk SSO để chạy độc lập hoàn toàn.
- Tích hợp các hệ thống khác?
- Tối ưu kiến trúc hệ thống hiện tại?
- Tối ưu các phần tử, các điểm nghẽn...?
- Loại bỏ hoặc viết lại 1 số code không đạt chuẩn.?

VI. CÁC THÔNG TIN KHÁC
1. Phỏng vấn lập trình viên tham gia phát triển
- Trong quá trình triển khai, bạn gặp phải những vấn đề gì rủi ro cho thành công của dự án

2. Phỏng vấn người dùng sử dụng, có đáp ứng được nhu cầu...
- Đã sử dụng phần mềm với tàn xuất thế nào?
- Phần mềm có phù hợp với thực tế không?
- Phần mềm có những vấn đề gì cần hỗ trợ


## DataTP OKR/KPI/Job Tracking

Người được phỏng vấn: Nhật.

I. THÔNG TIN CHUNG
1. Thời gian triển khai

2. Nhân sự triển khai:

3. Các chức năng chính (mô tả chi tiết các chức năng chính của hệ thống)

4. Phạm vi triển khai:
- Hệ thống được triển khai ở đâu?
- Số lượng người sử dụng (theo thiết kế và theo thực tế hiện tại)?

II. CHỨC NĂNG VÀ QUY TRINH NGHIỆP VỤ
- Danh sách đầy đủ các module, chức năng cho từng nhánh công việc
- Mô tả các workflow
- Liên kết với hệ thống CoreTP thế nào

III. KIẾN TRÚC VÀ CÔNG NGHỆ
Công nghệ sử dụng chính:
- Backend:
    + Ngôn ngữ lập trình:
    + Framework/thư viện:
    + Kiến trúc hệ thống: (Monolithic, Microservices, SOA...?)
- Frontend:
    + Ngôn ngữ lập trình:
    + Framework/thư viện:
Cơ sở dữ liệu:
- (MySQL, PostgreSQL, MongoDB...?)
- Mô tả kiến trúc và cấu trúc CSDL?
Phân quyền:
- Các loại người dùng (quản trị, người sử dụng, nhân viên...)
- Các cơ chế phân quyền
- Cơ chế đăng nhập: (tài khoản độc lập, SSO, LDAP...?)
- Cơ chế xác thực: 2FA, OTP?
Sao lưu dữ liệu
- Cơ chế sao lưu, backup dữ liệu, khả năng
- Hình thức sao lưu (local, cloud...) sao lưu tự động
Hạ tầng tài nguyên hệ thống:
- Loại hạ tầng của hệ thống: Cloud
- Hệ điều hành:
- Hạ tầng mạng
    + Ảo hóa, mạng vật lý
    + Tốc độ, băng thông giữa các thiết bị
- Bảo mật hệ thống
    + Có các hình thức ngăn chặn truy cập trái phép (firewall, IPS/IDS, VPN, filter...)
    + Có phân hệ thống thành các vùng riêng biệt (VLAN, VPN, DMZ..?)
    + Chống tấn công DDoS?

IV. VẬN HÀNH VÀ KHAI THÁC
- Phần mềm vẫn được duy trì phát triển và maintain thế nào
- Có tài liệu hướng dẫn sử dụng và vận hành chưa?
- Hệ thống giám sát, cảnh bảo hoạt động thế nào (tài nguyên hệ thống, lưu lượng, bảo mật, log...)

V. HƯỚNG PHÁT TRIỂN
- Kế hoach 3 tháng tới là tách dự án, dùng db riêng giao diện độc lập. Có thể kết hợp với Keycloadk SSO để chạy độc lập hoàn toàn.
- Tích hợp các hệ thống khác?
- Tối ưu kiến trúc hệ thống hiện tại?
- Tối ưu các phần tử, các điểm nghẽn...?
- Loại bỏ hoặc viết lại 1 số code không đạt chuẩn.?

VI. CÁC THÔNG TIN KHÁC
1. Phỏng vấn lập trình viên tham gia phát triển
- Trong quá trình triển khai, bạn gặp phải những vấn đề gì rủi ro cho thành công của dự án

2. Phỏng vấn người dùng sử dụng, có đáp ứng được nhu cầu...
- Đã sử dụng phần mềm với tàn xuất thế nào?
- Phần mềm có phù hợp với thực tế không?
- Phần mềm có những vấn đề gì cần hỗ trợ

## DataTP Document IE

Người được phỏng vấn: Đạt Lương

I. THÔNG TIN CHUNG
1. Thời gian triển khai
- `03/2025 -> NOW`

2. Nhân sự triển khai:
- `Lương Văn Đạt`: Người thực hiện chính
- `Phùng Hoàng Quân`: Người hỗ trợ một số chức năng

3. Các chức năng chính (mô tả chi tiết các chức năng chính của hệ thống)
- Thực hiện trích xuất dữ liệu trên hóa đơn hoặc biên lai.
  + Giúp verify thông tin trên bản kê của thầu phụ với các bộ hóa đơn
  + Tổng hợp thông tin thành bản kê cho kế toán

4. Phạm vi triển khai:
- Hệ thống hiện tại được triển khai cho kế toán thuộc `BeeHPH, BeeCORP, BeeHCM`
- Số lượng người sử dụng kỳ vọng là toàn bộ kế toán thuộc các cty trên

II. CHỨC NĂNG VÀ QUY TRINH NGHIỆP VỤ
- Module **document**:
  + Upload, download các bộ chứng từ để thực hiện trích rút
  + Thống kê số lượng thông tin trích rút được
  + Phân chia loại hóa đơn để gọi service phù hợp
  + Verify dữ liệu trích rút được với bản kê
  + Tạo bản kê từ dữ liệu trích rút
- Mô tả các workflow
  + Người dùng tạo bộ chứng từ
  + Upload các file lên `pdf, excel` của hóa đơn hay bản kê
  + Đánh dấu **type** của file đó, để chọn đúng service trích rút có thể là `invoice, receipt, fcl, lcl`
  + Bắt đầu quá trình trích rút, file đó sẽ được gửi về **back-end** và nhận lại dữ liệu
  + Tùy theo nghiệp vụ thực hiện verify với bản kê hoặc tạo bản kê
- Liên kết với hệ thống MSA
  + File sau khi nhận được ở **back-end** sẽ gửi qua tiếp hệ thống MSA triển khai bằng python để xử lý trích rút
  + **Back-end** nhận dữ liệu dưới dạng **Json** và parse thành các model java và lưu vào database

III. KIẾN TRÚC VÀ CÔNG NGHỆ
Công nghệ sử dụng chính:
- Backend:
    + Ngôn ngữ lập trình: `Java, Python`
    + Framework/thư viện:
      + Java framework: `Spring`
      + Python library: `pdfplumber, pandas, tabulate, tornado, aiohttp`
- Frontend:
    + Ngôn ngữ lập trình: `Javascript`
    + Framework/thư viện: `React`
- Cơ sở dữ liệu: `PostgresSQL`
- Phân quyền:
  - Người dùng chủ yếu là kế toán được phân quyền
  - Phân quyền theo công ty và người dùng
  - Cơ chế đăng nhập: (tài khoản độc lập, SSO, LDAP...?)
  - Cơ chế xác thực: 2FA, OTP?
- Sao lưu dữ liệu
  - Cơ chế sao lưu, backup dữ liệu, khả năng
  - Hình thức sao lưu (local, cloud...) sao lưu tự động
- Hạ tầng tài nguyên hệ thống:
  - Loại hạ tầng của hệ thống: Cloud
  - Hệ điều hành:
  - Hạ tầng mạng
      + Ảo hóa, mạng vật lý
      + Tốc độ, băng thông giữa các thiết bị
- Bảo mật hệ thống
    + Có các hình thức ngăn chặn truy cập trái phép (firewall, IPS/IDS, VPN, filter...)
    + Có phân hệ thống thành các vùng riêng biệt (VLAN, VPN, DMZ..?)
    + Chống tấn công DDoS?

IV. VẬN HÀNH VÀ KHAI THÁC
- Phần mềm vẫn được duy trì phát triển và maintain thế nào
- Có tài liệu hướng dẫn sử dụng và vận hành chưa?
- Hệ thống giám sát, cảnh bảo hoạt động thế nào (tài nguyên hệ thống, lưu lượng, bảo mật, log...)

V. HƯỚNG PHÁT TRIỂN
- Kế hoach 3 tháng tới là tách dự án, dùng db riêng giao diện độc lập. Có thể kết hợp với Keycloadk SSO và datatp-core để chạy độc lập hoàn toàn.
- Kế hoạch 6 tháng tới là Tích hợp với phần mềm CMS strapi để cung cấp đầy đủ dịch vụ CMS và document management.
- Tích hợp các hệ thống khác?
- Tối ưu kiến trúc hệ thống hiện tại?
- Tối ưu các phần tử, các điểm nghẽn...?
- Loại bỏ hoặc viết lại 1 số code không đạt chuẩn.?

VI. CÁC THÔNG TIN KHÁC
1. Phỏng vấn lập trình viên tham gia phát triển
- Hệ thông nhận dạng ban đầu sử dụng các thư viện java để trích rút nhưng có nhược điểm là không đọc được file pdf nên cần chuyển file pdf sang excel để tận dụng các thư viện đọc excel có sẵn.
Tuy nhiên cách này làm phát sinh nhiều bước và kết quả cũng chưa đạt kỳ vọng
- Sau này qua quá trình tìm hiểu và thử nghiệm thấy được thư viện của python có tiềm năng rất lớn cho việc trích rút trực tiêp file pdf.
Sau đó kết hợp với việc xây dựng các chức năng nhận dạng từ, cụm từ hay gán nhãn ký tự đã thu được kết quả rất tốt và được chính thức sử dụng
- Cùng với thành công của thư viện python bắt đầu tích hợp nhận dạng thêm biên lai, tương lai triển khai sử dụng ocr để nhận dạng ảnh

2. Phỏng vấn người dùng sử dụng, có đáp ứng được nhu cầu...
- Phần mềm được kế toán sử dụng và có phản hồi rất tốt
- Phần mềm phù hợp với nhu cầu tự động hóa quá trình nhập tay của kế toán



## DataTP Spreadsheet

Người được phỏng vấn: Lê Ngọc Đàn + Chiến.

I. THÔNG TIN CHUNG
1. Thời gian triển khai: `16/10/2022`

2. Nhân sự triển khai:
  - `Lê Ngọc Đàn`
  - `Lê Quang Nhật`
  - `Phạm Minh Chiến`

3. Các chức năng chính (mô tả chi tiết các chức năng chính của hệ thống)

4. Phạm vi triển khai:
- Hệ thống được triển khai ở đâu?
- Số lượng người sử dụng (theo thiết kế và theo thực tế hiện tại)?

II. CHỨC NĂNG VÀ QUY TRINH NGHIỆP VỤ
- Danh sách đầy đủ các module, chức năng cho từng nhánh công việc
- Mô tả các workflow
- Liên kết với hệ thống CoreTP thế nào

III. KIẾN TRÚC VÀ CÔNG NGHỆ
Công nghệ sử dụng chính:
- Backend:
    + Ngôn ngữ lập trình:
    + Framework/thư viện:
    + Kiến trúc hệ thống: (Monolithic, Microservices, SOA...?)
- Frontend:
    + Ngôn ngữ lập trình:
    + Framework/thư viện:
Cơ sở dữ liệu:
- (MySQL, PostgreSQL, MongoDB...?)
- Mô tả kiến trúc và cấu trúc CSDL?
Phân quyền:
- Các loại người dùng (quản trị, người sử dụng, nhân viên...)
- Các cơ chế phân quyền
- Cơ chế đăng nhập: (tài khoản độc lập, SSO, LDAP...?)
- Cơ chế xác thực: 2FA, OTP?
Sao lưu dữ liệu
- Cơ chế sao lưu, backup dữ liệu, khả năng
- Hình thức sao lưu (local, cloud...) sao lưu tự động
Hạ tầng tài nguyên hệ thống:
- Loại hạ tầng của hệ thống: Cloud
- Hệ điều hành:
- Hạ tầng mạng
    + Ảo hóa, mạng vật lý
    + Tốc độ, băng thông giữa các thiết bị
- Bảo mật hệ thống
    + Có các hình thức ngăn chặn truy cập trái phép (firewall, IPS/IDS, VPN, filter...)
    + Có phân hệ thống thành các vùng riêng biệt (VLAN, VPN, DMZ..?)
    + Chống tấn công DDoS?

IV. VẬN HÀNH VÀ KHAI THÁC
- Phần mềm vẫn được duy trì phát triển và maintain thế nào
- Có tài liệu hướng dẫn sử dụng và vận hành chưa?
- Hệ thống giám sát, cảnh bảo hoạt động thế nào (tài nguyên hệ thống, lưu lượng, bảo mật, log...)

V. HƯỚNG PHÁT TRIỂN
- Kế hoach 3 tháng tới là tách dự án, dùng db riêng giao diện độc lập. Có thể kết hợp với Keycloadk SSO để chạy độc lập hoàn toàn.
- Tích hợp các hệ thống khác?
- Tối ưu kiến trúc hệ thống hiện tại?
- Tối ưu các phần tử, các điểm nghẽn...?
- Loại bỏ hoặc viết lại 1 số code không đạt chuẩn.?

VI. CÁC THÔNG TIN KHÁC
1. Phỏng vấn lập trình viên tham gia phát triển
- Trong quá trình triển khai, bạn gặp phải những vấn đề gì rủi ro cho thành công của dự án
2. Phỏng vấn người dùng sử dụng, có đáp ứng được nhu cầu...
- Đã sử dụng phần mềm với tàn xuất thế nào?
- Phần mềm có phù hợp với thực tế không?
- Phần mềm có những vấn đề gì cần hỗ trợ

## Bee Legacy

Người được phỏng vấn: `Lê Ngọc Đàn`

I. THÔNG TIN CHUNG
1. Thời gian triển khai: `25/04/2025 -> Now`
2. Nhân sự triển khai: `Nguyễn Tuấn Anh` , `Lê Ngọc Đàn`

3. Các chức năng chính (mô tả chi tiết các chức năng chính của hệ thống)
>  - Tích hợp và đồng bộ dữ liệu từ hệ thống BFS One (hệ thống vận hành, kế toán)
>  - Quản lý tỷ giá hối đoái liên quan đến các loại báo cáo cho saleman trên hệ thống BFSOne (Exchange Rate)
>  - Quản lý thông tin vận đơn (House Bill), thu thập từ nhiều bảng trên database BFSOne (integrated_housebill)
>  - Quản lý thông tin partners tích hợp (integrated_partner)
>  - Quản lý thông tin unit (đơn vị).

4. Phạm vi triển khai:
  - Hệ thống được triển khai nội bộ để hỗ trợ chuyển đổi và tích hợp dữ liệu
  - Hệ thống phụ trợ, hỗ trợ tích hợp chức năng với hệ thống khác.
  - Người sử dụng chủ yếu là IT, Dev.

II. CHỨC NĂNG VÀ QUY TRINH NGHIỆP VỤ
  - Các model chính:
    - Exchange Rate: Đồng bộ và quản lý tỷ giá hối đoái từ BFS One
    - House Bill: Tích hợp và quản lý thông tin vận đơn, thu thập từ nhiều bảng, nhiều nguồn ở BFSOne.
    - Integrated Partner: Quản lý thông tin đối tác, thu thập đẩy đủ thông tin partner (từ BFSOne, CRM)
    - Settings Unit: Quản lý cài đặt đơn vị
  - Tạo ra các bảng chung, thu thập dữ liệu từ nhiều nguồn, tối ưu cho mục đích báo cáo, trích rút dữ liệu thường sử dụng một cách nhanh chóng.
  - Các bảng này sẽ được sync tự động từ BFSOne, DataTP qua hình thức pull hoặc push (DataTP cập qua api)

III. KIẾN TRÚC VÀ CÔNG NGHỆ
Công nghệ sử dụng chính:
  - Backend:
    - Ngôn ngữ lập trình: Python 3.10
    - Framework/thư viện: SQLAlchemy, pyodbc, click, PyYAML
    - Kiến trúc hệ thống: Module-based monolithic
  - Frontend: Không có giao diện người dùng riêng, tích hợp vào hệ thống khác
  - Cơ sở dữ liệu:
    - PostgreSQL cho lưu trữ dữ liệu chính
    - Kết nối đến Microsoft SQL Server (BFS One) để đồng bộ dữ liệu
    - Cấu trúc CSDL gồm các bảng chính: integrated_housebill, exchange_rate, integrated_partner, settings_unit
  - Phân quyền:
    - Không có hệ thống phân quyền riêng, phụ thuộc vào hệ thống chính
    - Truy cập cơ sở dữ liệu dựa trên tài khoản riêng được cấu hình trong file config.yaml
  - Sao lưu dữ liệu:
    - Sử dụng cơ chế sao lưu chung của hệ thống PostgreSQL
  - Hạ tầng tài nguyên hệ thống:
    - Chạy trên cùng hạ tầng với các dịch vụ khác của DataTP
    - Hệ điều hành: Linux Debian
    - Chia sẻ tài nguyên với các module khác của hệ thống

IV. VẬN HÀNH VÀ KHAI THÁC
  - Phần mềm được duy trì để hỗ trợ việc chuyển đổi, tập trung dữ liệu từ các hệ thống để tạo ra bộ dữ liệu chuẩn để các hệ thống cùng dùng chung.
  - Có các unit test để đảm bảo tính ổn định của hệ thống

V. HƯỚNG PHÁT TRIỂN
  - Kế hoạch 3 tháng tới:
    - Tích hợp với `Keycloak SSO` để quản lý xác thực
    - Làm việc với anh Quý để việc api với BFSOne mượt mà hơn.
    - Đối soát chéo với các hệ thống, triển khai một phần, đảm bảo dữ liệu là chính xác, đầy đủ.
  - Kế hoạch 6 tháng tới:
    - Nâng cấp khả năng đồng bộ dữ liệu từ nhiều nguồn
    - Tối ưu hiệu suất truy vấn và xử lý dữ liệu lớn
    - Thay thế các loại, màn hình báo cáo cho hệ thống DataTP Cloud, một số màn hình của BFSOne để tối ưu trải nghiệm cho người dùng.

  - Cải tiến kiến trúc:
    - Tăng độ phủ của unit test
    - Cải thiện hệ thống quản lý lỗi và log (grafana, prometheus, ..)

VI. CÁC THÔNG TIN KHÁC

  1. Phỏng vấn lập trình viên:
    - Thách thức chính là việc đồng bộ dữ liệu real time từ hệ thống BFS One (cũ) sang hệ thống mới.
    - Vấn đề về định dạng dữ liệu không đồng nhất giữa các hệ thống (dữ liệu phải phù hợp và thuận tiện cho cả BFSOne và DataTP Cloud)

  2. Phỏng vấn người dùng:
    - Hệ thống giúp giảm thời gian xử lý báo cáo từ nhiều nguồn dữ liệu
    - Cần cải thiện tốc độ đồng bộ dữ liệu và tính ổn định


## Beesco

Người được phỏng vấn: Nguyễn Tuấn Anh.

I. THÔNG TIN CHUNG
1. Thời gian triển khai

2. Nhân sự triển khai:

3. Các chức năng chính (mô tả chi tiết các chức năng chính của hệ thống)

4. Phạm vi triển khai:
- Hệ thống được triển khai ở đâu?
- Số lượng người sử dụng (theo thiết kế và theo thực tế hiện tại)?

II. CHỨC NĂNG VÀ QUY TRINH NGHIỆP VỤ
- Danh sách đầy đủ các module, chức năng cho từng nhánh công việc
- Mô tả các workflow
- Liên kết với hệ thống CoreTP thế nào

III. KIẾN TRÚC VÀ CÔNG NGHỆ
Công nghệ sử dụng chính:
- Backend:
    + Ngôn ngữ lập trình:
    + Framework/thư viện:
    + Kiến trúc hệ thống: (Monolithic, Microservices, SOA...?)
- Frontend:
    + Ngôn ngữ lập trình:
    + Framework/thư viện:
Cơ sở dữ liệu:
- (MySQL, PostgreSQL, MongoDB...?)
- Mô tả kiến trúc và cấu trúc CSDL?
Phân quyền:
- Các loại người dùng (quản trị, người sử dụng, nhân viên...)
- Các cơ chế phân quyền
- Cơ chế đăng nhập: (tài khoản độc lập, SSO, LDAP...?)
- Cơ chế xác thực: 2FA, OTP?
Sao lưu dữ liệu
- Cơ chế sao lưu, backup dữ liệu, khả năng
- Hình thức sao lưu (local, cloud...) sao lưu tự động
Hạ tầng tài nguyên hệ thống:
- Loại hạ tầng của hệ thống: Cloud
- Hệ điều hành:
- Hạ tầng mạng
    + Ảo hóa, mạng vật lý
    + Tốc độ, băng thông giữa các thiết bị
- Bảo mật hệ thống
    + Có các hình thức ngăn chặn truy cập trái phép (firewall, IPS/IDS, VPN, filter...)
    + Có phân hệ thống thành các vùng riêng biệt (VLAN, VPN, DMZ..?)
    + Chống tấn công DDoS?

IV. VẬN HÀNH VÀ KHAI THÁC
- Phần mềm vẫn được duy trì phát triển và maintain thế nào
- Có tài liệu hướng dẫn sử dụng và vận hành chưa?
- Hệ thống giám sát, cảnh bảo hoạt động thế nào (tài nguyên hệ thống, lưu lượng, bảo mật, log...)

V. HƯỚNG PHÁT TRIỂN
- Kế hoach 3 tháng tới là tách dự án, dùng db riêng giao diện độc lập. Có thể kết hợp với Keycloadk SSO để chạy độc lập hoàn toàn.
- Tích hợp các hệ thống khác?
- Tối ưu kiến trúc hệ thống hiện tại?
- Tối ưu các phần tử, các điểm nghẽn...?
- Loại bỏ hoặc viết lại 1 số code không đạt chuẩn.?

VI. CÁC THÔNG TIN KHÁC
1. Phỏng vấn lập trình viên tham gia phát triển
- Trong quá trình triển khai, bạn gặp phải những vấn đề gì rủi ro cho thành công của dự án
2. Phỏng vấn người dùng sử dụng, có đáp ứng được nhu cầu...
- Đã sử dụng phần mềm với tàn xuất thế nào?
- Phần mềm có phù hợp với thực tế không?
- Phần mềm có những vấn đề gì cần hỗ trợ