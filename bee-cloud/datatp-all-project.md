

# DataTP Core

## I. THÔNG TIN CHUNG

### 1. Ngườ<PERSON> được phỏng vấn: <PERSON><PERSON><PERSON><PERSON>.

### 2. Thời gian triển khai: 7/2020 - curent

### 3. <PERSON><PERSON><PERSON> sự triển khai:
- <PERSON><PERSON><PERSON><PERSON>h
- Đàn 
- Chiến
- Nhật
- Hiếu - đã thôi việc từ 2022
- Lâm  - đã thôi việc từ 2023

### 4. <PERSON><PERSON><PERSON> chứ<PERSON> năng chính:
- Là nền tảng chung của hệ thống (mô tả rõ hơn các phần dùng chung: chung database, chung hạ tầng...?)
- Nền tảng chung cho quản trị user, employees, công việc.
- <PERSON><PERSON><PERSON> thư viện, service dùng chung
- Các service giám sát monitor các activity người dùng, và các activities của hệ thống.

### 5. Phạm vi triển khai:
- <PERSON><PERSON> thống được triển khai ở đâu?
  * <PERSON><PERSON><PERSON> là nền tảng chung được sử dụng bởi các module kh<PERSON>c như <PERSON>M, TMS, OKR/KPI...
- Số lượng người sử dụng (theo thiết kế và theo thực tế hiện tại)?
  * Toàn bộ nhân viên văn  phòng tại HP sử dụng 
  * 1 phần nhân viên khối sale, cus của Hà Nội, Đà Nẵng, HCM.

## II. CHỨC NĂNG VÀ QUY TRINH NGHIỆP VỤ
- Danh sách đầy đủ các module, chức năng cho từng nhánh công việc
  * N/A
- Mô tả các workflow
  * N/A

## III. KIẾN TRÚC VÀ CÔNG NGHỆ

### 1. Công nghệ sử dụng chính:
- Backend:
  + Ngôn ngữ lập trình: Java v21, groovy scripting language v4.0.20
  + Framework/thư viện: springframework v3.3.3
  + Kiến trúc hệ thống: (Monolithic, Microservices, SOA...?)
- Frontend:
  + Ngôn ngữ lập trình: javascript, html/css,
  + Framework/thư viện: bootstrap V5.3, reactjs V18.3.1

### 2. Cơ sở dữ liệu:
- Database
  + PostgresQL 16.1
- Mô tả kiến trúc và cấu trúc CSDL?
  + Không có thiết kế tổng thể, chỉ có 1 số quy định chung về cách đặt tên bảng, tên trường, các trường cơ bản bắt buộc bảng nào cũng phải có như created_by, created_time, modified_by, modified_time, company_id, storage_state... Các tên bảng cùng module có prefix như account_xxx, hr_xxxx, tmx_xxxxx.
  + Khi có yêu cầu cho từng module, team mới thiết kế sơ bộ.
  + Team dùng chiến lược adative strategy, vừa làm vừa rút kinh nghiệm và có phát triển các công cụ hỗ trợ cho phép thay đổi linh hoạt bằng các cách như thêm bảng, xoá bảng, tạo lại các liên kết hay migrate dữ liệu.

### 3. Phân quyền:
- Các loại người dùng (quản trị, người sử dụng, nhân viên...)
  + Hiôn chỉ có Tuấn và Đàn có quyền truy cập vào máy chủ và DB để làm các việc như nâng cấp, cập nhật.
  + Chiến cũng được đào tạo và hướng dẫn làm việc này, nhưng không được giao quyền.
- Các cơ chế phân quyền
  + N/A
- Cơ chế đăng nhập: (tài khoản độc lập, SSO, LDAP...?)
  + Tài khoản đăng nhập máy chủ.
- Cơ chế xác thực: 2FA, OTP?
  + Máy chủ dùng cơ chế ssh + username/password.
  + Máy DB dùng cơ chế ssh + username/password +  Google Authenticator (2FA)

### 4. Sao lưu dữ liệu
- Cơ chế sao lưu, backup dữ liệu, khả năng
  + Hiện dữ liệu của hệ thống chưa lớn(khoàng 5GB dữ liệu nén), nên mỗi khi cập nhật, quản trị viên chạy backup script backup lại toàn bộ dữ liệu. Mất khoảng 6 - 8 phút) 
  + 3 bản backup mới nhất được giữ lại.
  + Ngoài ra đi kèm với backup thủ công. Dịch vụ VM có backup lại ổ cứng tự động các ngày 2 - 6 hàng đêm. 
- Hình thức sao lưu (local, cloud...) sao lưu tự động
  + Thủ công, bản sao để trên máy chủ

#### 5. Hạ tầng tài nguyên hệ thống:
- Loại hạ tầng của hệ thống: Cloud
  + Dùng chung máy ảo server 8 core, 16GB RAM, 100GB storage
  + Dùng chung máy ảo DB 4 core, 8GB RAM, 100GB storage
- Phần mềm platform: Linux Debian v12, database Postgresql v16.1, Kubernettes v1.33.2
- Hạ tầng mạng
  + Ảo hóa, mạng vật lý
  + Tốc độ, băng thông giữa các thiết bị
- Bảo mật hệ thống
  + Có các hình thức ngăn chặn truy cập trái phép (firewall, IPS/IDS, VPN, filter...)
  + Có phân hệ thống thành các vùng riêng biệt (VLAN, VPN, DMZ..?)
  + Chống tấn công DDoS?

## IV. VẬN HÀNH VÀ KHAI THÁC
- Phần mềm vẫn được duy trì phát triển và maintain
- Tất cả module quan trọng đang phát triển như CRM, TMS... đều phụ thuộc vào module này.
- Có tài liệu hướng dẫn sử dụng và vận hành chưa?
  + Phần này là phần core nên chỉ có các tài liệu hướng dẫn developer setup để phát triển.
  + Developer phải tự đọc code để hiểu hoặc đi hỏi.
- Hệ thống giám sát, cảnh bảo hoạt động thế nào (tài nguyên hệ thống, lưu lượng, bảo mật, log...)
  + Có phát triển hệ thống activity monitor, theo dõi các activity của người dùng theo từng tennant, khung giờ, service sử dụng...
  + Các activity được phân loại như người dùng, bot...
  + Dựa vào hệ thống log.

## V. HƯỚNG PHÁT TRIỂN
- Vì đây là phần core nên vấn bỏ nhiều công sức để duy trì, phát triển và nâng cấp.
- Kế hoach 3 tháng tới là chuyển nền tảng authentication sang dùng chung hạ tầng SSO của Keycloak
- Kế hoạch 6 tháng tới là tuning nâng cấp hệ thống activity monitor.
- Tích hợp các hệ thống khác
- Tối ưu kiến trúc hệ thống hiện tại
- Tối ưu các phần tử, các điểm nghẽn...
- Loại bỏ hoặc viết lại 1 số code không đạt chuẩn.

## VI. CÁC THÔNG TIN KHÁC


----------------------------------------------------------------------------------------------------------

# DataTP Forwarder Management


## I. THÔNG TIN CHUNG

### 1. Người được phỏng vấn: `Lê Ngọc Đàn`

### 2. Thời gian triển khai: `06/2022 -> 11/2023`

### 3. Nhân sự triển khai:
- `Nguyễn Tuấn Anh`
- `Hà Trung Hiếu`
- `Lê Ngọc Đàn`
- `Lê Quang Nhật`

### 4. Các chức năng chính (mô tả chi tiết các chức năng chính của hệ thống)
- Quy trình vận hành, thay thế BFSOne.
- Quản lý vận đơn (Master Bill, House Bill),
- Quản lý Job file, bao gồm toàn bộ thông tin của 1 lô hàng.

### 5. Phạm vi triển khai:
- GD1: Phát triển và triển cho công ty HPS chạy thử nghiệm.
- Số lượng người sử dụng:
- Kỳ vọng: toàn bộ nhân sự khối vấn hàng (Docs/ Cus) của BEE.

## II. CHỨC NĂNG VÀ QUY TRINH NGHIỆP VỤ

## III. KIẾN TRÚC VÀ CÔNG NGHỆ

### 1. Công nghệ sử dụng chính:
- Backend:
  + Ngôn ngữ lập trình: Java 21, Groovy
  + Framework/thư viện: Spring Boot, Spring Batch
  + Kiến trúc hệ thống: Monolithic với các module tách biệt
- Frontend:
  + Ngôn ngữ lập trình: JavaScript, HTML/CSS
  + Framework/thư viện: React

### 2. Cơ sở dữ liệu:
- PostgreSQL

### 3. Phân quyền:
- Sử dụng hệ thống phân quyền của DataTP Core
- Các vai trò: Admin, Moderator, Write, Read
- Scope dữ liệu: All, Company, Group, Owner.
- Cơ chế đăng nhập: Thông qua DataTP Core, kế hoạch chuyển sang Keycloak SSO

### 4. Sao lưu dữ liệu:
- Hiện tại sử dụng cơ chế sao lưu chung của hệ thống DataTP Core
- Kế hoạch xây dựng cơ chế sao lưu riêng khi tách hệ thống

### 5. Hạ tầng tài nguyên hệ thống:
- Chạy trên nền tảng cloud chung với DataTP Core
- Kế hoạch tách thành hệ thống độc lập

## IV. VẬN HÀNH VÀ KHAI THÁC
- Dự án dùng và không tiếp tục phát triển vì các lý do khách quan không liên quan đến phần mềm như:
  + Công ty HPS tách khỏi Bee.
  + Api, liên kết dữ liệu với phần mềm kế toán (Công ty đang dùng AV - phần mềm kế toán cũ)

## V. HƯỚNG PHÁT TRIỂN
## VI. CÁC THÔNG TIN KHÁC

----------------------------------------------------------------------------------------------------------

# DataTP TMS

## I. THÔNG TIN CHUNG

### 1. Người được phỏng vấn: Chiến1. 

### 2. Thời gian triển khai
- Giai giai đoạn 1 (Bắt đầu dự án - Phát triển cho BEESCS) : 02/2023 - 07/2023
- Giai giai đoạn 2 : 02/2024 - Hiện tại

### 3. Nhân sự triển khai:
- Nguyễn Tuấn Anh
- Phạm Minh Chiến
- Phùng Hoàn Quân

### 4. Các chức năng chính (mô tả chi tiết các chức năng chính của hệ thống)
- Module TMSBill:
  + Quản lý kế hoạch của 1 lô hàng
- Module Fleet:
  + Quản lý kế hoạch vận tại
  + Quản lý chuyến xe
  + Quản lý đội xe
- Module VendorBill:
  + Quản lý bill của Thầu Phụ
- Module TMSRouseUse:
  + Quản lý bill Round Use

### 5. Phạm vi triển khai:
- Hệ thống được triển khai ở đâu?
  + Giai đoạn 1: Triển khai cho BEESCS
  + Giai đoạn 2: Triển khai cho BEEHPH, BEEHAN
  + Giai đoạn hiện tại: Đang triển khai cho BEEHCM, BEEDAD, MARINE
- Số lượng người sử dụng (theo thiết kế và theo thực tế hiện tại)?
  + Bộ phận trucking (>30 người)
  + Bộ phận kê toán (>5 người)
  + Bộ phận sale (>5 người)

## II. CHỨC NĂNG VÀ QUY TRINH NGHIỆP VỤ
### 1. Danh sách đầy đủ các module, chức năng cho từng nhánh công việc
- Module TMSBill - Quản lý kế hoạch của 1 lô hàng :
  + Lên kế hoạch cho 1 lô hàng bao gồm: Thông tin khách hàng, Khối lượng hàng, Ngày giao hàng, Địa chỉ lấy trả hàng,...
  + Tạo file PDF biên bản giao hàng(POD)
  + Gửi Mail cho thầu phụ
  + Export data ra file excell
  + Tạo tempalte export data gửi thầu phụ
  + Tạo tempalte export data BFSOne
  + Tích đầu việc cần hoàn thành của 1 lô hàng
  + Báo cáo số lượng lô hàng được mở và số lượng đầu việc đã hoàn thành theo từng Account
  + API push các lô hàng sang các đơn vị thầu phụ có sử dụng API(Hiện tại đang push chéo các công ty nội bộ)
- Module Fleet - Quản lý kế hoạch vận tại (Module:Fleet) :
    + Lên kế hoạch xe, xếp xe, ghép chuyến cho các kế hoạch được gửi từ TMSBill
    + Hệ thống gửi tin nhắn zalo, mail khi nhập đủ thông tin xe cho các đối tượng tham gia vào lô hàng
    + Định vị lô hàng, ghi nhận lịch sử di chuyển các xe có hỗ trợ kết nối định vị
    + Quản thông tin thầu phụ, thông tin xe, thông tin lái xe
    + Hệ thống tích hợp định vị xe từ nhiều nhà cung cấp định vị khác nhau
- Module VendorBill - Quản lý bill của Thầu Phụ :
    + Export/Import bill cho thầu phụ (Bản kê), so sánh đối chiếu giá cước thầu phụ input
    + Lưu trữ thông tin xe do thầu phụ cung cấp
- Module RoundUse: Quản lý bill Round Use
    + Nhập và lên kế hoách cho các lô hang round-use
### 2. Mô tả các workflow
- TMSBill: Dữ liệu nguồn cho các module dưới kế thừa
    + Nhận yêu cầu vận chuyển hàng từ Sale hoặc khách hàng
    + Tạo kế hoạch mới hoặc kéo kế hoạch từ BFSOne về TMSBill và điền đủ thông tin
    + Xác định thầu phụ sẽ vận chuyển lô hàng và gửi mail + biên bản giao hàng(POD) cho thầu phụ mục tiêu
    + Theo dõi tiến độ vận tải lô hàng, nhập thông tin xe, cước nếu đó là các xe gọi ngoài(Các xe nội bộ điều vận sẽ nhập)
- Fleet:
    + Điều vận
      * Nhận các lô hàng TMSBill gửi cho thầu phụ nội bộ
      * Tách các lô hàng nếu lô hàng lớn vượt qua mức tải của xe, tối ưu hóa tuyến đường vận tải bằng các nhóm các lô hàng trên cùng tuyến vận tải lên 1 chuyến
      * Khóa các lô hàng đã chốt thông tin vận tải
    + Lái xe
      * Nhận các lô hàng điều vận đã khóa kế hoạch
      * Nhận chuyến xe khi bắt đầu chạy và kết thúc chuyến khi hoàn thành
- VendorBill
    + Thầu phụ nhận bản kê vận tải theo ngày bằng file excell hoặc trên hệ thống
    + Nhập giá và update lên hệ thống, đối chiếu giá thầu phụ nhập và Cus Trucking nhập trên TMSBill, nếu lệch thì 2 bên phải trao đổi và thông nhất lại giá
- RoundUse : Mục đích sử dụng lại CONT 1 cách hiệu quả
    + Giải thích cách hoạt động: Tận dụng CONT các lô hàng Nhâp/Xuất để tối ưu chi phí thuê CONT.
      VD: 1 lô hàng CONT 20DC nhập về cảng và vận chuyển tới Hải Dương, nếu hãng tàu A cho phép tái sử dụng CONT và hạn 3 ngày phải kéo trả lại hãng tàu thì khi đó BEE sẽ tận dụng để tìm kiếm các khách hàng ở Hải Dương có book CONT 20DC cho hãng tàu A vận tải để làm round-use, như vậy sẽ tiết kiệm được chi phí kéo CONT rỗng về cảng.
      Hoặc nếu có bãi để CONT thì có thể kéo về bãi và chờ các lô hàng phù hợp hoặc các lô hàng chuyển kho để vận tải
    + Hoạt động trên Phần Mềm: Tìm kiếm trên TMSBill đã tạo các lô hàng CONT chưa vận chuyển, các lô đủ điều kiện round-use sẽ yêu cầu người chụ trách nhiệm tms-bill đó cho cho phép round-use không (Vì tùy theo CONT đang mượn của hãng tàu có được phép round-use không)
    + Lưu thông tin nâng/hạ CONT với các CONT kéo về bãi của BEE
- Liên kết  với hệ thống CoreTP thế nào
  - Sử dụng chung cơ sở dữ liệu về khách hàng, đối tác, locaion

## III. KIẾN TRÚC VÀ CÔNG NGHỆ
Công nghệ sử dụng chính:
- Backend:
  + Ngôn ngữ lập trình: Java 21, Groovy
  + Framework/thư viện: Spring Boot, Spring Batch
  + Kiến trúc hệ thống: (Monolithic, Microservices, SOA...?)
- Frontend:
  + Ngôn ngữ lập trình: JavaScript, HTML/CSS
  + Framework/thư viện: React
Cơ sở dữ liệu:
- PostgreSQL
Phân quyền:
- Các loại người dùng : quản trị, nhân viên...
- Các cơ chế phân quyền
- Cơ chế đăng nhập: (tài khoản độc lập, SSO, LDAP...?)
- Cơ chế xác thực: 2FA, OTP?
Sao lưu dữ liệu
- Cơ chế sao lưu, backup dữ liệu, khả năng
- Hình thức sao lưu (local, cloud...) sao lưu tự động
Hạ tầng tài nguyên hệ thống:
- Loại hạ tầng của hệ thống: Cloud
- Hệ điều hành:
- Hạ tầng mạng
  + Ảo hóa, mạng vật lý
  + Tốc độ, băng thông giữa các thiết bị
- Bảo mật hệ thống
  + Có các hình thức ngăn chặn truy cập trái phép (firewall, IPS/IDS, VPN, filter...)
  + Có phân hệ thống thành các vùng riêng biệt (VLAN, VPN, DMZ..?)
  + Chống tấn công DDoS?

## IV. VẬN HÀNH VÀ KHAI THÁC
- Phần mềm vẫn được duy trì phát triển và maintain thế nào
- Có tài liệu hướng dẫn sử dụng và vận hành chưa?
- Hệ thống giám sát, cảnh bảo hoạt động thế nào (tài nguyên hệ thống, lưu lượng, bảo mật, log...)

## V. HƯỚNG PHÁT TRIỂN
- Kế hoach 3 tháng tới là tách dự án, dùng db riêng giao diện độc lập. Có thể kết hợp với Keycloadk SSO để chạy độc lập hoàn toàn.
- Tích hợp các hệ thống khác?
- Tối ưu kiến trúc hệ thống hiện tại?
- Tối ưu các phần tử, các điểm nghẽn...?
- Loại bỏ hoặc viết lại 1 số code không đạt chuẩn.?

## VI. CÁC THÔNG TIN KHÁC

----------------------------------------------------------------------------------------------------------

# DataTP Document IE

## I. THÔNG TIN CHUNG

### 1. Người được phỏng vấn: Đạt Lương

### 2. Thời gian triển khai: `03/2025 -> NOW`

### 3. Nhân sự triển khai:
- `Lương Văn Đạt`: Người thực hiện chính
- `Phùng Hoàng Quân`: Người hỗ trợ một số chức năng

### 4. Các chức năng chính (mô tả chi tiết các chức năng chính của hệ thống)
- Thực hiện trích xuất dữ liệu trên hóa đơn hoặc biên lai.
  + Giúp verify thông tin trên bản kê của thầu phụ với các bộ hóa đơn
  + Tổng hợp thông tin thành bản kê cho kế toán

### 5. Phạm vi triển khai:
- Hệ thống hiện tại được triển khai cho kế toán thuộc `BeeHPH, BeeCORP, BeeHCM`
- Số lượng người sử dụng kỳ vọng là toàn bộ kế toán thuộc các cty trên

## II. CHỨC NĂNG VÀ QUY TRINH NGHIỆP VỤ
- Module **document**:
  + Upload, download các bộ chứng từ để thực hiện trích rút
  + Thống kê số lượng thông tin trích rút được
  + Phân chia loại hóa đơn để gọi service phù hợp
  + Verify dữ liệu trích rút được với bản kê
  + Tạo bản kê từ dữ liệu trích rút
- Mô tả các workflow
  + Người dùng tạo bộ chứng từ
  + Upload các file lên `pdf, excel` của hóa đơn hay bản kê
  + Đánh dấu **type** của file đó, để chọn đúng service trích rút có thể là `invoice, receipt, fcl, lcl`
  + Bắt đầu quá trình trích rút, file đó sẽ được gửi về **back-end** và nhận lại dữ liệu
  + Tùy theo nghiệp vụ thực hiện verify với bản kê hoặc tạo bản kê
- Liên kết với hệ thống MSA
  + File sau khi nhận được ở **back-end** sẽ gửi qua tiếp hệ thống MSA triển khai bằng python để xử lý trích rút
  + **Back-end** nhận dữ liệu dưới dạng **Json** và parse thành các model java và lưu vào database

## III. KIẾN TRÚC VÀ CÔNG NGHỆ
Công nghệ sử dụng chính:
- Backend:
    + Ngôn ngữ lập trình: `Java, Python`
    + Framework/thư viện:
      + Java framework: `Spring`
      + Python library: `pdfplumber, pandas, tabulate, tornado, aiohttp`
- Frontend:
    + Ngôn ngữ lập trình: `Javascript`
    + Framework/thư viện: `React`
- Cơ sở dữ liệu: `PostgresSQL`
- Phân quyền:
  + Người dùng chủ yếu là kế toán được phân quyền
  + Phân quyền theo công ty và người dùng
  + Cơ chế đăng nhập: (tài khoản độc lập, SSO, LDAP...?)
  + Cơ chế xác thực: 2FA, OTP?
- Sao lưu dữ liệu
  + Cơ chế sao lưu, backup dữ liệu, khả năng
  = Hình thức sao lưu (local, cloud...) sao lưu tự động
- Hạ tầng tài nguyên hệ thống:
  + Loại hạ tầng của hệ thống: Cloud
  + Hệ điều hành:
  + Hạ tầng mạng
    * Ảo hóa, mạng vật lý
    * Tốc độ, băng thông giữa các thiết bị
- Bảo mật hệ thống
    + Có các hình thức ngăn chặn truy cập trái phép (firewall, IPS/IDS, VPN, filter...)
    + Có phân hệ thống thành các vùng riêng biệt (VLAN, VPN, DMZ..?)
    + Chống tấn công DDoS?

## IV. VẬN HÀNH VÀ KHAI THÁC
- Phần mềm vẫn được duy trì phát triển và maintain thế nào
- Có tài liệu hướng dẫn sử dụng và vận hành chưa?
- Hệ thống giám sát, cảnh bảo hoạt động thế nào (tài nguyên hệ thống, lưu lượng, bảo mật, log...)

## V. HƯỚNG PHÁT TRIỂN
- Kế hoach 3 tháng tới là tách dự án, dùng db riêng giao diện độc lập. Có thể kết hợp với Keycloadk SSO và datatp-core để chạy độc lập hoàn toàn.
- Kế hoạch 6 tháng tới là Tích hợp với phần mềm CMS strapi để cung cấp đầy đủ dịch vụ CMS và document management.
- Tích hợp các hệ thống khác?
- Tối ưu kiến trúc hệ thống hiện tại?
- Tối ưu các phần tử, các điểm nghẽn...?
- Loại bỏ hoặc viết lại 1 số code không đạt chuẩn.?

## VI. CÁC THÔNG TIN KHÁC
1. Phỏng vấn lập trình viên tham gia phát triển
- Hệ thông nhận dạng ban đầu sử dụng các thư viện java để trích rút nhưng có nhược điểm là không đọc được file pdf nên cần chuyển file pdf sang excel để tận dụng các thư viện đọc excel có sẵn.
Tuy nhiên cách này làm phát sinh nhiều bước và kết quả cũng chưa đạt kỳ vọng
- Sau này qua quá trình tìm hiểu và thử nghiệm thấy được thư viện của python có tiềm năng rất lớn cho việc trích rút trực tiêp file pdf.
Sau đó kết hợp với việc xây dựng các chức năng nhận dạng từ, cụm từ hay gán nhãn ký tự đã thu được kết quả rất tốt và được chính thức sử dụng
- Cùng với thành công của thư viện python bắt đầu tích hợp nhận dạng thêm biên lai, tương lai triển khai sử dụng ocr để nhận dạng ảnh

2. Phỏng vấn người dùng sử dụng, có đáp ứng được nhu cầu...
- Phần mềm được kế toán sử dụng và có phản hồi rất tốt
- Phần mềm phù hợp với nhu cầu tự động hóa quá trình nhập tay của kế toán

----------------------------------------------------------------------------------------------------------

## DataTP Spreadsheet

## I. THÔNG TIN CHUNG

### 1. Người được phỏng vấn: Lê Ngọc Đàn + Chiến.

### 2. Thời gian triển khai: `16/10/2022`

### 3. Nhân sự triển khai:
- `Lê Ngọc Đàn`
- `Lê Quang Nhật`
- `Phạm Minh Chiến`

### 4. Các chức năng chính (mô tả chi tiết các chức năng chính của hệ thống)

### 5. Phạm vi triển khai:
- Hệ thống được triển khai ở đâu?
- Số lượng người sử dụng (theo thiết kế và theo thực tế hiện tại)?

## II. CHỨC NĂNG VÀ QUY TRINH NGHIỆP VỤ
- Danh sách đầy đủ các module, chức năng cho từng nhánh công việc
- Mô tả các workflow
- Liên kết với hệ thống CoreTP thế nào

## III. KIẾN TRÚC VÀ CÔNG NGHỆ
Công nghệ sử dụng chính:
- Backend:
  + Ngôn ngữ lập trình:
  + Framework/thư viện:
  + Kiến trúc hệ thống: (Monolithic, Microservices, SOA...?)
- Frontend:
    + Ngôn ngữ lập trình:
    + Framework/thư viện:
Cơ sở dữ liệu:
- (MySQL, PostgreSQL, MongoDB...?)
- Mô tả kiến trúc và cấu trúc CSDL?
Phân quyền:
- Các loại người dùng (quản trị, người sử dụng, nhân viên...)
- Các cơ chế phân quyền
- Cơ chế đăng nhập: (tài khoản độc lập, SSO, LDAP...?)
- Cơ chế xác thực: 2FA, OTP?
Sao lưu dữ liệu
- Cơ chế sao lưu, backup dữ liệu, khả năng
- Hình thức sao lưu (local, cloud...) sao lưu tự động
Hạ tầng tài nguyên hệ thống:
- Loại hạ tầng của hệ thống: Cloud
- Hệ điều hành:
- Hạ tầng mạng
    + Ảo hóa, mạng vật lý
    + Tốc độ, băng thông giữa các thiết bị
- Bảo mật hệ thống
    + Có các hình thức ngăn chặn truy cập trái phép (firewall, IPS/IDS, VPN, filter...)
    + Có phân hệ thống thành các vùng riêng biệt (VLAN, VPN, DMZ..?)
    + Chống tấn công DDoS?

## IV. VẬN HÀNH VÀ KHAI THÁC
- Phần mềm vẫn được duy trì phát triển và maintain thế nào
- Có tài liệu hướng dẫn sử dụng và vận hành chưa?
- Hệ thống giám sát, cảnh bảo hoạt động thế nào (tài nguyên hệ thống, lưu lượng, bảo mật, log...)

## V. HƯỚNG PHÁT TRIỂN
- Kế hoach 3 tháng tới là tách dự án, dùng db riêng giao diện độc lập. Có thể kết hợp với Keycloadk SSO để chạy độc lập hoàn toàn.
- Tích hợp các hệ thống khác?
- Tối ưu kiến trúc hệ thống hiện tại?
- Tối ưu các phần tử, các điểm nghẽn...?
- Loại bỏ hoặc viết lại 1 số code không đạt chuẩn.?

## VI. CÁC THÔNG TIN KHÁC


----------------------------------------------------------------------------------------------------------

## Bee Legacy

## I. THÔNG TIN CHUNG
### 1. Người được phỏng vấn: `Lê Ngọc Đàn`
### 2. Thời gian triển khai: `25/04/2025 -> Now`
### 3. Nhân sự triển khai: `Nguyễn Tuấn Anh` , `Lê Ngọc Đàn`

### 4. Các chức năng chính (mô tả chi tiết các chức năng chính của hệ thống)
- Tích hợp và đồng bộ dữ liệu từ hệ thống BFS One (hệ thống vận hành, kế toán)
- Quản lý tỷ giá hối đoái liên quan đến các loại báo cáo cho saleman trên hệ thống BFSOne (Exchange Rate)
- Quản lý thông tin vận đơn (House Bill), thu thập từ nhiều bảng trên database BFSOne (integrated_housebill)
- Quản lý thông tin partners tích hợp (integrated_partner)
- Quản lý thông tin unit (đơn vị).

### 5. Phạm vi triển khai:
- Hệ thống được triển khai nội bộ để hỗ trợ chuyển đổi và tích hợp dữ liệu
- Hệ thống phụ trợ, hỗ trợ tích hợp chức năng với hệ thống khác.
- Người sử dụng chủ yếu là IT, Dev.

## II. CHỨC NĂNG VÀ QUY TRINH NGHIỆP VỤ
- Các model chính:
  + Exchange Rate: Đồng bộ và quản lý tỷ giá hối đoái từ BFS One
  + House Bill: Tích hợp và quản lý thông tin vận đơn, thu thập từ nhiều bảng, nhiều nguồn ở BFSOne.
  + Integrated Partner: Quản lý thông tin đối tác, thu thập đẩy đủ thông tin partner (từ BFSOne, CRM)
  + Settings Unit: Quản lý cài đặt đơn vị
- Tạo ra các bảng chung, thu thập dữ liệu từ nhiều nguồn, tối ưu cho mục đích báo cáo, trích rút dữ liệu thường sử dụng một cách nhanh chóng.
- Các bảng này sẽ được sync tự động từ BFSOne, DataTP qua hình thức pull hoặc push (DataTP cập qua api)

## III. KIẾN TRÚC VÀ CÔNG NGHỆ
Công nghệ sử dụng chính:
- Backend:
  + Ngôn ngữ lập trình: Python 3.10
  + Framework/thư viện: SQLAlchemy, pyodbc, click, PyYAML
  + Kiến trúc hệ thống: Module-based monolithic
- Frontend: Không có giao diện người dùng riêng, tích hợp vào hệ thống khác
- Cơ sở dữ liệu:
  + PostgreSQL cho lưu trữ dữ liệu chính
  + Kết nối đến Microsoft SQL Server (BFS One) để đồng bộ dữ liệu
  + Cấu trúc CSDL gồm các bảng chính: integrated_housebill, exchange_rate, integrated_partner, settings_unit
- Phân quyền:
  + Không có hệ thống phân quyền riêng, phụ thuộc vào hệ thống chính
  + Truy cập cơ sở dữ liệu dựa trên tài khoản riêng được cấu hình trong file config.yaml
- Sao lưu dữ liệu:
  + Sử dụng cơ chế sao lưu chung của hệ thống PostgreSQL
- Hạ tầng tài nguyên hệ thống:
  + Chạy trên cùng hạ tầng với các dịch vụ khác của DataTP
  + Hệ điều hành: Linux Debian
  + Chia sẻ tài nguyên với các module khác của hệ thống

## IV. VẬN HÀNH VÀ KHAI THÁC
- Phần mềm được duy trì để hỗ trợ việc chuyển đổi, tập trung dữ liệu từ các hệ thống để tạo ra bộ dữ liệu chuẩn để các hệ thống cùng dùng chung.
- Có các unit test để đảm bảo tính ổn định của hệ thống

## V. HƯỚNG PHÁT TRIỂN
- Kế hoạch 3 tháng tới:
  + Tích hợp với `Keycloak SSO` để quản lý xác thực
  + Làm việc với anh Quý để việc api với BFSOne mượt mà hơn.
  + Đối soát chéo với các hệ thống, triển khai một phần, đảm bảo dữ liệu là chính xác, đầy đủ.
- Kế hoạch 6 tháng tới:
  + Nâng cấp khả năng đồng bộ dữ liệu từ nhiều nguồn
  + Tối ưu hiệu suất truy vấn và xử lý dữ liệu lớn
  + Thay thế các loại, màn hình báo cáo cho hệ thống DataTP Cloud, một số màn hình của BFSOne để tối ưu trải nghiệm cho người dùng.

- Cải tiến kiến trúc:
  + Tăng độ phủ của unit test
  + Cải thiện hệ thống quản lý lỗi và log (grafana, prometheus, ..)

## VI. CÁC THÔNG TIN KHÁC

### 1. Phỏng vấn lập trình viên:
    - Thách thức chính là việc đồng bộ dữ liệu real time từ hệ thống BFS One (cũ) sang hệ thống mới.
    - Vấn đề về định dạng dữ liệu không đồng nhất giữa các hệ thống (dữ liệu phải phù hợp và thuận tiện cho cả BFSOne và DataTP Cloud)

### 2. Phỏng vấn người dùng:
    - Hệ thống giúp giảm thời gian xử lý báo cáo từ nhiều nguồn dữ liệu
    - Cần cải thiện tốc độ đồng bộ dữ liệu và tính ổn định
	
----------------------------------------------------------------------------------------------------------

# Datatp-job-Tracking

## I. THÔNG TIN CHUNG

- Người được phỏng vấn:
  + Lê Quang Nhật

- Thời gian triển khai: 02/2024 - curent

- Nhân sự triển khai:
  + Lê Quang Nhật
  + Phạm Minh Chiến

- Các chức năng chính (mô tả chi tiết các chức năng chính của hệ thống)
  + Quản lý thông tin lô hàng, tiến độ hoàn thành các đầu việc trong quy trình hoàn thiện 1 lô hàng. Mỗi bộ phận/nghiệp vụ được phân vào từng Project riêng biệt.
  + Kiểm đếm và báo cáo số lượng đầu việc mà 1 nhân viên đã làm được, phục vụ cho việc đánh giá hiệu quả công việc.

- Phạm vi triển khai:
  + Hệ thống được triển khai phục vụ nội bộ công ty, hiện tại toàn bộ các bộ phận ở HPH đang sử dụng, có ý định triển khai ra các chi nhánh khác.
  + Số lượng người sử dụng theo thiết kế: toàn bộ nhân viên bộ phận BackOffice.
  + Số lượng người sử dụng thực tế hiện tại: ~100 người

## II. CHỨC NĂNG VÀ QUY TRINH NGHIỆP VỤ

- Các chức năng:
  + Quản lý, theo dõi thông tin lô hàng, tiến độ hoàn thành các đầu việc trong quy trình hoàn thiện 1 lô hàng.
  + Kiểm đếm và báo cáo số lượng đầu việc mà 1 nhân viên đã làm được, phục vụ cho việc đánh giá hiệu quả công việc.
  + Hiển thị dữ liệu theo các dạng báo cáo khác nhau, phục vụ cho việc đánh giá hiệu quả công việc.

- Mô tả các workflow:
  + Nhận Inquiry của khách, nhập thông tin lô hàng, các thông tin liên quan vào hệ thống.
  + Theo dõi, cập nhật thông tin lô hàng, cập nhật các đầu việc hoàn thành..
  + Báo cáo, đánh giá hiệu quả công việc của nhân viên theo tuần, tháng, quý, năm.

- Liên kết với hệ thống CoreTP:
  + Sử dụng chung cơ sở dữ liệu về khách hàng, đối tác
  + Kế hoạch tách ra thành hệ thống độc lập với database riêng

## III. KIẾN TRÚC VÀ CÔNG NGHỆ
Công nghệ sử dụng chính:

- Backend:
  + Ngôn ngữ lập trình: Java 21, Groovy
  + Framework/thư viện: Spring Boot, Spring Batch
  + Kiến trúc hệ thống: Monolithic với các module tách biệt

- Frontend:
  + Ngôn ngữ lập trình: JavaScript, HTML/CSS
  + Framework/thư viện: React

- Cơ sở dữ liệu:
  + PostgreSQL

- Phân quyền:
  + Sử dụng hệ thống phân quyền của DataTP Core gồm các vai trò: Admin, Moderator, Write, Read.
  + Có hệ thống phân quyền riêng trong module gồm các vai trò: Admin, Moderator, Write, Read.
  + Cơ chế đăng nhập: Thông qua DataTP Core, kế hoạch chuyển sang Keycloak SSO.

- Sao lưu dữ liệu:
  + Hiện tại sử dụng cơ chế sao lưu chung của hệ thống DataTP Core.
  + Kế hoạch xây dựng cơ chế sao lưu riêng khi tách hệ thống.

- Hạ tầng tài nguyên hệ thống:
  + Chạy trên nền tảng cloud chung với DataTP Core
  + Kế hoạch tách thành hệ thống độc lập

## IV. VẬN HÀNH VÀ KHAI THÁC

- Phần mềm đã được phát triển và đã vào giai đoạn vận hành ổn định, vẫn được duy trì phát triển và maintain.

## V. HƯỚNG PHÁT TRIỂN

- Kế hoạch 3 - 6 tháng tới:
  + Tách dự án thành hệ thống độc lập với database riêng.
  + Tích hợp với Keycloak SSO.
  + Cải thiện thông báo lỗi từ server đến người dùng.
  + Tích hợp với hệ thống OKR/KPI, nhằm tổng hợp dữ liệu để đánh giá hiệu quả công việc của nhân viên.

- Tích hợp các hệ thống khác?
  + Tích hợp với hệ thống TMS, để lấy thông tin về các lô hàng, tiến độ hoàn thành các đầu việc trong quy trình hoàn thiện 1 lô hàng.
  + Tích hợp với hệ thống Pricing, để lấy thông tin về giá của các đầu việc, phục vụ cho việc đánh giá hiệu quả công việc.

## VI. CÁC THÔNG TIN KHÁC
### 1. Phỏng vấn lập trình viên tham gia phát triển
- Khó khăn trong việc tối ưu giao diện, thao tác cho người dùng vì người dùng đã quen thuộc với việc thao tác trên file Excel.
- Yêu cầu thay đổi thường xuyên từ người dùng.

### 2. Phỏng vấn người dùng sử dụng, có đáp ứng được nhu cầu...
- Phần mềm được sử dụng hàng ngày
- Phần mềm đáp ứng tốt nhu cầu quản lý thông tin và theo dõi quy trình hoàn thiện 1 lô hàng.
- Cải thiện tốc độ tải trang, tối ưu giao diện người dùng trên thiết bị di động.
----------------------------------------------------------------------------------------------------------

# Datatp-KPI

## I. THÔNG TIN CHUNG

- Người được phỏng vấn:
  + Lê Quang Nhật

- Thời gian triển khai: 02/2025 - curent

- Nhân sự triển khai:
  + Lê Quang Nhật

- Các chức năng chính (mô tả chi tiết các chức năng chính của hệ thống)
  + Đánh giá KPI - hiệu quả công việc của từng nhân viên.

- Phạm vi triển khai:
  + Hệ thống được triển khai phục vụ nội bộ công ty.
  + Số lượng người sử dụng theo thiết kế: toàn bộ nhân viên của BEE.
  + Số lượng người sử dụng thực tế hiện tại: ~500 người

## II. CHỨC NĂNG VÀ QUY TRINH NGHIỆP VỤ

- Các chức năng:
  + Lập các Jobcode - template các mục tiêu để đánh giá cho từng vị trí công việc - phòng ban.
  + Đánh giá KPI - hiệu quả công việc của từng nhân viên.
  + Nhận/Gửi yêu cầu nhận xét đánh giá KPI.

- Mô tả các workflow:
  + Quản lý lập template các mục tiêu để đánh giá cho từng vị trí công việc - phòng ban.
  + Quản lý lập chỉ tiêu, mục tiêu cho từng nhân viên.
  + Nhân viên tự đánh giá KPI.
  + Quản lý nhận xét đánh giá KPI.
  + Giám đốc văn phòng xác nhận kết quả.

- Liên kết với hệ thống CoreTP:
  + Sử dụng chung cơ sở dữ liệu về khách hàng, đối tác
  + Kế hoạch tách ra thành hệ thống độc lập với database riêng

## III. KIẾN TRÚC VÀ CÔNG NGHỆ

Công nghệ sử dụng chính:

- Backend:
  + Ngôn ngữ lập trình: Java 21, Groovy
  + Framework/thư viện: Spring Boot, Spring Batch
  + Kiến trúc hệ thống: Monolithic với các module tách biệt

- Frontend:
  + Ngôn ngữ lập trình: JavaScript, HTML/CSS
  + Framework/thư viện: React

- Cơ sở dữ liệu:
  + PostgreSQL

- Phân quyền:
  + Sử dụng hệ thống phân quyền của DataTP Core gồm các vai trò: Admin, Moderator, Write, Read.
  + Cơ chế đăng nhập: Thông qua DataTP Core, kế hoạch chuyển sang Keycloak SSO.

- Sao lưu dữ liệu:
  + Hiện tại sử dụng cơ chế sao lưu chung của hệ thống DataTP Core.
  + Kế hoạch xây dựng cơ chế sao lưu riêng khi tách hệ thống.

- Hạ tầng tài nguyên hệ thống:
  + Chạy trên nền tảng cloud chung với DataTP Core
  + Kế hoạch tách thành hệ thống độc lập

## IV. VẬN HÀNH VÀ KHAI THÁC

- Phần mềm đã được phát triển và đã vào giai đoạn vận hành, vẫn được duy trì phát triển và maintain.

## V. HƯỚNG PHÁT TRIỂN

- Kế hoạch 3 - 6 tháng tới:
  + Tách dự án thành hệ thống độc lập với database riêng.
  + Tích hợp với Keycloak SSO.
  + Phát triển thêm tính năng mới, hoàn thiện đầy đủ tính năng đánh giá KPI.
  + Tích hợp với hệ thống Job Tracking, Pricing, TMS, BFSOne nhằm tổng hợp dữ liệu để đánh giá hiệu quả công việc của nhân viên.

- Tích hợp các hệ thống khác?

## VI. CÁC THÔNG TIN KHÁC

1. Phỏng vấn lập trình viên tham gia phát triển
- Khó khăn trong việc tối ưu giao diện, làm sao cho người dùng dễ dàng sử dụng phần mềm.
- Yêu cầu thay đổi thường xuyên từ người dùng.

2. Phỏng vấn người dùng sử dụng, có đáp ứng được nhu cầu...
- Phần mềm được sử dụng hàng ngày
- Phần mềm đáp ứng tốt nhu cầu đánh giá KPI của nhân viên, thay thế được quy trình thủ công bằng excel trước đây.
- Cải thiện, tối ưu giao diện người dùng trên thiết bị di động.

----------------------------------------------------------------------------------------------------------

# Datatp-OKR

## I. THÔNG TIN CHUNG

- Người được phỏng vấn:
  + Lê Quang Nhật

- Thời gian triển khai: 01/2023 - curent

- Nhân sự triển khai:
  + Lê Quang Nhật
  + Lê Ngọc Đàn

- Các chức năng chính (mô tả chi tiết các chức năng chính của hệ thống)
  + Lên các mục tiêu - kết quả then chốt cho từng phòng ban, theo dõi tiến độ các mục tiêu - kết quả then chốt bằng các chỉ số có thể đo lường được, từ đó có thể đưa ra được các kế hoạch/điều chỉnh kịp thời.

- Phạm vi triển khai:
  + Hệ thống được triển khai phục vụ nội bộ công ty.
  + Số lượng người sử dụng theo thiết kế: toàn bộ nhân viên của BEE.
  + Số lượng người sử dụng thực tế hiện tại: ~600 người

## II. CHỨC NĂNG VÀ QUY TRINH NGHIỆP VỤ

- Các chức năng:
  + Tạo OKR Project cho từng phòng ban.
  + Lập các mục tiêu - kết quả then chốt từ cấp Corp, phân bổ các chỉ tiêu xuống từng chi nhánh, từ chi nhánh xuống từng phòng ban, từ phòng ban xuống từng nhân viên.
  + Tạo các rule để tự động cập nhật các kết quả thực tế cho mục tiêu, kết quả then chốt.

- Mô tả các workflow:
  + Giám đốc lập ra các mục tiêu - kết quả then chốt cho Corp, phân bổ chỉ tiêu cho từng chi nhánh.
  + Giám đốc chi nhánh nhận chỉ tiêu được phân bổ, lập các mục tiêu - kết quả then chốt cho chi nhánh, phân bổ chỉ tiêu cho từng phòng ban.
  + Các phòng ban nhận chỉ tiêu được phân bổ, lập các mục tiêu - kết quả then chốt cho phòng ban.
  + Các phòng ban hoàn thành các mục tiêu - kết quả then chốt, đối với những mục tiêu - kết quả then chốt được phân bổ, kết quả sẽ tự động được cập nhật lên cấp trên.
  + Theo dõi tiến độ hoàn thành các mục tiêu - kết quả then chốt, đánh giá hiệu quả công việc của từng phòng ban, để điều chỉnh kế hoạch kịp thời.

- Liên kết với hệ thống CoreTP:
  + Sử dụng chung cơ sở dữ liệu về khách hàng, đối tác
  + Kế hoạch tách ra thành hệ thống độc lập với database riêng

## III. KIẾN TRÚC VÀ CÔNG NGHỆ
Công nghệ sử dụng chính:

- Backend:
  + Ngôn ngữ lập trình: Java 21, Groovy
  + Framework/thư viện: Spring Boot, Spring Batch
  + Kiến trúc hệ thống: Monolithic với các module tách biệt

- Frontend:
  + Ngôn ngữ lập trình: JavaScript, HTML/CSS
  + Framework/thư viện: React

- Cơ sở dữ liệu:
  + PostgreSQL

- Phân quyền:
  + Sử dụng hệ thống phân quyền của DataTP Core gồm các vai trò: Admin, Moderator, Write, Read.
  + Sử dụng hế thống phân quyền riêng cho từng OKR Project, gồm các vai trò: Owner, Moderator, Write, Read.
  + Cơ chế đăng nhập: Thông qua DataTP Core, kế hoạch chuyển sang Keycloak SSO.

- Sao lưu dữ liệu:
  + Hiện tại sử dụng cơ chế sao lưu chung của hệ thống DataTP Core.
  + Kế hoạch xây dựng cơ chế sao lưu riêng khi tách hệ thống.

- Hạ tầng tài nguyên hệ thống:
  + Chạy trên nền tảng cloud chung với DataTP Core
  + Kế hoạch tách thành hệ thống độc lập

## IV. VẬN HÀNH VÀ KHAI THÁC

- Phần mềm đã được phát triển và đã vào giai đoạn vận hành, vẫn được duy trì phát triển và maintain.

V. HƯỚNG PHÁT TRIỂN

- Kế hoạch 3 - 6 tháng tới:
  + Tách dự án thành hệ thống độc lập với database riêng.
  + Tích hợp với Keycloak SSO.
  + Phát triển thêm tính năng mới, hoàn thiện chức năng tự động cập nhật dữ liệu...
  + Tích hợp với hệ thống Job Tracking, Pricing, TMS, BFSOne nhằm tổng hợp dữ liệu để đánh giá hiệu quả công việc của nhân viên.

- Tích hợp các hệ thống khác?

## VI. CÁC THÔNG TIN KHÁC

1. Phỏng vấn lập trình viên tham gia phát triển
- Khó khăn trong việc thu thập yêu cầu, phân tích yêu cầu từ người dùng,
- Khó khăn trong việc tối ưu giao diện, làm sao cho người dùng dễ dàng sử dụng phần mềm.
- Yêu cầu thay đổi thường xuyên từ người dùng.

2. Phỏng vấn người dùng sử dụng, có đáp ứng được nhu cầu...
- Phần mềm được sử dụng hàng ngày.
- Phần mềm đáp ứng tốt nhu cầu theo dõi mục tiêu - kết quả then chốt của từng phòng ban, công ty.
- Tối ưu giao diện hiển thị cho thiết bị di động.
- Đảm bảo tính chính xác khi cập nhật kết quả tự động cho các mục tiêu.

----------------------------------------------------------------------------------------------------------

# Datatp-CRM

## I. THÔNG TIN CHUNG

### 1. người được phỏng vấn: `Lê Ngọc Đàn`

### 2. Thời gian triển khai: `01/2024 -> current`

### 3. Nhân sự triển khai:
- `Lê Ngọc Đàn`
- `Vũ Tường An`
- `Lê Quang Nhật`
- `Nguyễn Năng Bình` đã thôi việc từ 06/2024

### 4. Các chức năng chính:

- Module Pricing Tools: Quản lý giá cước vận chuyển (sea, air, logistics, trucking)
- Module Sales: Quản lý quy trình bán hàng (lead, inquiry, quotation, booking)
- Dashboard: Theo dõi hiệu suất và báo cáo KPI
- Quản lý đối tác (Partner Management)
- Theo dõi hoạt động hàng ngày của nhân viên kinh doanh (Tasks Calender)

### 5. Phạm vi triển khai:
- Hệ thống được triển khai phục vụ nội bộ công ty
- Số lượng người sử dụng theo thiết kế:
    + Toàn bộ nhân viên sales và pricing/ bộ phận backoffice khác.
- Số lượng người sử dụng thực tế hiện tại:
    + 1074 người (dựa theo dữ liệu account được phân quyền vào CRM)

## II. CHỨC NĂNG VÀ QUY TRÌNH NGHIỆP VỤ
- Danh sách chi tiết các module:

###  1. Module Pricing Tools:
- Quản lý bảng giá Sea FCL, Sea LCL, Air Transport, Trucking (Container / Truck/ CBT).
- Tách bảng giá nhập/xuất theo mục đích sử dụng.
- Template cho Local Charge.
- Theo dõi công việc, tính kpi cho bộ phận Pricing dựa theo inquiry và số lượng đầu việc trên từng inquiry.

### 2. Module Sales:
- Quản lý khách hàng tiềm năng (Lead Management).
- Quản lý khách hàng (Customer Management).
- Theo dõi yêu cầu báo giá (Inquiry).
- Tạo và gửi báo giá (Quotation).
- Chuyển đổi báo giá thành booking, api với hệ thống BFSOne.

###  3. Dashboard:
- Company Pricing Dashboard
- Sales Dashboard
- Volume Performance by Department
- Sales Activities Tracker

- Quy trình làm việc chính:
  a. Sales nhập inquiry vào hệ thống
  b. Hệ thống kiểm tra giá có sẵn hoặc gửi yêu cầu báo giá đến bộ phận pricing
  c. Tạo quotation và gửi cho khách hàng
  d. Xác nhận booking và đồng bộ với hệ thống OF1

- Liên kết với hệ thống CoreTP:
  - Sử dụng chung cơ sở dữ liệu về khách hàng, đối tác
  - Kế hoạch tách ra thành hệ thống độc lập với database riêng

## III. KIẾN TRÚC VÀ CÔNG NGHỆ
### 1. Công nghệ sử dụng chính:
- Backend:
  + Ngôn ngữ lập trình: Java 21, Groovy scripting language v4.0.20
  + Framework/thư viện: springframework v3.3.3
  + Kiến trúc hệ thống: Monolithic với các module tách biệt
- Frontend:
  + Ngôn ngữ lập trình: JavaScript, HTML/CSS
  + Framework/thư viện: Reactjs V18.3.1, Bootstrap V5.3

### 2. Cơ sở dữ liệu:
- Database
  + PostgresQL 16.1

- Mô tả kiến trúc và cấu trúc CSDL?
  + Không có thiết kế tổng thể, chỉ có 1 số quy định chung về cách đặt tên bảng, tên trường, các trường cơ bản bắt buộc bảng nào cũng phải có như created_by, created_time, modified_by, modified_time, company_id, storage_state... Các tên bảng cùng module có prefix như lgc_sale_xxx, lgc_price_xxxx.
  + Khi có yêu cầu cho từng module, team mới thiết kế sơ bộ.
  + Team dùng chiến lược adative strategy, vừa làm vừa rút kinh nghiệm và có phát triển các công cụ hỗ trợ cho phép thay đổi linh hoạt bằng các cách như thêm bảng, xoá bảng, tạo lại các liên kết hay migrate dữ liệu.

### 3. Phân quyền:
- Sử dụng hệ thống phân quyền của DataTP Core
- Các vai trò: Admin, Moderator, Write, Read
- Scope dữ liệu: All, Company, Group, Owner.
- Cơ chế đăng nhập: Thông qua DataTP Core, kế hoạch chuyển sang Keycloak SSO

### 4. Sao lưu dữ liệu:
- Hiện tại sử dụng cơ chế sao lưu chung của hệ thống DataTP Core
- Kế hoạch xây dựng cơ chế sao lưu riêng khi tách hệ thống

### 5. Hạ tầng tài nguyên hệ thống:
- Chạy trên nền tảng cloud chung với DataTP Core
- Kế hoạch tách thành hệ thống độc lập

## IV. VẬN HÀNH VÀ KHAI THÁC
- Phần mềm đang được phát triển tích cực với các phiên bản mới hàng tháng
- Có tài liệu hướng dẫn sử dụng cho người dùng trên trang web `https://docs.beelogistics.cloud/`
- Đang xây dựng hệ thống giám sát và cảnh báo riêng

## V. HƯỚNG PHÁT TRIỂN
- Kế hoạch 3 tháng tới:
  + Tách dự án thành hệ thống độc lập với database riêng
  + Tích hợp với Keycloak SSO
  + Hoàn thiện các màn hình và chức năng module sales
  + Cải thiện thông báo lỗi từ server đến người dùng

- Kế hoạch 6 tháng tới:
  + Tối ưu kiến trúc hệ thống
  + Phát triển thêm các tính năng marketing
  + Tích hợp đầy đủ với hệ thống OF1

- Cải tiến hệ thống:
  + Cập nhật UI/UX theo tiêu chuẩn mới
  + Tối ưu hiệu suất truy vấn cơ sở dữ liệu

## VI. CÁC THÔNG TIN KHÁC
### 1. Phỏng vấn lập trình viên tham gia phát triển:
- Thách thức trong việc xử lý dữ liệu từ nhiều nguồn và đồng bộ với hệ thống cũ.
- Khó khăn trong việc thiết kế UI/UX đáp ứng nhu cầu của nhiều nhóm người dùng khác nhau.
- Yêu cầu thay đổi thường xuyên từ người dùng.

### 2. Phỏng vấn người dùng:
- Phần mềm sử dụng thường xuyên, mỗi ngày.
- Phần mềm đáp ứng tốt nhu cầu quản lý giá và theo dõi quy trình bán hàng.
- Cần cải thiện tốc độ và giao diện người dùng trên thiết bị di động.
- Một số chức năng cần được đơn giản hóa quy trình.

----------------------------------------------------------------------------------------------------------

# Beesco

## I. THÔNG TIN CHUNG

### 1. Người được phỏng vấn: Nguyễn Tuấn Anh.

### 2. Thời gian triển khai

### 3. Nhân sự triển khai:

### 4. Các chức năng chính (mô tả chi tiết các chức năng chính của hệ thống)

### 5. Phạm vi triển khai:
- Hệ thống được triển khai ở đâu?
- Số lượng người sử dụng (theo thiết kế và theo thực tế hiện tại)?

## II. CHỨC NĂNG VÀ QUY TRINH NGHIỆP VỤ
- Danh sách đầy đủ các module, chức năng cho từng nhánh công việc
- Mô tả các workflow
- Liên kết với hệ thống CoreTP thế nào

## III. KIẾN TRÚC VÀ CÔNG NGHỆ
Công nghệ sử dụng chính:
- Backend:
    + Ngôn ngữ lập trình:
    + Framework/thư viện:
    + Kiến trúc hệ thống: (Monolithic, Microservices, SOA...?)
- Frontend:
    + Ngôn ngữ lập trình:
    + Framework/thư viện:
Cơ sở dữ liệu:
- (MySQL, PostgreSQL, MongoDB...?)
- Mô tả kiến trúc và cấu trúc CSDL?
Phân quyền:
- Các loại người dùng (quản trị, người sử dụng, nhân viên...)
- Các cơ chế phân quyền
- Cơ chế đăng nhập: (tài khoản độc lập, SSO, LDAP...?)
- Cơ chế xác thực: 2FA, OTP?
Sao lưu dữ liệu
- Cơ chế sao lưu, backup dữ liệu, khả năng
- Hình thức sao lưu (local, cloud...) sao lưu tự động
Hạ tầng tài nguyên hệ thống:
- Loại hạ tầng của hệ thống: Cloud
- Hệ điều hành:
- Hạ tầng mạng
    + Ảo hóa, mạng vật lý
    + Tốc độ, băng thông giữa các thiết bị
- Bảo mật hệ thống
    + Có các hình thức ngăn chặn truy cập trái phép (firewall, IPS/IDS, VPN, filter...)
    + Có phân hệ thống thành các vùng riêng biệt (VLAN, VPN, DMZ..?)
    + Chống tấn công DDoS?

## IV. VẬN HÀNH VÀ KHAI THÁC
- Phần mềm vẫn được duy trì phát triển và maintain thế nào
- Có tài liệu hướng dẫn sử dụng và vận hành chưa?
- Hệ thống giám sát, cảnh bảo hoạt động thế nào (tài nguyên hệ thống, lưu lượng, bảo mật, log...)

## V. HƯỚNG PHÁT TRIỂN
- Kế hoach 3 tháng tới là tách dự án, dùng db riêng giao diện độc lập. Có thể kết hợp với Keycloadk SSO để chạy độc lập hoàn toàn.
- Tích hợp các hệ thống khác?
- Tối ưu kiến trúc hệ thống hiện tại?
- Tối ưu các phần tử, các điểm nghẽn...?
- Loại bỏ hoặc viết lại 1 số code không đạt chuẩn.?

## VI. CÁC THÔNG TIN KHÁC
