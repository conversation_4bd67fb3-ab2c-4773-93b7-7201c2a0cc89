# Team Bfsone

Thành Viên:
- <PERSON><PERSON><PERSON>
- <PERSON><PERSON><PERSON> lý: <PERSON><PERSON><PERSON>.
Team Leader: <PERSON><PERSON><PERSON>

# Các Dự Án <PERSON>


# BfsOne

## I. THÔNG TIN CHUNG

### 1. Người được phỏng vấn: <PERSON><PERSON><PERSON>

### 2. <PERSON>h<PERSON>i gian triển khai

### 3. Nhân sự triển khai:

### 4. <PERSON><PERSON><PERSON> chức năng chính (mô tả chi tiết các chức năng chính của hệ thống)

### 5. Phạm vi triển khai:
- Hệ thống được triển khai ở đâu?
- Số lượng người sử dụng (theo thiết kế và theo thực tế hiện tại)?

## II. CHỨC NĂNG VÀ QUY TRINH NGHIỆP VỤ
- <PERSON><PERSON> sách đầy đủ các module, chứ<PERSON> năng cho từng nh<PERSON>h công việc
- <PERSON><PERSON> tả các workflow
- <PERSON><PERSON><PERSON> kết với hệ thống CoreTP thế nào

## III. KIẾN TRÚC VÀ CÔNG NGHỆ
Công nghệ sử dụng chính:
- Backend:
    + Ngôn ngữ lập trình:
    + Framework/thư viện:
    + Kiến trúc hệ thống: (Monolithic, Microservices, SOA...?)
- Frontend:
    + Ngôn ngữ lập trình:
    + Framework/thư viện:
Cơ sở dữ liệu:
- (MySQL, PostgreSQL, MongoDB...?)
- Mô tả kiến trúc và cấu trúc CSDL?
Phân quyền:
- Các loại người dùng (quản trị, người sử dụng, nhân viên...)
- Các cơ chế phân quyền
- Cơ chế đăng nhập: (tài khoản độc lập, SSO, LDAP...?)
- Cơ chế xác thực: 2FA, OTP?
Sao lưu dữ liệu
- Cơ chế sao lưu, backup dữ liệu, khả năng
- Hình thức sao lưu (local, cloud...) sao lưu tự động
Hạ tầng tài nguyên hệ thống:
- Loại hạ tầng của hệ thống: Cloud
- Hệ điều hành:
- Hạ tầng mạng
    + Ảo hóa, mạng vật lý
    + Tốc độ, băng thông giữa các thiết bị
- Bảo mật hệ thống
    + Có các hình thức ngăn chặn truy cập trái phép (firewall, IPS/IDS, VPN, filter...)
    + Có phân hệ thống thành các vùng riêng biệt (VLAN, VPN, DMZ..?)
    + Chống tấn công DDoS?

## IV. VẬN HÀNH VÀ KHAI THÁC
- Phần mềm vẫn được duy trì phát triển và maintain thế nào
- Có tài liệu hướng dẫn sử dụng và vận hành chưa?
- Hệ thống giám sát, cảnh bảo hoạt động thế nào (tài nguyên hệ thống, lưu lượng, bảo mật, log...)

## V. HƯỚNG PHÁT TRIỂN
- Kế hoach 3 tháng tới là tách dự án, dùng db riêng giao diện độc lập. Có thể kết hợp với Keycloadk SSO để chạy độc lập hoàn toàn.
- Tích hợp các hệ thống khác?
- Tối ưu kiến trúc hệ thống hiện tại?
- Tối ưu các phần tử, các điểm nghẽn...?
- Loại bỏ hoặc viết lại 1 số code không đạt chuẩn.?

## VI. CÁC THÔNG TIN KHÁC
### 1. Phỏng vấn lập trình viên tham gia phát triển
- Trong quá trình triển khai, bạn gặp phải những vấn đề gì rủi ro cho thành công của dự án

### 2. Phỏng vấn người dùng sử dụng, có đáp ứng được nhu cầu...
- Đã sử dụng phần mềm với tàn xuất thế nào?
- Phần mềm có phù hợp với thực tế không?
- Phần mềm có những vấn đề gì cần hỗ trợ